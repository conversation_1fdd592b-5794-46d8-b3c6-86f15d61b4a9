"""
Production Monitoring Dashboard with 2025 Best Practices
Real-time monitoring with comprehensive metrics, alerting, and observability.
"""

import asyncio
import json
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union
import threading
from concurrent.futures import ThreadPoolExecutor

import structlog
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
import uvicorn

from ..core.config import config
from ..core.logging_config import get_logger, CorrelationContext


logger = get_logger(__name__)


class MetricType(Enum):
    """Types of metrics collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Metric:
    """Individual metric data point."""
    name: str
    value: Union[int, float]
    metric_type: MetricType
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metric to dictionary for JSON serialization."""
        return {
            "name": self.name,
            "value": self.value,
            "type": self.metric_type.value,
            "timestamp": self.timestamp.isoformat(),
            "labels": self.labels
        }


@dataclass
class Alert:
    """System alert."""
    id: str
    message: str
    severity: AlertSeverity
    timestamp: datetime
    metric_name: str
    current_value: Union[int, float]
    threshold: Union[int, float]
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "message": self.message,
            "severity": self.severity.value,
            "timestamp": self.timestamp.isoformat(),
            "metric_name": self.metric_name,
            "current_value": self.current_value,
            "threshold": self.threshold,
            "resolved": self.resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None
        }


class MetricsCollector:
    """High-performance metrics collection with thread safety."""
    
    def __init__(self, max_history: int = 10000):
        self.max_history = max_history
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = defaultdict(float)
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self.timers: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._lock = threading.RLock()
        
        logger.info("MetricsCollector initialized", max_history=max_history)
    
    def increment_counter(self, name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """Increment a counter metric."""
        with self._lock:
            key = self._make_key(name, labels)
            self.counters[key] += value
            
            metric = Metric(
                name=name,
                value=self.counters[key],
                metric_type=MetricType.COUNTER,
                timestamp=datetime.now(timezone.utc),
                labels=labels or {}
            )
            self.metrics[key].append(metric)
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Set a gauge metric value."""
        with self._lock:
            key = self._make_key(name, labels)
            self.gauges[key] = value
            
            metric = Metric(
                name=name,
                value=value,
                metric_type=MetricType.GAUGE,
                timestamp=datetime.now(timezone.utc),
                labels=labels or {}
            )
            self.metrics[key].append(metric)
    
    def record_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Record a histogram value."""
        with self._lock:
            key = self._make_key(name, labels)
            self.histograms[key].append(value)
            
            # Keep only recent values for performance
            if len(self.histograms[key]) > 1000:
                self.histograms[key] = self.histograms[key][-1000:]
            
            metric = Metric(
                name=name,
                value=value,
                metric_type=MetricType.HISTOGRAM,
                timestamp=datetime.now(timezone.utc),
                labels=labels or {}
            )
            self.metrics[key].append(metric)
    
    def record_timer(self, name: str, duration: float, labels: Optional[Dict[str, str]] = None):
        """Record a timer duration."""
        with self._lock:
            key = self._make_key(name, labels)
            self.timers[key].append(duration)
            
            metric = Metric(
                name=name,
                value=duration,
                metric_type=MetricType.TIMER,
                timestamp=datetime.now(timezone.utc),
                labels=labels or {}
            )
            self.metrics[key].append(metric)
    
    def get_metrics(self, name_pattern: Optional[str] = None, 
                   since: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get metrics matching pattern and time range."""
        with self._lock:
            results = []
            
            for key, metric_deque in self.metrics.items():
                if name_pattern and name_pattern not in key:
                    continue
                
                for metric in metric_deque:
                    if since and metric.timestamp < since:
                        continue
                    results.append(metric.to_dict())
            
            return sorted(results, key=lambda x: x["timestamp"])
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics for all metrics."""
        with self._lock:
            stats = {
                "counters": dict(self.counters),
                "gauges": dict(self.gauges),
                "histograms": {},
                "timers": {},
                "total_metrics": sum(len(deque) for deque in self.metrics.values()),
                "collection_time": datetime.now(timezone.utc).isoformat()
            }
            
            # Calculate histogram statistics
            for key, values in self.histograms.items():
                if values:
                    stats["histograms"][key] = {
                        "count": len(values),
                        "min": min(values),
                        "max": max(values),
                        "avg": sum(values) / len(values),
                        "p50": self._percentile(values, 50),
                        "p95": self._percentile(values, 95),
                        "p99": self._percentile(values, 99)
                    }
            
            # Calculate timer statistics
            for key, values in self.timers.items():
                if values:
                    stats["timers"][key] = {
                        "count": len(values),
                        "min": min(values),
                        "max": max(values),
                        "avg": sum(values) / len(values),
                        "p50": self._percentile(values, 50),
                        "p95": self._percentile(values, 95),
                        "p99": self._percentile(values, 99)
                    }
            
            return stats
    
    def _make_key(self, name: str, labels: Optional[Dict[str, str]]) -> str:
        """Create a unique key for metric with labels."""
        if not labels:
            return name
        
        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile of values."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int((percentile / 100.0) * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]


class AlertManager:
    """Intelligent alerting system with threshold management."""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.alerts: Dict[str, Alert] = {}
        self.thresholds: Dict[str, Dict[str, Union[int, float]]] = {}
        self.alert_history: deque = deque(maxlen=1000)
        self._lock = threading.RLock()
        
        # Default thresholds
        self._setup_default_thresholds()
        
        logger.info("AlertManager initialized")
    
    def _setup_default_thresholds(self):
        """Setup default alert thresholds."""
        self.thresholds.update({
            "api_response_time": {"warning": 5.0, "critical": 30.0},
            "error_rate": {"warning": 0.05, "critical": 0.10},
            "memory_usage": {"warning": 0.80, "critical": 0.95},
            "cpu_usage": {"warning": 0.80, "critical": 0.95},
            "active_connections": {"warning": 1000, "critical": 2000},
            "queue_depth": {"warning": 100, "critical": 500},
            "cache_hit_rate": {"warning": 0.70, "critical": 0.50},  # Lower is worse
        })
    
    def check_thresholds(self):
        """Check all metrics against thresholds and generate alerts."""
        with self._lock:
            current_time = datetime.now(timezone.utc)
            stats = self.metrics_collector.get_summary_stats()
            
            # Check gauge metrics
            for metric_name, value in stats["gauges"].items():
                self._check_metric_threshold(metric_name, value, current_time)
            
            # Check timer averages
            for metric_name, timer_stats in stats["timers"].items():
                avg_value = timer_stats.get("avg", 0)
                self._check_metric_threshold(metric_name, avg_value, current_time)
    
    def _check_metric_threshold(self, metric_name: str, value: float, timestamp: datetime):
        """Check individual metric against thresholds."""
        base_name = metric_name.split("{")[0]  # Remove labels for threshold lookup
        
        if base_name not in self.thresholds:
            return
        
        thresholds = self.thresholds[base_name]
        alert_id = f"{metric_name}_threshold"
        
        # Determine severity
        severity = None
        threshold_value = None
        
        if "critical" in thresholds and value >= thresholds["critical"]:
            severity = AlertSeverity.CRITICAL
            threshold_value = thresholds["critical"]
        elif "warning" in thresholds and value >= thresholds["warning"]:
            severity = AlertSeverity.WARNING
            threshold_value = thresholds["warning"]
        
        # Handle special case for cache hit rate (lower is worse)
        if base_name == "cache_hit_rate":
            if "critical" in thresholds and value <= thresholds["critical"]:
                severity = AlertSeverity.CRITICAL
                threshold_value = thresholds["critical"]
            elif "warning" in thresholds and value <= thresholds["warning"]:
                severity = AlertSeverity.WARNING
                threshold_value = thresholds["warning"]
        
        if severity:
            # Create or update alert
            if alert_id not in self.alerts or self.alerts[alert_id].resolved:
                alert = Alert(
                    id=alert_id,
                    message=f"{metric_name} threshold exceeded: {value:.3f} >= {threshold_value}",
                    severity=severity,
                    timestamp=timestamp,
                    metric_name=metric_name,
                    current_value=value,
                    threshold=threshold_value
                )
                
                self.alerts[alert_id] = alert
                self.alert_history.append(alert)
                
                logger.warning(
                    "Alert triggered",
                    alert_id=alert_id,
                    metric_name=metric_name,
                    current_value=value,
                    threshold=threshold_value,
                    severity=severity.value
                )
        else:
            # Resolve existing alert if value is back to normal
            if alert_id in self.alerts and not self.alerts[alert_id].resolved:
                self.alerts[alert_id].resolved = True
                self.alerts[alert_id].resolved_at = timestamp
                
                logger.info(
                    "Alert resolved",
                    alert_id=alert_id,
                    metric_name=metric_name,
                    current_value=value
                )
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active (unresolved) alerts."""
        with self._lock:
            return [alert.to_dict() for alert in self.alerts.values() if not alert.resolved]
    
    def get_alert_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent alert history."""
        with self._lock:
            recent_alerts = list(self.alert_history)[-limit:]
            return [alert.to_dict() for alert in recent_alerts]


# Global instances
metrics_collector = MetricsCollector()
alert_manager = AlertManager(metrics_collector)


class MonitoringDashboard:
    """Real-time monitoring dashboard with WebSocket support."""
    
    def __init__(self):
        self.app = FastAPI(title="Token Analyzer Monitoring Dashboard")
        self.active_connections: List[WebSocket] = []
        self.background_tasks = set()
        self._background_started = False

        self._setup_routes()

        logger.info("MonitoringDashboard initialized")
    
    def _setup_routes(self):
        """Setup FastAPI routes."""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard():
            return self._get_dashboard_html()
        
        @self.app.get("/api/metrics")
        async def get_metrics():
            return metrics_collector.get_summary_stats()
        
        @self.app.get("/api/alerts")
        async def get_alerts():
            return {
                "active": alert_manager.get_active_alerts(),
                "history": alert_manager.get_alert_history()
            }
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await self._handle_websocket(websocket)
    
    async def _handle_websocket(self, websocket: WebSocket):
        """Handle WebSocket connections for real-time updates."""
        await websocket.accept()
        self.active_connections.append(websocket)

        # Start background tasks if not already started
        if not self._background_started:
            await self._start_background_tasks()

        try:
            while True:
                # Send periodic updates
                data = {
                    "metrics": metrics_collector.get_summary_stats(),
                    "alerts": alert_manager.get_active_alerts(),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }

                await websocket.send_json(data)
                await asyncio.sleep(5)  # Update every 5 seconds

        except WebSocketDisconnect:
            self.active_connections.remove(websocket)
            logger.info("WebSocket client disconnected")
    
    async def _start_background_tasks(self):
        """Start background monitoring tasks."""
        if self._background_started:
            return

        async def alert_checker():
            """Periodic alert checking."""
            while True:
                try:
                    alert_manager.check_thresholds()
                    await asyncio.sleep(10)  # Check every 10 seconds
                except Exception as e:
                    logger.exception("Alert checker error", error=str(e))
                    await asyncio.sleep(30)  # Back off on error

        # Start background task
        task = asyncio.create_task(alert_checker())
        self.background_tasks.add(task)
        task.add_done_callback(self.background_tasks.discard)
        self._background_started = True
    
    def _get_dashboard_html(self) -> str:
        """Generate dashboard HTML."""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Token Analyzer Monitoring Dashboard</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; }
                .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .metric { display: inline-block; margin: 10px; padding: 15px; background: #e3f2fd; border-radius: 4px; }
                .alert { padding: 10px; margin: 5px 0; border-radius: 4px; }
                .alert.critical { background: #ffebee; border-left: 4px solid #f44336; }
                .alert.warning { background: #fff3e0; border-left: 4px solid #ff9800; }
                .alert.info { background: #e8f5e8; border-left: 4px solid #4caf50; }
                .status { font-weight: bold; }
                .status.healthy { color: #4caf50; }
                .status.warning { color: #ff9800; }
                .status.critical { color: #f44336; }
                #metrics, #alerts { margin-top: 20px; }
                .timestamp { color: #666; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Token Analyzer Monitoring Dashboard</h1>
                <div class="card">
                    <h2>System Status: <span id="system-status" class="status">Connecting...</span></h2>
                    <p class="timestamp">Last Update: <span id="last-update">-</span></p>
                </div>
                
                <div class="card">
                    <h2>📊 Key Metrics</h2>
                    <div id="metrics">Loading metrics...</div>
                </div>
                
                <div class="card">
                    <h2>🚨 Active Alerts</h2>
                    <div id="alerts">Loading alerts...</div>
                </div>
            </div>
            
            <script>
                const ws = new WebSocket('ws://localhost:8001/ws');
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    updateDashboard(data);
                };
                
                function updateDashboard(data) {
                    // Update timestamp
                    document.getElementById('last-update').textContent = new Date(data.timestamp).toLocaleString();
                    
                    // Update system status
                    const statusEl = document.getElementById('system-status');
                    if (data.alerts.length === 0) {
                        statusEl.textContent = 'Healthy';
                        statusEl.className = 'status healthy';
                    } else {
                        const hasCritical = data.alerts.some(a => a.severity === 'critical');
                        if (hasCritical) {
                            statusEl.textContent = 'Critical Issues';
                            statusEl.className = 'status critical';
                        } else {
                            statusEl.textContent = 'Warnings';
                            statusEl.className = 'status warning';
                        }
                    }
                    
                    // Update metrics
                    const metricsEl = document.getElementById('metrics');
                    let metricsHtml = '';
                    
                    // Show key gauges
                    for (const [name, value] of Object.entries(data.metrics.gauges)) {
                        metricsHtml += `<div class="metric"><strong>${name}:</strong> ${value.toFixed(3)}</div>`;
                    }
                    
                    // Show timer averages
                    for (const [name, stats] of Object.entries(data.metrics.timers)) {
                        metricsHtml += `<div class="metric"><strong>${name} (avg):</strong> ${stats.avg.toFixed(3)}ms</div>`;
                    }
                    
                    metricsEl.innerHTML = metricsHtml || 'No metrics available';
                    
                    // Update alerts
                    const alertsEl = document.getElementById('alerts');
                    if (data.alerts.length === 0) {
                        alertsEl.innerHTML = '<p style="color: #4caf50;">✅ No active alerts</p>';
                    } else {
                        let alertsHtml = '';
                        data.alerts.forEach(alert => {
                            alertsHtml += `
                                <div class="alert ${alert.severity}">
                                    <strong>${alert.severity.toUpperCase()}:</strong> ${alert.message}
                                    <br><small>Triggered: ${new Date(alert.timestamp).toLocaleString()}</small>
                                </div>
                            `;
                        });
                        alertsEl.innerHTML = alertsHtml;
                    }
                }
                
                ws.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    document.getElementById('system-status').textContent = 'Connection Error';
                    document.getElementById('system-status').className = 'status critical';
                };
            </script>
        </body>
        </html>
        """
    
    def run(self, host: str = "0.0.0.0", port: int = 8001):
        """Run the monitoring dashboard."""
        logger.info("Starting monitoring dashboard", host=host, port=port)
        uvicorn.run(self.app, host=host, port=port, log_level="info")


# Convenience functions for easy metric collection
def increment_counter(name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None):
    """Increment a counter metric."""
    metrics_collector.increment_counter(name, value, labels)


def set_gauge(name: str, value: float, labels: Optional[Dict[str, str]] = None):
    """Set a gauge metric value."""
    metrics_collector.set_gauge(name, value, labels)


def record_histogram(name: str, value: float, labels: Optional[Dict[str, str]] = None):
    """Record a histogram value."""
    metrics_collector.record_histogram(name, value, labels)


def record_timer(name: str, duration: float, labels: Optional[Dict[str, str]] = None):
    """Record a timer duration."""
    metrics_collector.record_timer(name, duration, labels)


# Context manager for timing operations
class timer_context:
    """Context manager for timing operations."""
    
    def __init__(self, name: str, labels: Optional[Dict[str, str]] = None):
        self.name = name
        self.labels = labels
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (time.time() - self.start_time) * 1000  # Convert to milliseconds
            record_timer(self.name, duration, self.labels)


# Initialize dashboard instance (lazy initialization to avoid event loop issues)
dashboard = None

def get_dashboard():
    """Get or create dashboard instance."""
    global dashboard
    if dashboard is None:
        dashboard = MonitoringDashboard()
    return dashboard
