"""
Advanced Performance Metrics Collection System

This module implements comprehensive performance telemetry including API response times,
cache hit rates, and resource utilization monitoring. It provides detailed performance
insights following 2025 observability best practices.

Features:
- Comprehensive API response time tracking with percentiles
- Cache performance monitoring with hit/miss ratios
- Resource utilization monitoring (CPU, memory, disk, network)
- Database query performance tracking
- Custom business metrics collection
- Performance anomaly detection
- Automated performance optimization recommendations
- Real-time performance dashboards
"""

import asyncio
import json
import logging
import psutil
import statistics
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
import threading
import functools
import inspect

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from ..core.config import config
from ..core.logging_config import get_logger
from ..compliance.audit_framework import AuditTrail, AuditEventType, RiskLevel
from .dashboard import metrics_collector, alert_manager, AlertSeverity

logger = get_logger(__name__)


class PerformanceMetricType(Enum):
    """Types of performance metrics."""
    API_RESPONSE_TIME = "api_response_time"
    CACHE_HIT_RATE = "cache_hit_rate"
    DATABASE_QUERY_TIME = "database_query_time"
    CPU_UTILIZATION = "cpu_utilization"
    MEMORY_UTILIZATION = "memory_utilization"
    DISK_UTILIZATION = "disk_utilization"
    NETWORK_THROUGHPUT = "network_throughput"
    CUSTOM_BUSINESS_METRIC = "custom_business_metric"


class PerformanceAlertType(Enum):
    """Types of performance alerts."""
    HIGH_RESPONSE_TIME = "high_response_time"
    LOW_CACHE_HIT_RATE = "low_cache_hit_rate"
    HIGH_RESOURCE_USAGE = "high_resource_usage"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    ANOMALY_DETECTED = "anomaly_detected"


@dataclass
class PerformanceDataPoint:
    """Single performance measurement."""
    timestamp: datetime
    metric_type: PerformanceMetricType
    value: float
    endpoint: Optional[str] = None
    operation: Optional[str] = None
    labels: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class APIPerformanceMetrics:
    """API endpoint performance metrics."""
    endpoint: str
    method: str
    total_requests: int = 0
    total_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    response_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    status_codes: Dict[int, int] = field(default_factory=dict)
    error_count: int = 0
    last_request: Optional[datetime] = None


@dataclass
class CachePerformanceMetrics:
    """Cache performance metrics."""
    cache_name: str
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    total_size: int = 0
    evictions: int = 0
    avg_lookup_time: float = 0.0
    last_updated: Optional[datetime] = None


@dataclass
class ResourceUtilizationMetrics:
    """System resource utilization metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_bytes_sent: int
    network_bytes_recv: int
    active_connections: int
    process_count: int


class AdvancedPerformanceTelemetry:
    """
    Advanced performance metrics collection system.
    
    Provides comprehensive performance monitoring with detailed telemetry,
    automated analysis, and intelligent alerting capabilities.
    """
    
    def __init__(self, audit_trail: Optional[AuditTrail] = None):
        self.audit_trail = audit_trail
        self.performance_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.api_metrics: Dict[str, APIPerformanceMetrics] = {}
        self.cache_metrics: Dict[str, CachePerformanceMetrics] = {}
        self.resource_history: deque = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        self.custom_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._lock = threading.RLock()
        
        # Performance thresholds (2025 best practices)
        self.performance_thresholds = {
            "api_response_time_warning": 1000,    # 1 second
            "api_response_time_critical": 5000,   # 5 seconds
            "cache_hit_rate_warning": 0.7,        # 70%
            "cache_hit_rate_critical": 0.5,       # 50%
            "cpu_usage_warning": 70,              # 70%
            "cpu_usage_critical": 90,             # 90%
            "memory_usage_warning": 80,           # 80%
            "memory_usage_critical": 95,          # 95%
            "disk_usage_warning": 80,             # 80%
            "disk_usage_critical": 95             # 95%
        }
        
        # Monitoring configuration
        self.collection_interval_seconds = 60  # Collect system metrics every minute
        self.anomaly_detection_window = 100    # Look at last 100 data points for anomalies
        self.performance_baseline_hours = 24   # Use 24 hours for baseline calculation
        
        # Performance optimization recommendations
        self.optimization_recommendations: List[str] = []
        
        # Statistics
        self.stats = {
            "total_api_requests": 0,
            "total_cache_requests": 0,
            "performance_alerts_triggered": 0,
            "last_collection": None
        }
        
        logger.info("AdvancedPerformanceTelemetry initialized")
    
    def record_api_performance(self, endpoint: str, method: str, response_time_ms: float,
                             status_code: int, request_size: int = 0, response_size: int = 0):
        """Record API endpoint performance metrics."""
        try:
            current_time = datetime.now(timezone.utc)
            
            with self._lock:
                # Update statistics
                self.stats["total_api_requests"] += 1
                
                # Get or create API metrics
                key = f"{method}:{endpoint}"
                if key not in self.api_metrics:
                    self.api_metrics[key] = APIPerformanceMetrics(
                        endpoint=endpoint,
                        method=method
                    )
                
                api_metrics = self.api_metrics[key]
                
                # Update metrics
                api_metrics.total_requests += 1
                api_metrics.total_response_time += response_time_ms
                api_metrics.min_response_time = min(api_metrics.min_response_time, response_time_ms)
                api_metrics.max_response_time = max(api_metrics.max_response_time, response_time_ms)
                api_metrics.response_times.append(response_time_ms)
                api_metrics.last_request = current_time
                
                # Update status code counts
                if status_code not in api_metrics.status_codes:
                    api_metrics.status_codes[status_code] = 0
                api_metrics.status_codes[status_code] += 1
                
                # Count errors (4xx and 5xx)
                if status_code >= 400:
                    api_metrics.error_count += 1
                
                # Record performance data point
                data_point = PerformanceDataPoint(
                    timestamp=current_time,
                    metric_type=PerformanceMetricType.API_RESPONSE_TIME,
                    value=response_time_ms,
                    endpoint=endpoint,
                    labels={
                        "method": method,
                        "status_code": str(status_code)
                    },
                    metadata={
                        "request_size": request_size,
                        "response_size": response_size
                    }
                )
                
                self.performance_data[f"api:{key}"].append(data_point)
            
            # Update metrics collector
            metrics_collector.record_timer(f"api_response_time", response_time_ms, {
                "endpoint": endpoint,
                "method": method,
                "status_code": str(status_code)
            })
            
            metrics_collector.increment_counter("api_requests_total", 1, {
                "endpoint": endpoint,
                "method": method,
                "status_code": str(status_code)
            })
            
            # Check for performance alerts
            asyncio.create_task(self._check_api_performance_alerts(endpoint, method, response_time_ms))
            
        except Exception as e:
            logger.error(f"Failed to record API performance: {e}")
    
    def record_cache_performance(self, cache_name: str, operation: str, hit: bool,
                               lookup_time_ms: float = 0.0, cache_size: int = 0):
        """Record cache performance metrics."""
        try:
            current_time = datetime.now(timezone.utc)
            
            with self._lock:
                # Update statistics
                self.stats["total_cache_requests"] += 1
                
                # Get or create cache metrics
                if cache_name not in self.cache_metrics:
                    self.cache_metrics[cache_name] = CachePerformanceMetrics(
                        cache_name=cache_name
                    )
                
                cache_metrics = self.cache_metrics[cache_name]
                
                # Update metrics
                cache_metrics.total_requests += 1
                if hit:
                    cache_metrics.cache_hits += 1
                else:
                    cache_metrics.cache_misses += 1
                
                cache_metrics.total_size = cache_size
                cache_metrics.last_updated = current_time
                
                # Update average lookup time
                if cache_metrics.total_requests == 1:
                    cache_metrics.avg_lookup_time = lookup_time_ms
                else:
                    cache_metrics.avg_lookup_time = (
                        cache_metrics.avg_lookup_time * 0.9 + lookup_time_ms * 0.1
                    )
                
                # Calculate hit rate
                hit_rate = cache_metrics.cache_hits / cache_metrics.total_requests
                
                # Record performance data point
                data_point = PerformanceDataPoint(
                    timestamp=current_time,
                    metric_type=PerformanceMetricType.CACHE_HIT_RATE,
                    value=hit_rate,
                    operation=operation,
                    labels={
                        "cache_name": cache_name,
                        "operation": operation,
                        "hit": str(hit)
                    },
                    metadata={
                        "lookup_time_ms": lookup_time_ms,
                        "cache_size": cache_size
                    }
                )
                
                self.performance_data[f"cache:{cache_name}"].append(data_point)
            
            # Update metrics collector
            metrics_collector.set_gauge(f"cache_hit_rate_{cache_name}", hit_rate)
            metrics_collector.record_timer(f"cache_lookup_time_{cache_name}", lookup_time_ms)
            metrics_collector.increment_counter("cache_operations_total", 1, {
                "cache_name": cache_name,
                "operation": operation,
                "hit": str(hit)
            })
            
            # Check for cache performance alerts
            asyncio.create_task(self._check_cache_performance_alerts(cache_name, hit_rate))
            
        except Exception as e:
            logger.error(f"Failed to record cache performance: {e}")
    
    async def collect_resource_utilization(self):
        """Collect system resource utilization metrics."""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Collect system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network metrics
            try:
                network = psutil.net_io_counters()
                network_sent = network.bytes_sent
                network_recv = network.bytes_recv
            except:
                network_sent = network_recv = 0
            
            # Process metrics
            try:
                active_connections = len(psutil.net_connections())
                process_count = len(psutil.pids())
            except:
                active_connections = process_count = 0
            
            # Create resource metrics
            resource_metrics = ResourceUtilizationMetrics(
                timestamp=current_time,
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk.percent,
                network_bytes_sent=network_sent,
                network_bytes_recv=network_recv,
                active_connections=active_connections,
                process_count=process_count
            )
            
            with self._lock:
                self.resource_history.append(resource_metrics)
                self.stats["last_collection"] = current_time
            
            # Record individual metrics
            resource_data = [
                (PerformanceMetricType.CPU_UTILIZATION, cpu_percent),
                (PerformanceMetricType.MEMORY_UTILIZATION, memory.percent),
                (PerformanceMetricType.DISK_UTILIZATION, disk.percent),
                (PerformanceMetricType.NETWORK_THROUGHPUT, network_sent + network_recv)
            ]
            
            for metric_type, value in resource_data:
                data_point = PerformanceDataPoint(
                    timestamp=current_time,
                    metric_type=metric_type,
                    value=value,
                    metadata={
                        "active_connections": active_connections,
                        "process_count": process_count
                    }
                )
                
                self.performance_data[f"resource:{metric_type.value}"].append(data_point)
            
            # Update metrics collector
            metrics_collector.set_gauge("system_cpu_percent", cpu_percent)
            metrics_collector.set_gauge("system_memory_percent", memory.percent)
            metrics_collector.set_gauge("system_disk_percent", disk.percent)
            metrics_collector.set_gauge("system_active_connections", active_connections)
            
            # Check for resource alerts
            await self._check_resource_alerts(resource_metrics)
            
        except Exception as e:
            logger.error(f"Failed to collect resource utilization: {e}")

    async def _check_api_performance_alerts(self, endpoint: str, method: str, response_time_ms: float):
        """Check API performance against thresholds and generate alerts."""
        try:
            alert_id = f"api_performance_{method}_{endpoint.replace('/', '_')}"

            # Determine alert severity
            severity = None
            threshold_value = 0.0

            if response_time_ms > self.performance_thresholds["api_response_time_critical"]:
                severity = AlertSeverity.CRITICAL
                threshold_value = self.performance_thresholds["api_response_time_critical"]
            elif response_time_ms > self.performance_thresholds["api_response_time_warning"]:
                severity = AlertSeverity.WARNING
                threshold_value = self.performance_thresholds["api_response_time_warning"]

            if severity:
                # Check if alert already exists and is recent
                if alert_id in alert_manager.alerts:
                    last_alert = alert_manager.alerts[alert_id]
                    if not last_alert.get("resolved", True):
                        time_since_alert = (datetime.now(timezone.utc) - last_alert["timestamp"]).total_seconds() / 60
                        if time_since_alert < 5:  # 5-minute cooldown
                            return

                # Create alert
                message = f"High response time for {method} {endpoint}: {response_time_ms:.1f}ms (threshold: {threshold_value}ms)"

                alert_manager.alerts[alert_id] = {
                    "id": alert_id,
                    "message": message,
                    "severity": severity,
                    "timestamp": datetime.now(timezone.utc),
                    "metric_name": "api_response_time",
                    "current_value": response_time_ms,
                    "threshold": threshold_value,
                    "endpoint": endpoint,
                    "method": method,
                    "resolved": False
                }

                with self._lock:
                    self.stats["performance_alerts_triggered"] += 1

                logger.warning("API performance alert triggered",
                             endpoint=endpoint,
                             method=method,
                             response_time=response_time_ms,
                             threshold=threshold_value,
                             severity=severity.value)

                # Audit trail
                if self.audit_trail:
                    self.audit_trail.log_event(
                        AuditEventType.SYSTEM_ACCESS,
                        None,
                        "performance_telemetry",
                        "api_performance_alert",
                        {
                            "endpoint": endpoint,
                            "method": method,
                            "response_time_ms": response_time_ms,
                            "threshold": threshold_value,
                            "severity": severity.value
                        },
                        risk_level=RiskLevel.HIGH if severity == AlertSeverity.CRITICAL else RiskLevel.MEDIUM
                    )

        except Exception as e:
            logger.error(f"Failed to check API performance alerts: {e}")

    async def _check_cache_performance_alerts(self, cache_name: str, hit_rate: float):
        """Check cache performance against thresholds and generate alerts."""
        try:
            alert_id = f"cache_performance_{cache_name}"

            # Determine alert severity (low hit rate is bad)
            severity = None
            threshold_value = 0.0

            if hit_rate < self.performance_thresholds["cache_hit_rate_critical"]:
                severity = AlertSeverity.CRITICAL
                threshold_value = self.performance_thresholds["cache_hit_rate_critical"]
            elif hit_rate < self.performance_thresholds["cache_hit_rate_warning"]:
                severity = AlertSeverity.WARNING
                threshold_value = self.performance_thresholds["cache_hit_rate_warning"]

            if severity:
                # Check if alert already exists and is recent
                if alert_id in alert_manager.alerts:
                    last_alert = alert_manager.alerts[alert_id]
                    if not last_alert.get("resolved", True):
                        time_since_alert = (datetime.now(timezone.utc) - last_alert["timestamp"]).total_seconds() / 60
                        if time_since_alert < 10:  # 10-minute cooldown for cache alerts
                            return

                # Create alert
                message = f"Low cache hit rate for {cache_name}: {hit_rate:.1%} (threshold: {threshold_value:.1%})"

                alert_manager.alerts[alert_id] = {
                    "id": alert_id,
                    "message": message,
                    "severity": severity,
                    "timestamp": datetime.now(timezone.utc),
                    "metric_name": "cache_hit_rate",
                    "current_value": hit_rate,
                    "threshold": threshold_value,
                    "cache_name": cache_name,
                    "resolved": False
                }

                logger.warning("Cache performance alert triggered",
                             cache_name=cache_name,
                             hit_rate=hit_rate,
                             threshold=threshold_value,
                             severity=severity.value)

        except Exception as e:
            logger.error(f"Failed to check cache performance alerts: {e}")

    async def _check_resource_alerts(self, resource_metrics: ResourceUtilizationMetrics):
        """Check resource utilization against thresholds and generate alerts."""
        try:
            current_time = datetime.now(timezone.utc)

            # Check CPU usage
            await self._check_resource_threshold(
                "cpu_usage", resource_metrics.cpu_percent, current_time,
                self.performance_thresholds["cpu_usage_warning"],
                self.performance_thresholds["cpu_usage_critical"]
            )

            # Check memory usage
            await self._check_resource_threshold(
                "memory_usage", resource_metrics.memory_percent, current_time,
                self.performance_thresholds["memory_usage_warning"],
                self.performance_thresholds["memory_usage_critical"]
            )

            # Check disk usage
            await self._check_resource_threshold(
                "disk_usage", resource_metrics.disk_percent, current_time,
                self.performance_thresholds["disk_usage_warning"],
                self.performance_thresholds["disk_usage_critical"]
            )

        except Exception as e:
            logger.error(f"Failed to check resource alerts: {e}")

    async def _check_resource_threshold(self, resource_name: str, value: float, timestamp: datetime,
                                      warning_threshold: float, critical_threshold: float):
        """Check individual resource threshold and generate alert if needed."""
        try:
            alert_id = f"resource_{resource_name}"

            # Determine alert severity
            severity = None
            threshold_value = 0.0

            if value > critical_threshold:
                severity = AlertSeverity.CRITICAL
                threshold_value = critical_threshold
            elif value > warning_threshold:
                severity = AlertSeverity.WARNING
                threshold_value = warning_threshold

            if severity:
                # Check if alert already exists and is recent
                if alert_id in alert_manager.alerts:
                    last_alert = alert_manager.alerts[alert_id]
                    if not last_alert.get("resolved", True):
                        time_since_alert = (timestamp - last_alert["timestamp"]).total_seconds() / 60
                        if time_since_alert < 15:  # 15-minute cooldown for resource alerts
                            return

                # Create alert
                message = f"High {resource_name.replace('_', ' ')}: {value:.1f}% (threshold: {threshold_value:.1f}%)"

                alert_manager.alerts[alert_id] = {
                    "id": alert_id,
                    "message": message,
                    "severity": severity,
                    "timestamp": timestamp,
                    "metric_name": resource_name,
                    "current_value": value,
                    "threshold": threshold_value,
                    "resolved": False
                }

                logger.warning("Resource utilization alert triggered",
                             resource=resource_name,
                             value=value,
                             threshold=threshold_value,
                             severity=severity.value)
            else:
                # Resolve existing alert if value is back to normal
                if alert_id in alert_manager.alerts and not alert_manager.alerts[alert_id].get("resolved", True):
                    alert_manager.alerts[alert_id]["resolved"] = True
                    alert_manager.alerts[alert_id]["resolved_at"] = timestamp

                    logger.info("Resource utilization alert resolved",
                               resource=resource_name,
                               value=value)

        except Exception as e:
            logger.error(f"Failed to check resource threshold: {e}")

    def record_custom_metric(self, metric_name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Record custom business metric."""
        try:
            current_time = datetime.now(timezone.utc)

            data_point = PerformanceDataPoint(
                timestamp=current_time,
                metric_type=PerformanceMetricType.CUSTOM_BUSINESS_METRIC,
                value=value,
                operation=metric_name,
                labels=labels or {}
            )

            with self._lock:
                self.custom_metrics[metric_name].append(data_point)

            # Update metrics collector
            metrics_collector.set_gauge(f"custom_{metric_name}", value, labels or {})

        except Exception as e:
            logger.error(f"Failed to record custom metric: {e}")

    def analyze_performance_trends(self, hours: int = 24) -> Dict[str, Any]:
        """Analyze performance trends over specified time period."""
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            trends = {}

            # Analyze API performance trends
            api_trends = {}
            for endpoint_key, api_metrics in self.api_metrics.items():
                if api_metrics.last_request and api_metrics.last_request >= cutoff_time:
                    recent_times = [t for t in api_metrics.response_times if t is not None]
                    if len(recent_times) >= 10:
                        avg_time = statistics.mean(recent_times)
                        p95_time = self._percentile(recent_times, 95)
                        p99_time = self._percentile(recent_times, 99)

                        api_trends[endpoint_key] = {
                            "avg_response_time": avg_time,
                            "p95_response_time": p95_time,
                            "p99_response_time": p99_time,
                            "total_requests": api_metrics.total_requests,
                            "error_rate": api_metrics.error_count / api_metrics.total_requests if api_metrics.total_requests > 0 else 0
                        }

            trends["api_performance"] = api_trends

            # Analyze cache performance trends
            cache_trends = {}
            for cache_name, cache_metrics in self.cache_metrics.items():
                if cache_metrics.last_updated and cache_metrics.last_updated >= cutoff_time:
                    hit_rate = cache_metrics.cache_hits / cache_metrics.total_requests if cache_metrics.total_requests > 0 else 0
                    cache_trends[cache_name] = {
                        "hit_rate": hit_rate,
                        "total_requests": cache_metrics.total_requests,
                        "avg_lookup_time": cache_metrics.avg_lookup_time,
                        "cache_size": cache_metrics.total_size
                    }

            trends["cache_performance"] = cache_trends

            # Analyze resource utilization trends
            recent_resources = [r for r in self.resource_history if r.timestamp >= cutoff_time]
            if recent_resources:
                trends["resource_utilization"] = {
                    "avg_cpu_percent": statistics.mean([r.cpu_percent for r in recent_resources]),
                    "avg_memory_percent": statistics.mean([r.memory_percent for r in recent_resources]),
                    "avg_disk_percent": statistics.mean([r.disk_percent for r in recent_resources]),
                    "avg_active_connections": statistics.mean([r.active_connections for r in recent_resources])
                }

            return trends

        except Exception as e:
            logger.error(f"Failed to analyze performance trends: {e}")
            return {}

    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of data."""
        try:
            if not data:
                return 0.0

            sorted_data = sorted(data)
            index = (percentile / 100.0) * (len(sorted_data) - 1)

            if index.is_integer():
                return sorted_data[int(index)]
            else:
                lower_index = int(index)
                upper_index = lower_index + 1
                weight = index - lower_index
                return sorted_data[lower_index] * (1 - weight) + sorted_data[upper_index] * weight

        except Exception as e:
            logger.error(f"Failed to calculate percentile: {e}")
            return 0.0

    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Basic statistics
                report = {
                    "report_generated_at": current_time.isoformat(),
                    "summary": {
                        "total_api_requests": self.stats["total_api_requests"],
                        "total_cache_requests": self.stats["total_cache_requests"],
                        "performance_alerts_triggered": self.stats["performance_alerts_triggered"],
                        "last_collection": self.stats["last_collection"].isoformat() if self.stats["last_collection"] else None,
                        "monitored_endpoints": len(self.api_metrics),
                        "monitored_caches": len(self.cache_metrics)
                    },
                    "thresholds": self.performance_thresholds
                }

                # API performance summary
                api_summary = {}
                for endpoint_key, api_metrics in self.api_metrics.items():
                    if api_metrics.response_times:
                        avg_time = statistics.mean(api_metrics.response_times)
                        p95_time = self._percentile(list(api_metrics.response_times), 95)
                        error_rate = api_metrics.error_count / api_metrics.total_requests if api_metrics.total_requests > 0 else 0

                        api_summary[endpoint_key] = {
                            "avg_response_time": avg_time,
                            "p95_response_time": p95_time,
                            "total_requests": api_metrics.total_requests,
                            "error_rate": error_rate,
                            "last_request": api_metrics.last_request.isoformat() if api_metrics.last_request else None
                        }

                report["api_performance"] = api_summary

                # Cache performance summary
                cache_summary = {}
                for cache_name, cache_metrics in self.cache_metrics.items():
                    hit_rate = cache_metrics.cache_hits / cache_metrics.total_requests if cache_metrics.total_requests > 0 else 0
                    cache_summary[cache_name] = {
                        "hit_rate": hit_rate,
                        "total_requests": cache_metrics.total_requests,
                        "avg_lookup_time": cache_metrics.avg_lookup_time,
                        "cache_size": cache_metrics.total_size,
                        "last_updated": cache_metrics.last_updated.isoformat() if cache_metrics.last_updated else None
                    }

                report["cache_performance"] = cache_summary

                # Resource utilization summary
                if self.resource_history:
                    latest_resource = self.resource_history[-1]
                    report["resource_utilization"] = {
                        "current_cpu_percent": latest_resource.cpu_percent,
                        "current_memory_percent": latest_resource.memory_percent,
                        "current_disk_percent": latest_resource.disk_percent,
                        "active_connections": latest_resource.active_connections,
                        "process_count": latest_resource.process_count,
                        "timestamp": latest_resource.timestamp.isoformat()
                    }

                # Performance trends
                report["trends"] = self.analyze_performance_trends(24)

                # Performance recommendations
                report["recommendations"] = self._generate_performance_recommendations()

                return report

        except Exception as e:
            logger.error(f"Failed to generate performance report: {e}")
            return {"error": str(e)}

    def _generate_performance_recommendations(self) -> List[str]:
        """Generate performance optimization recommendations."""
        recommendations = []

        try:
            # Check API performance
            for endpoint_key, api_metrics in self.api_metrics.items():
                if api_metrics.response_times:
                    avg_time = statistics.mean(api_metrics.response_times)
                    if avg_time > self.performance_thresholds["api_response_time_warning"]:
                        recommendations.append(f"Optimize {endpoint_key} - average response time {avg_time:.1f}ms exceeds threshold")

                    error_rate = api_metrics.error_count / api_metrics.total_requests if api_metrics.total_requests > 0 else 0
                    if error_rate > 0.05:  # 5% error rate
                        recommendations.append(f"Investigate errors in {endpoint_key} - error rate {error_rate:.1%}")

            # Check cache performance
            for cache_name, cache_metrics in self.cache_metrics.items():
                hit_rate = cache_metrics.cache_hits / cache_metrics.total_requests if cache_metrics.total_requests > 0 else 0
                if hit_rate < self.performance_thresholds["cache_hit_rate_warning"]:
                    recommendations.append(f"Improve cache strategy for {cache_name} - hit rate {hit_rate:.1%} below optimal")

            # Check resource utilization
            if self.resource_history:
                latest_resource = self.resource_history[-1]
                if latest_resource.cpu_percent > self.performance_thresholds["cpu_usage_warning"]:
                    recommendations.append(f"High CPU usage detected ({latest_resource.cpu_percent:.1f}%) - consider scaling or optimization")

                if latest_resource.memory_percent > self.performance_thresholds["memory_usage_warning"]:
                    recommendations.append(f"High memory usage detected ({latest_resource.memory_percent:.1f}%) - investigate memory leaks or increase capacity")

            # Check for performance trends
            trends = self.analyze_performance_trends(24)
            api_trends = trends.get("api_performance", {})
            for endpoint, trend_data in api_trends.items():
                if trend_data.get("error_rate", 0) > 0.1:
                    recommendations.append(f"High error rate trend detected for {endpoint} - requires immediate attention")

            if not recommendations:
                recommendations.append("Performance monitoring shows all systems operating within normal parameters")

        except Exception as e:
            logger.error(f"Failed to generate performance recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations

    async def start_monitoring(self):
        """Start performance monitoring loop."""
        logger.info("Starting advanced performance telemetry monitoring")

        while True:
            try:
                # Collect resource utilization metrics
                await self.collect_resource_utilization()

                # Wait for next collection cycle
                await asyncio.sleep(self.collection_interval_seconds)

            except Exception as e:
                logger.error(f"Error in performance monitoring loop: {e}")
                await asyncio.sleep(30)  # Wait before retry


class PerformanceMiddleware(BaseHTTPMiddleware):
    """FastAPI middleware for automatic API performance tracking."""

    def __init__(self, app, telemetry: AdvancedPerformanceTelemetry):
        super().__init__(app)
        self.telemetry = telemetry

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Track API performance for each request."""
        start_time = time.time()

        # Get request details
        endpoint = request.url.path
        method = request.method
        request_size = int(request.headers.get("content-length", 0))

        try:
            # Process request
            response = await call_next(request)

            # Calculate metrics
            response_time_ms = (time.time() - start_time) * 1000
            status_code = response.status_code
            response_size = int(response.headers.get("content-length", 0))

            # Record performance
            self.telemetry.record_api_performance(
                endpoint=endpoint,
                method=method,
                response_time_ms=response_time_ms,
                status_code=status_code,
                request_size=request_size,
                response_size=response_size
            )

            return response

        except Exception as e:
            # Record error
            response_time_ms = (time.time() - start_time) * 1000
            self.telemetry.record_api_performance(
                endpoint=endpoint,
                method=method,
                response_time_ms=response_time_ms,
                status_code=500,
                request_size=request_size,
                response_size=0
            )
            raise


def performance_timer(operation_name: str):
    """Decorator for timing function execution."""
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                performance_telemetry.record_custom_metric(f"{operation_name}_duration", duration_ms)
                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                performance_telemetry.record_custom_metric(f"{operation_name}_duration", duration_ms, {"error": "true"})
                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                performance_telemetry.record_custom_metric(f"{operation_name}_duration", duration_ms)
                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                performance_telemetry.record_custom_metric(f"{operation_name}_duration", duration_ms, {"error": "true"})
                raise

        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# Global instance
performance_telemetry = AdvancedPerformanceTelemetry()
