"""
Real-Time Accuracy Monitoring Dashboard

This module implements live accuracy tracking with automated alerts when accuracy drops
below 95% threshold, following 2025 observability best practices. It provides comprehensive
accuracy monitoring across all ML models and prediction systems.

Features:
- Real-time accuracy tracking with sub-second updates
- Automated alerts when accuracy drops below thresholds
- Multi-model accuracy comparison and trending
- Accuracy degradation detection and root cause analysis
- Performance correlation analysis
- Automated accuracy recovery recommendations
- Live dashboard with WebSocket updates
"""

import asyncio
import json
import logging
import statistics
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import threading

import structlog
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse

from ..core.config import config
from ..core.logging_config import get_logger
from ..compliance.audit_framework import AuditTrail, AuditEventType, RiskLevel
from .dashboard import metrics_collector, alert_manager, AlertSeverity

logger = get_logger(__name__)


class AccuracyMetricType(Enum):
    """Types of accuracy metrics."""
    OVERALL_ACCURACY = "overall_accuracy"
    MODEL_ACCURACY = "model_accuracy"
    PREDICTION_ACCURACY = "prediction_accuracy"
    FALSE_POSITIVE_RATE = "false_positive_rate"
    FALSE_NEGATIVE_RATE = "false_negative_rate"
    PRECISION = "precision"
    RECALL = "recall"
    F1_SCORE = "f1_score"


class AccuracyAlertType(Enum):
    """Types of accuracy alerts."""
    ACCURACY_DROP = "accuracy_drop"
    ACCURACY_DEGRADATION = "accuracy_degradation"
    MODEL_DRIFT = "model_drift"
    PERFORMANCE_ANOMALY = "performance_anomaly"
    THRESHOLD_BREACH = "threshold_breach"


@dataclass
class AccuracyDataPoint:
    """Single accuracy measurement."""
    timestamp: datetime
    metric_type: AccuracyMetricType
    value: float
    model_name: Optional[str] = None
    prediction_id: Optional[str] = None
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class AccuracyAlert:
    """Accuracy-related alert."""
    alert_id: str
    alert_type: AccuracyAlertType
    severity: AlertSeverity
    message: str
    current_value: float
    threshold_value: float
    model_name: Optional[str]
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None


@dataclass
class AccuracyTrend:
    """Accuracy trend analysis."""
    metric_type: AccuracyMetricType
    model_name: Optional[str]
    current_value: float
    trend_direction: str  # "improving", "declining", "stable"
    trend_strength: float  # 0.0 to 1.0
    prediction_horizon_hours: int
    predicted_value: float
    confidence: float


class RealTimeAccuracyMonitor:
    """
    Real-time accuracy monitoring system with automated alerting.
    
    Provides comprehensive accuracy tracking across all ML models and prediction
    systems with sub-second updates and intelligent alerting.
    """
    
    def __init__(self, audit_trail: Optional[AuditTrail] = None):
        self.audit_trail = audit_trail
        self.accuracy_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.accuracy_alerts: Dict[str, AccuracyAlert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        self.active_websockets: List[WebSocket] = []
        self._lock = threading.RLock()
        
        # Accuracy thresholds (2025 best practices)
        self.accuracy_thresholds = {
            "critical": 0.90,    # 90% - critical alert
            "warning": 0.95,     # 95% - warning alert
            "target": 0.98       # 98% - target accuracy
        }
        
        # Monitoring configuration
        self.monitoring_interval_seconds = 1    # Real-time updates
        self.trend_analysis_window_hours = 24   # 24-hour trend analysis
        self.alert_cooldown_minutes = 5         # Prevent alert spam
        self.accuracy_smoothing_window = 100    # Moving average window
        
        # Performance correlation tracking
        self.performance_correlations: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Accuracy recovery tracking
        self.recovery_actions: List[Dict[str, Any]] = []
        
        # Statistics
        self.stats = {
            "total_predictions": 0,
            "accurate_predictions": 0,
            "current_accuracy": 0.0,
            "accuracy_alerts_triggered": 0,
            "last_accuracy_check": None
        }
        
        logger.info("RealTimeAccuracyMonitor initialized")
    
    def record_prediction_accuracy(self, prediction_id: str, model_name: str,
                                 is_accurate: bool, confidence: float = 1.0,
                                 labels: Optional[Dict[str, str]] = None):
        """Record accuracy for a single prediction."""
        try:
            current_time = datetime.now(timezone.utc)
            
            with self._lock:
                # Update statistics
                self.stats["total_predictions"] += 1
                if is_accurate:
                    self.stats["accurate_predictions"] += 1
                
                # Calculate current accuracy
                if self.stats["total_predictions"] > 0:
                    self.stats["current_accuracy"] = self.stats["accurate_predictions"] / self.stats["total_predictions"]
                
                self.stats["last_accuracy_check"] = current_time
                
                # Record accuracy data point
                accuracy_value = 1.0 if is_accurate else 0.0
                data_point = AccuracyDataPoint(
                    timestamp=current_time,
                    metric_type=AccuracyMetricType.PREDICTION_ACCURACY,
                    value=accuracy_value,
                    model_name=model_name,
                    prediction_id=prediction_id,
                    labels=labels or {}
                )
                
                key = f"{model_name}:prediction_accuracy"
                self.accuracy_data[key].append(data_point)
                
                # Calculate model-specific accuracy
                model_accuracy = self._calculate_model_accuracy(model_name)
                model_data_point = AccuracyDataPoint(
                    timestamp=current_time,
                    metric_type=AccuracyMetricType.MODEL_ACCURACY,
                    value=model_accuracy,
                    model_name=model_name,
                    labels=labels or {}
                )
                
                model_key = f"{model_name}:model_accuracy"
                self.accuracy_data[model_key].append(model_data_point)
                
                # Record overall accuracy
                overall_data_point = AccuracyDataPoint(
                    timestamp=current_time,
                    metric_type=AccuracyMetricType.OVERALL_ACCURACY,
                    value=self.stats["current_accuracy"],
                    labels=labels or {}
                )
                
                self.accuracy_data["overall:overall_accuracy"].append(overall_data_point)
            
            # Update metrics collector
            metrics_collector.set_gauge("prediction_accuracy", self.stats["current_accuracy"])
            metrics_collector.set_gauge(f"model_accuracy_{model_name}", model_accuracy)
            metrics_collector.increment_counter("predictions_total", 1, {"model": model_name})
            metrics_collector.increment_counter("predictions_accurate", 1 if is_accurate else 0, {"model": model_name})
            
            # Check for accuracy alerts
            asyncio.create_task(self._check_accuracy_alerts())
            
            # Broadcast to WebSocket clients
            asyncio.create_task(self._broadcast_accuracy_update())
            
        except Exception as e:
            logger.error(f"Failed to record prediction accuracy: {e}")
    
    def _calculate_model_accuracy(self, model_name: str) -> float:
        """Calculate current accuracy for a specific model."""
        try:
            key = f"{model_name}:prediction_accuracy"
            if key not in self.accuracy_data or not self.accuracy_data[key]:
                return 0.0
            
            # Use recent predictions for accuracy calculation
            recent_predictions = list(self.accuracy_data[key])[-self.accuracy_smoothing_window:]
            if not recent_predictions:
                return 0.0
            
            accurate_count = sum(1 for dp in recent_predictions if dp.value == 1.0)
            return accurate_count / len(recent_predictions)
            
        except Exception as e:
            logger.error(f"Failed to calculate model accuracy: {e}")
            return 0.0
    
    async def _check_accuracy_alerts(self):
        """Check accuracy metrics against thresholds and generate alerts."""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Check overall accuracy
            overall_accuracy = self.stats["current_accuracy"]
            await self._check_accuracy_threshold("overall", overall_accuracy, current_time)
            
            # Check model-specific accuracy
            for key, data_points in self.accuracy_data.items():
                if ":model_accuracy" in key and data_points:
                    model_name = key.split(":")[0]
                    latest_accuracy = data_points[-1].value
                    await self._check_accuracy_threshold(model_name, latest_accuracy, current_time)
            
            # Check for accuracy degradation trends
            await self._check_accuracy_degradation()
            
        except Exception as e:
            logger.error(f"Failed to check accuracy alerts: {e}")
    
    async def _check_accuracy_threshold(self, model_name: str, accuracy: float, timestamp: datetime):
        """Check accuracy against thresholds and generate alerts if needed."""
        try:
            alert_id = f"accuracy_threshold_{model_name}"
            
            # Determine alert severity
            severity = None
            threshold_value = 0.0
            
            if accuracy < self.accuracy_thresholds["critical"]:
                severity = AlertSeverity.CRITICAL
                threshold_value = self.accuracy_thresholds["critical"]
            elif accuracy < self.accuracy_thresholds["warning"]:
                severity = AlertSeverity.WARNING
                threshold_value = self.accuracy_thresholds["warning"]
            
            if severity:
                # Check cooldown period
                if alert_id in self.accuracy_alerts:
                    last_alert = self.accuracy_alerts[alert_id]
                    if not last_alert.resolved:
                        time_since_alert = (timestamp - last_alert.timestamp).total_seconds() / 60
                        if time_since_alert < self.alert_cooldown_minutes:
                            return  # Still in cooldown
                
                # Create new alert
                alert = AccuracyAlert(
                    alert_id=alert_id,
                    alert_type=AccuracyAlertType.THRESHOLD_BREACH,
                    severity=severity,
                    message=f"Accuracy for {model_name} dropped to {accuracy:.3f} (below {threshold_value:.3f} threshold)",
                    current_value=accuracy,
                    threshold_value=threshold_value,
                    model_name=model_name,
                    timestamp=timestamp
                )
                
                with self._lock:
                    self.accuracy_alerts[alert_id] = alert
                    self.alert_history.append(alert)
                    self.stats["accuracy_alerts_triggered"] += 1
                
                # Log alert
                logger.warning("Accuracy alert triggered",
                             alert_id=alert_id,
                             model_name=model_name,
                             accuracy=accuracy,
                             threshold=threshold_value,
                             severity=severity.value)
                
                # Add to main alert manager
                alert_manager.alerts[alert_id] = {
                    "id": alert_id,
                    "message": alert.message,
                    "severity": severity,
                    "timestamp": timestamp,
                    "metric_name": f"accuracy_{model_name}",
                    "current_value": accuracy,
                    "threshold": threshold_value,
                    "resolved": False
                }
                
                # Audit trail
                if self.audit_trail:
                    self.audit_trail.log_event(
                        AuditEventType.SECURITY_VIOLATION,
                        None,
                        "accuracy_monitor",
                        "accuracy_alert",
                        {
                            "model_name": model_name,
                            "accuracy": accuracy,
                            "threshold": threshold_value,
                            "severity": severity.value
                        },
                        risk_level=RiskLevel.HIGH if severity == AlertSeverity.CRITICAL else RiskLevel.MEDIUM
                    )
                
            else:
                # Resolve existing alert if accuracy is back to normal
                if alert_id in self.accuracy_alerts and not self.accuracy_alerts[alert_id].resolved:
                    self.accuracy_alerts[alert_id].resolved = True
                    self.accuracy_alerts[alert_id].resolved_at = timestamp
                    
                    # Resolve in main alert manager
                    if alert_id in alert_manager.alerts:
                        alert_manager.alerts[alert_id]["resolved"] = True
                        alert_manager.alerts[alert_id]["resolved_at"] = timestamp
                    
                    logger.info("Accuracy alert resolved",
                               alert_id=alert_id,
                               model_name=model_name,
                               accuracy=accuracy)
                    
        except Exception as e:
            logger.error(f"Failed to check accuracy threshold: {e}")
    
    async def _check_accuracy_degradation(self):
        """Check for accuracy degradation trends."""
        try:
            current_time = datetime.now(timezone.utc)
            
            for key, data_points in self.accuracy_data.items():
                if ":model_accuracy" in key and len(data_points) >= 100:
                    model_name = key.split(":")[0]
                    
                    # Calculate trend
                    recent_points = list(data_points)[-100:]
                    values = [dp.value for dp in recent_points]
                    
                    # Simple linear trend calculation
                    if len(values) >= 10:
                        x = list(range(len(values)))
                        slope = self._calculate_slope(x, values)
                        
                        # Check for significant negative trend
                        if slope < -0.001:  # Declining by 0.1% per measurement
                            alert_id = f"accuracy_degradation_{model_name}"
                            
                            if alert_id not in self.accuracy_alerts or self.accuracy_alerts[alert_id].resolved:
                                alert = AccuracyAlert(
                                    alert_id=alert_id,
                                    alert_type=AccuracyAlertType.ACCURACY_DEGRADATION,
                                    severity=AlertSeverity.WARNING,
                                    message=f"Accuracy degradation detected for {model_name} (slope: {slope:.6f})",
                                    current_value=values[-1],
                                    threshold_value=slope,
                                    model_name=model_name,
                                    timestamp=current_time
                                )
                                
                                with self._lock:
                                    self.accuracy_alerts[alert_id] = alert
                                    self.alert_history.append(alert)
                                
                                logger.warning("Accuracy degradation detected",
                                             model_name=model_name,
                                             slope=slope,
                                             current_accuracy=values[-1])
                                
        except Exception as e:
            logger.error(f"Failed to check accuracy degradation: {e}")
    
    def _calculate_slope(self, x: List[float], y: List[float]) -> float:
        """Calculate linear regression slope."""
        try:
            n = len(x)
            if n < 2:
                return 0.0
            
            sum_x = sum(x)
            sum_y = sum(y)
            sum_xy = sum(x[i] * y[i] for i in range(n))
            sum_x2 = sum(x[i] * x[i] for i in range(n))
            
            denominator = n * sum_x2 - sum_x * sum_x
            if denominator == 0:
                return 0.0
            
            slope = (n * sum_xy - sum_x * sum_y) / denominator
            return slope
            
        except Exception as e:
            logger.error(f"Failed to calculate slope: {e}")
            return 0.0

    async def _broadcast_accuracy_update(self):
        """Broadcast accuracy updates to WebSocket clients."""
        try:
            if not self.active_websockets:
                return

            # Prepare update data
            update_data = {
                "type": "accuracy_update",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "overall_accuracy": self.stats["current_accuracy"],
                "total_predictions": self.stats["total_predictions"],
                "active_alerts": len([a for a in self.accuracy_alerts.values() if not a.resolved]),
                "model_accuracies": self._get_current_model_accuracies()
            }

            # Broadcast to all connected clients
            disconnected_clients = []
            for websocket in self.active_websockets:
                try:
                    await websocket.send_json(update_data)
                except Exception:
                    disconnected_clients.append(websocket)

            # Remove disconnected clients
            for client in disconnected_clients:
                self.active_websockets.remove(client)

        except Exception as e:
            logger.error(f"Failed to broadcast accuracy update: {e}")

    def _get_current_model_accuracies(self) -> Dict[str, float]:
        """Get current accuracy for all models."""
        try:
            model_accuracies = {}

            for key, data_points in self.accuracy_data.items():
                if ":model_accuracy" in key and data_points:
                    model_name = key.split(":")[0]
                    model_accuracies[model_name] = data_points[-1].value

            return model_accuracies

        except Exception as e:
            logger.error(f"Failed to get current model accuracies: {e}")
            return {}

    def analyze_accuracy_trends(self, hours: int = 24) -> List[AccuracyTrend]:
        """Analyze accuracy trends over specified time period."""
        try:
            trends = []
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            for key, data_points in self.accuracy_data.items():
                if ":model_accuracy" in key:
                    model_name = key.split(":")[0]

                    # Filter recent data points
                    recent_points = [dp for dp in data_points if dp.timestamp >= cutoff_time]

                    if len(recent_points) >= 10:
                        values = [dp.value for dp in recent_points]
                        current_value = values[-1]

                        # Calculate trend
                        x = list(range(len(values)))
                        slope = self._calculate_slope(x, values)

                        # Determine trend direction and strength
                        if abs(slope) < 0.0001:
                            direction = "stable"
                            strength = 0.0
                        elif slope > 0:
                            direction = "improving"
                            strength = min(1.0, abs(slope) * 1000)
                        else:
                            direction = "declining"
                            strength = min(1.0, abs(slope) * 1000)

                        # Simple prediction (linear extrapolation)
                        prediction_steps = hours  # Predict for same time period ahead
                        predicted_value = current_value + (slope * prediction_steps)
                        predicted_value = max(0.0, min(1.0, predicted_value))  # Clamp to [0,1]

                        # Calculate confidence based on trend consistency
                        confidence = min(1.0, len(recent_points) / 100.0)

                        trend = AccuracyTrend(
                            metric_type=AccuracyMetricType.MODEL_ACCURACY,
                            model_name=model_name,
                            current_value=current_value,
                            trend_direction=direction,
                            trend_strength=strength,
                            prediction_horizon_hours=hours,
                            predicted_value=predicted_value,
                            confidence=confidence
                        )

                        trends.append(trend)

            return trends

        except Exception as e:
            logger.error(f"Failed to analyze accuracy trends: {e}")
            return []

    def generate_accuracy_report(self) -> Dict[str, Any]:
        """Generate comprehensive accuracy monitoring report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Basic statistics
                report = {
                    "report_generated_at": current_time.isoformat(),
                    "summary": {
                        "overall_accuracy": self.stats["current_accuracy"],
                        "total_predictions": self.stats["total_predictions"],
                        "accurate_predictions": self.stats["accurate_predictions"],
                        "active_alerts": len([a for a in self.accuracy_alerts.values() if not a.resolved]),
                        "total_alerts_triggered": self.stats["accuracy_alerts_triggered"],
                        "last_accuracy_check": self.stats["last_accuracy_check"].isoformat() if self.stats["last_accuracy_check"] else None
                    },
                    "thresholds": self.accuracy_thresholds,
                    "model_accuracies": self._get_current_model_accuracies()
                }

                # Active alerts
                active_alerts = []
                for alert in self.accuracy_alerts.values():
                    if not alert.resolved:
                        active_alerts.append({
                            "alert_id": alert.alert_id,
                            "alert_type": alert.alert_type.value,
                            "severity": alert.severity.value,
                            "message": alert.message,
                            "current_value": alert.current_value,
                            "threshold_value": alert.threshold_value,
                            "model_name": alert.model_name,
                            "timestamp": alert.timestamp.isoformat()
                        })

                report["active_alerts"] = active_alerts

                # Recent alert history
                recent_alerts = []
                for alert in list(self.alert_history)[-20:]:  # Last 20 alerts
                    recent_alerts.append({
                        "alert_id": alert.alert_id,
                        "alert_type": alert.alert_type.value,
                        "severity": alert.severity.value,
                        "message": alert.message,
                        "model_name": alert.model_name,
                        "timestamp": alert.timestamp.isoformat(),
                        "resolved": alert.resolved,
                        "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None
                    })

                report["recent_alerts"] = recent_alerts

                # Accuracy trends
                trends = self.analyze_accuracy_trends(24)
                report["accuracy_trends"] = []
                for trend in trends:
                    report["accuracy_trends"].append({
                        "model_name": trend.model_name,
                        "current_value": trend.current_value,
                        "trend_direction": trend.trend_direction,
                        "trend_strength": trend.trend_strength,
                        "predicted_value": trend.predicted_value,
                        "confidence": trend.confidence
                    })

                # Performance recommendations
                report["recommendations"] = self._generate_accuracy_recommendations()

                return report

        except Exception as e:
            logger.error(f"Failed to generate accuracy report: {e}")
            return {"error": str(e)}

    def _generate_accuracy_recommendations(self) -> List[str]:
        """Generate accuracy improvement recommendations."""
        recommendations = []

        try:
            # Check overall accuracy
            if self.stats["current_accuracy"] < self.accuracy_thresholds["target"]:
                recommendations.append(f"Overall accuracy ({self.stats['current_accuracy']:.3f}) below target ({self.accuracy_thresholds['target']:.3f}) - consider model retraining")

            # Check model-specific accuracy
            model_accuracies = self._get_current_model_accuracies()
            for model_name, accuracy in model_accuracies.items():
                if accuracy < self.accuracy_thresholds["warning"]:
                    recommendations.append(f"Model {model_name} accuracy ({accuracy:.3f}) below warning threshold - investigate model performance")

            # Check for accuracy trends
            trends = self.analyze_accuracy_trends(24)
            for trend in trends:
                if trend.trend_direction == "declining" and trend.trend_strength > 0.5:
                    recommendations.append(f"Model {trend.model_name} showing declining accuracy trend - monitor closely and consider intervention")

            # Check alert frequency
            recent_alerts = [a for a in self.alert_history if (datetime.now(timezone.utc) - a.timestamp).hours < 24]
            if len(recent_alerts) > 10:
                recommendations.append("High frequency of accuracy alerts in last 24 hours - investigate system stability")

            # Check data volume
            if self.stats["total_predictions"] < 1000:
                recommendations.append("Low prediction volume - ensure sufficient data for accurate monitoring")

            if not recommendations:
                recommendations.append("Accuracy monitoring is operating within normal parameters")

        except Exception as e:
            logger.error(f"Failed to generate accuracy recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations

    async def start_monitoring(self):
        """Start real-time accuracy monitoring loop."""
        logger.info("Starting real-time accuracy monitoring")

        while True:
            try:
                # Perform periodic accuracy checks
                await self._check_accuracy_alerts()

                # Update metrics
                metrics_collector.set_gauge("accuracy_monitor_active_alerts",
                                          len([a for a in self.accuracy_alerts.values() if not a.resolved]))
                metrics_collector.set_gauge("accuracy_monitor_total_predictions", self.stats["total_predictions"])

                # Wait for next monitoring cycle
                await asyncio.sleep(self.monitoring_interval_seconds)

            except Exception as e:
                logger.error(f"Error in accuracy monitoring loop: {e}")
                await asyncio.sleep(5)  # Wait before retry

    async def handle_websocket(self, websocket: WebSocket):
        """Handle WebSocket connection for real-time accuracy updates."""
        try:
            await websocket.accept()
            self.active_websockets.append(websocket)

            logger.info("Accuracy monitoring WebSocket client connected")

            # Send initial data
            initial_data = {
                "type": "initial_data",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "summary": self.generate_accuracy_report()["summary"],
                "model_accuracies": self._get_current_model_accuracies(),
                "active_alerts": len([a for a in self.accuracy_alerts.values() if not a.resolved])
            }

            await websocket.send_json(initial_data)

            # Keep connection alive
            while True:
                await asyncio.sleep(30)  # Ping every 30 seconds
                await websocket.send_json({"type": "ping", "timestamp": datetime.now(timezone.utc).isoformat()})

        except WebSocketDisconnect:
            if websocket in self.active_websockets:
                self.active_websockets.remove(websocket)
            logger.info("Accuracy monitoring WebSocket client disconnected")
        except Exception as e:
            logger.error(f"WebSocket error in accuracy monitor: {e}")
            if websocket in self.active_websockets:
                self.active_websockets.remove(websocket)


# Global instance
accuracy_monitor = RealTimeAccuracyMonitor()
