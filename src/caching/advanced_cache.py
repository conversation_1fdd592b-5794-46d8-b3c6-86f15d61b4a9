"""
Advanced Multi-Layer Caching Strategy
L1 (in-memory), L2 (Redis), L3 (database) with intelligent invalidation and warming.
"""

import asyncio
import json
import time
import uuid
import hashlib
from abc import ABC, abstractmethod
from collections import defaultdict, OrderedDict
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union, Callable
import threading
import pickle
import gzip

import structlog

from ..core.logging_config import get_logger
from ..monitoring import increment_counter, set_gauge, record_timer


logger = get_logger(__name__)


class CacheLayer(Enum):
    """Cache layer types."""
    L1_MEMORY = "l1_memory"
    L2_REDIS = "l2_redis"
    L3_DATABASE = "l3_database"


class CacheStrategy(Enum):
    """Cache strategies."""
    LRU = "lru"
    LFU = "lfu"
    TTL = "ttl"
    ADAPTIVE = "adaptive"


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl_seconds: Optional[int] = None
    size_bytes: int = 0
    tags: Set[str] = field(default_factory=set)
    
    def is_expired(self) -> bool:
        """Check if entry is expired."""
        if self.ttl_seconds is None:
            return False
        
        age = (datetime.now(timezone.utc) - self.created_at).total_seconds()
        return age > self.ttl_seconds
    
    def touch(self):
        """Update access metadata."""
        self.last_accessed = datetime.now(timezone.utc)
        self.access_count += 1


@dataclass
class CacheStats:
    """Cache statistics."""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size: int = 0
    memory_usage: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate hit rate."""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class CacheInterface(ABC):
    """Abstract cache interface."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in cache."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache entries."""
        pass
    
    @abstractmethod
    async def invalidate_by_tags(self, tags: Set[str]) -> int:
        """Invalidate entries by tags."""
        pass
    
    @abstractmethod
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        pass


class L1MemoryCache(CacheInterface):
    """L1 in-memory cache with advanced eviction strategies."""
    
    def __init__(self, max_size: int = 10000, max_memory_mb: int = 100, 
                 strategy: CacheStrategy = CacheStrategy.ADAPTIVE):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.strategy = strategy
        self.entries: Dict[str, CacheEntry] = {}
        self.access_order = OrderedDict()  # For LRU
        self.frequency_counter = defaultdict(int)  # For LFU
        self.stats = CacheStats()
        self._lock = threading.RLock()
        
        logger.info("L1MemoryCache initialized", 
                   max_size=max_size, 
                   max_memory_mb=max_memory_mb,
                   strategy=strategy.value)
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from L1 cache."""
        with self._lock:
            if key not in self.entries:
                self.stats.misses += 1
                increment_counter("cache_misses", 1, {"layer": "l1"})
                return None
            
            entry = self.entries[key]
            
            # Check expiration
            if entry.is_expired():
                await self.delete(key)
                self.stats.misses += 1
                increment_counter("cache_misses", 1, {"layer": "l1"})
                return None
            
            # Update access metadata
            entry.touch()
            self.access_order.move_to_end(key)
            self.frequency_counter[key] += 1
            
            self.stats.hits += 1
            increment_counter("cache_hits", 1, {"layer": "l1"})
            
            return entry.value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in L1 cache."""
        with self._lock:
            # Calculate entry size
            try:
                serialized = pickle.dumps(value)
                size_bytes = len(serialized)
            except:
                size_bytes = len(str(value).encode('utf-8'))
            
            # Check if we need to evict
            while (len(self.entries) >= self.max_size or 
                   self.stats.memory_usage + size_bytes > self.max_memory_bytes):
                if not await self._evict_entry():
                    break
            
            # Create entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(timezone.utc),
                last_accessed=datetime.now(timezone.utc),
                ttl_seconds=ttl,
                size_bytes=size_bytes,
                tags=tags or set()
            )
            
            # Remove old entry if exists
            if key in self.entries:
                old_entry = self.entries[key]
                self.stats.memory_usage -= old_entry.size_bytes
            
            # Add new entry
            self.entries[key] = entry
            self.access_order[key] = True
            self.stats.memory_usage += size_bytes
            self.stats.size = len(self.entries)
            
            set_gauge("cache_size", self.stats.size, {"layer": "l1"})
            set_gauge("cache_memory_usage", self.stats.memory_usage, {"layer": "l1"})
            
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete value from L1 cache."""
        with self._lock:
            if key not in self.entries:
                return False
            
            entry = self.entries[key]
            self.stats.memory_usage -= entry.size_bytes
            
            del self.entries[key]
            self.access_order.pop(key, None)
            self.frequency_counter.pop(key, None)
            
            self.stats.size = len(self.entries)
            set_gauge("cache_size", self.stats.size, {"layer": "l1"})
            
            return True
    
    async def clear(self) -> bool:
        """Clear all L1 cache entries."""
        with self._lock:
            self.entries.clear()
            self.access_order.clear()
            self.frequency_counter.clear()
            self.stats.memory_usage = 0
            self.stats.size = 0
            
            return True
    
    async def invalidate_by_tags(self, tags: Set[str]) -> int:
        """Invalidate entries by tags."""
        with self._lock:
            keys_to_delete = []
            
            for key, entry in self.entries.items():
                if entry.tags.intersection(tags):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                await self.delete(key)
            
            return len(keys_to_delete)
    
    async def _evict_entry(self) -> bool:
        """Evict an entry based on strategy."""
        if not self.entries:
            return False
        
        key_to_evict = None
        
        if self.strategy == CacheStrategy.LRU:
            key_to_evict = next(iter(self.access_order))
        elif self.strategy == CacheStrategy.LFU:
            key_to_evict = min(self.frequency_counter.keys(), 
                             key=lambda k: self.frequency_counter[k])
        elif self.strategy == CacheStrategy.TTL:
            # Evict expired entries first
            now = datetime.now(timezone.utc)
            for key, entry in self.entries.items():
                if entry.is_expired():
                    key_to_evict = key
                    break
            
            # If no expired entries, fall back to LRU
            if key_to_evict is None:
                key_to_evict = next(iter(self.access_order))
        elif self.strategy == CacheStrategy.ADAPTIVE:
            # Adaptive strategy: consider age, frequency, and size
            best_score = float('inf')
            now = datetime.now(timezone.utc)
            
            for key, entry in self.entries.items():
                age = (now - entry.last_accessed).total_seconds()
                frequency = self.frequency_counter.get(key, 1)
                size_factor = entry.size_bytes / 1024  # KB
                
                # Lower score = better candidate for eviction
                score = frequency / (age + 1) - size_factor * 0.1
                
                if score < best_score:
                    best_score = score
                    key_to_evict = key
        
        if key_to_evict:
            await self.delete(key_to_evict)
            self.stats.evictions += 1
            increment_counter("cache_evictions", 1, {"layer": "l1"})
            return True
        
        return False
    
    def get_stats(self) -> CacheStats:
        """Get L1 cache statistics."""
        with self._lock:
            return CacheStats(
                hits=self.stats.hits,
                misses=self.stats.misses,
                evictions=self.stats.evictions,
                size=self.stats.size,
                memory_usage=self.stats.memory_usage
            )


class L2RedisCache(CacheInterface):
    """L2 Redis cache (simulated for this implementation)."""
    
    def __init__(self, host: str = "localhost", port: int = 6379, db: int = 0):
        self.host = host
        self.port = port
        self.db = db
        self.stats = CacheStats()
        self._simulated_storage: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        
        logger.info("L2RedisCache initialized", host=host, port=port, db=db)
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from L2 cache."""
        with self._lock:
            if key not in self._simulated_storage:
                self.stats.misses += 1
                increment_counter("cache_misses", 1, {"layer": "l2"})
                return None
            
            entry_data = self._simulated_storage[key]
            
            # Check expiration
            if entry_data.get("expires_at"):
                if datetime.now(timezone.utc) > datetime.fromisoformat(entry_data["expires_at"]):
                    await self.delete(key)
                    self.stats.misses += 1
                    increment_counter("cache_misses", 1, {"layer": "l2"})
                    return None
            
            self.stats.hits += 1
            increment_counter("cache_hits", 1, {"layer": "l2"})
            
            # Simulate Redis serialization/deserialization
            try:
                return pickle.loads(entry_data["value"])
            except:
                return entry_data["value"]
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in L2 cache."""
        with self._lock:
            expires_at = None
            if ttl:
                expires_at = (datetime.now(timezone.utc) + timedelta(seconds=ttl)).isoformat()
            
            try:
                serialized_value = pickle.dumps(value)
            except:
                serialized_value = str(value).encode('utf-8')
            
            self._simulated_storage[key] = {
                "value": serialized_value,
                "expires_at": expires_at,
                "tags": list(tags) if tags else [],
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.stats.size = len(self._simulated_storage)
            set_gauge("cache_size", self.stats.size, {"layer": "l2"})
            
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete value from L2 cache."""
        with self._lock:
            if key in self._simulated_storage:
                del self._simulated_storage[key]
                self.stats.size = len(self._simulated_storage)
                return True
            return False
    
    async def clear(self) -> bool:
        """Clear all L2 cache entries."""
        with self._lock:
            self._simulated_storage.clear()
            self.stats.size = 0
            return True
    
    async def invalidate_by_tags(self, tags: Set[str]) -> int:
        """Invalidate entries by tags."""
        with self._lock:
            keys_to_delete = []
            
            for key, entry_data in self._simulated_storage.items():
                entry_tags = set(entry_data.get("tags", []))
                if entry_tags.intersection(tags):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                await self.delete(key)
            
            return len(keys_to_delete)
    
    def get_stats(self) -> CacheStats:
        """Get L2 cache statistics."""
        with self._lock:
            return CacheStats(
                hits=self.stats.hits,
                misses=self.stats.misses,
                evictions=self.stats.evictions,
                size=self.stats.size,
                memory_usage=0  # Redis memory usage would be tracked separately
            )


class L3DatabaseCache(CacheInterface):
    """L3 database cache (simulated for this implementation)."""
    
    def __init__(self, connection_string: str = "sqlite:///cache.db"):
        self.connection_string = connection_string
        self.stats = CacheStats()
        self._simulated_storage: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        
        logger.info("L3DatabaseCache initialized", connection=connection_string)
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from L3 cache."""
        with self._lock:
            # Simulate database latency
            await asyncio.sleep(0.001)
            
            if key not in self._simulated_storage:
                self.stats.misses += 1
                increment_counter("cache_misses", 1, {"layer": "l3"})
                return None
            
            entry_data = self._simulated_storage[key]
            
            # Check expiration
            if entry_data.get("expires_at"):
                if datetime.now(timezone.utc) > datetime.fromisoformat(entry_data["expires_at"]):
                    await self.delete(key)
                    self.stats.misses += 1
                    increment_counter("cache_misses", 1, {"layer": "l3"})
                    return None
            
            self.stats.hits += 1
            increment_counter("cache_hits", 1, {"layer": "l3"})
            
            # Simulate database deserialization
            try:
                return pickle.loads(entry_data["value"])
            except:
                return entry_data["value"]
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, tags: Optional[Set[str]] = None) -> bool:
        """Set value in L3 cache."""
        with self._lock:
            # Simulate database latency
            await asyncio.sleep(0.002)
            
            expires_at = None
            if ttl:
                expires_at = (datetime.now(timezone.utc) + timedelta(seconds=ttl)).isoformat()
            
            try:
                serialized_value = pickle.dumps(value)
            except:
                serialized_value = str(value).encode('utf-8')
            
            self._simulated_storage[key] = {
                "value": serialized_value,
                "expires_at": expires_at,
                "tags": list(tags) if tags else [],
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.stats.size = len(self._simulated_storage)
            set_gauge("cache_size", self.stats.size, {"layer": "l3"})
            
            return True
    
    async def delete(self, key: str) -> bool:
        """Delete value from L3 cache."""
        with self._lock:
            # Simulate database latency
            await asyncio.sleep(0.001)
            
            if key in self._simulated_storage:
                del self._simulated_storage[key]
                self.stats.size = len(self._simulated_storage)
                return True
            return False
    
    async def clear(self) -> bool:
        """Clear all L3 cache entries."""
        with self._lock:
            self._simulated_storage.clear()
            self.stats.size = 0
            return True
    
    async def invalidate_by_tags(self, tags: Set[str]) -> int:
        """Invalidate entries by tags."""
        with self._lock:
            keys_to_delete = []
            
            for key, entry_data in self._simulated_storage.items():
                entry_tags = set(entry_data.get("tags", []))
                if entry_tags.intersection(tags):
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                await self.delete(key)
            
            return len(keys_to_delete)
    
    def get_stats(self) -> CacheStats:
        """Get L3 cache statistics."""
        with self._lock:
            return CacheStats(
                hits=self.stats.hits,
                misses=self.stats.misses,
                evictions=self.stats.evictions,
                size=self.stats.size,
                memory_usage=0  # Database storage would be tracked separately
            )


class MultiLayerCache:
    """Multi-layer cache with intelligent routing and warming."""

    def __init__(self, l1_cache: L1MemoryCache, l2_cache: L2RedisCache, l3_cache: L3DatabaseCache):
        self.l1 = l1_cache
        self.l2 = l2_cache
        self.l3 = l3_cache
        self.warming_tasks: Set[str] = set()
        self._lock = threading.RLock()

        logger.info("MultiLayerCache initialized")

    async def get(self, key: str, warm_lower_layers: bool = True) -> Optional[Any]:
        """Get value from multi-layer cache with intelligent warming."""
        # Try L1 first
        value = await self.l1.get(key)
        if value is not None:
            record_timer("cache_get_latency", 1, {"layer": "l1", "hit": "true"})
            return value

        # Try L2
        value = await self.l2.get(key)
        if value is not None:
            record_timer("cache_get_latency", 5, {"layer": "l2", "hit": "true"})

            # Warm L1 if requested
            if warm_lower_layers:
                await self.l1.set(key, value)

            return value

        # Try L3
        value = await self.l3.get(key)
        if value is not None:
            record_timer("cache_get_latency", 10, {"layer": "l3", "hit": "true"})

            # Warm L2 and L1 if requested
            if warm_lower_layers:
                await self.l2.set(key, value)
                await self.l1.set(key, value)

            return value

        # Cache miss across all layers
        record_timer("cache_get_latency", 15, {"layer": "all", "hit": "false"})
        return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None,
                  tags: Optional[Set[str]] = None, layers: Optional[List[CacheLayer]] = None) -> bool:
        """Set value in specified cache layers."""
        if layers is None:
            layers = [CacheLayer.L1_MEMORY, CacheLayer.L2_REDIS, CacheLayer.L3_DATABASE]

        success = True

        for layer in layers:
            try:
                if layer == CacheLayer.L1_MEMORY:
                    await self.l1.set(key, value, ttl, tags)
                elif layer == CacheLayer.L2_REDIS:
                    await self.l2.set(key, value, ttl, tags)
                elif layer == CacheLayer.L3_DATABASE:
                    await self.l3.set(key, value, ttl, tags)
            except Exception as e:
                logger.error("Cache set failed", layer=layer.value, key=key, error=str(e))
                success = False

        return success

    async def delete(self, key: str, layers: Optional[List[CacheLayer]] = None) -> bool:
        """Delete value from specified cache layers."""
        if layers is None:
            layers = [CacheLayer.L1_MEMORY, CacheLayer.L2_REDIS, CacheLayer.L3_DATABASE]

        success = True

        for layer in layers:
            try:
                if layer == CacheLayer.L1_MEMORY:
                    await self.l1.delete(key)
                elif layer == CacheLayer.L2_REDIS:
                    await self.l2.delete(key)
                elif layer == CacheLayer.L3_DATABASE:
                    await self.l3.delete(key)
            except Exception as e:
                logger.error("Cache delete failed", layer=layer.value, key=key, error=str(e))
                success = False

        return success

    async def invalidate_by_tags(self, tags: Set[str]) -> Dict[str, int]:
        """Invalidate entries by tags across all layers."""
        results = {}

        try:
            results["l1"] = await self.l1.invalidate_by_tags(tags)
        except Exception as e:
            logger.error("L1 tag invalidation failed", tags=list(tags), error=str(e))
            results["l1"] = 0

        try:
            results["l2"] = await self.l2.invalidate_by_tags(tags)
        except Exception as e:
            logger.error("L2 tag invalidation failed", tags=list(tags), error=str(e))
            results["l2"] = 0

        try:
            results["l3"] = await self.l3.invalidate_by_tags(tags)
        except Exception as e:
            logger.error("L3 tag invalidation failed", tags=list(tags), error=str(e))
            results["l3"] = 0

        total_invalidated = sum(results.values())
        increment_counter("cache_invalidations", total_invalidated, {"method": "tags"})

        return results

    async def warm_cache(self, key: str, value_generator: Callable[[], Any],
                        ttl: Optional[int] = None, tags: Optional[Set[str]] = None) -> bool:
        """Warm cache with generated value."""
        with self._lock:
            if key in self.warming_tasks:
                return False  # Already warming

            self.warming_tasks.add(key)

        try:
            # Generate value
            if asyncio.iscoroutinefunction(value_generator):
                value = await value_generator()
            else:
                value = value_generator()

            # Set in all layers
            success = await self.set(key, value, ttl, tags)

            increment_counter("cache_warming", 1, {"success": str(success)})

            return success

        except Exception as e:
            logger.error("Cache warming failed", key=key, error=str(e))
            return False
        finally:
            with self._lock:
                self.warming_tasks.discard(key)

    async def batch_warm(self, warm_requests: List[Dict[str, Any]]) -> Dict[str, bool]:
        """Batch warm multiple cache entries."""
        tasks = []
        keys = []

        for request in warm_requests:
            key = request["key"]
            value_generator = request["value_generator"]
            ttl = request.get("ttl")
            tags = request.get("tags")

            task = self.warm_cache(key, value_generator, ttl, tags)
            tasks.append(task)
            keys.append(key)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        return {
            key: result if not isinstance(result, Exception) else False
            for key, result in zip(keys, results)
        }

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics across all cache layers."""
        l1_stats = self.l1.get_stats()
        l2_stats = self.l2.get_stats()
        l3_stats = self.l3.get_stats()

        total_hits = l1_stats.hits + l2_stats.hits + l3_stats.hits
        total_misses = l1_stats.misses + l2_stats.misses + l3_stats.misses
        total_requests = total_hits + total_misses

        return {
            "overall": {
                "total_requests": total_requests,
                "total_hits": total_hits,
                "total_misses": total_misses,
                "hit_rate": total_hits / total_requests if total_requests > 0 else 0.0,
                "warming_tasks": len(self.warming_tasks)
            },
            "l1_memory": {
                "hits": l1_stats.hits,
                "misses": l1_stats.misses,
                "hit_rate": l1_stats.hit_rate,
                "size": l1_stats.size,
                "memory_usage_mb": l1_stats.memory_usage / (1024 * 1024),
                "evictions": l1_stats.evictions
            },
            "l2_redis": {
                "hits": l2_stats.hits,
                "misses": l2_stats.misses,
                "hit_rate": l2_stats.hit_rate,
                "size": l2_stats.size
            },
            "l3_database": {
                "hits": l3_stats.hits,
                "misses": l3_stats.misses,
                "hit_rate": l3_stats.hit_rate,
                "size": l3_stats.size
            }
        }


class CacheWarmer:
    """Intelligent cache warming system."""

    def __init__(self, cache: MultiLayerCache):
        self.cache = cache
        self.warming_patterns: Dict[str, Dict[str, Any]] = {}
        self.access_patterns: Dict[str, List[datetime]] = defaultdict(list)
        self._lock = threading.RLock()

        logger.info("CacheWarmer initialized")

    def register_warming_pattern(self, pattern_name: str, key_generator: Callable[[], List[str]],
                                value_generator: Callable[[str], Any],
                                schedule: Dict[str, Any]):
        """Register a cache warming pattern."""
        with self._lock:
            self.warming_patterns[pattern_name] = {
                "key_generator": key_generator,
                "value_generator": value_generator,
                "schedule": schedule,
                "last_run": None
            }

        logger.info("Cache warming pattern registered", pattern=pattern_name)

    def record_access(self, key: str):
        """Record cache access for pattern analysis."""
        with self._lock:
            now = datetime.now(timezone.utc)
            self.access_patterns[key].append(now)

            # Keep only recent accesses (last 24 hours)
            cutoff = now - timedelta(hours=24)
            self.access_patterns[key] = [
                access_time for access_time in self.access_patterns[key]
                if access_time > cutoff
            ]

    async def run_warming_cycle(self):
        """Run cache warming cycle based on patterns."""
        for pattern_name, pattern in self.warming_patterns.items():
            try:
                await self._execute_warming_pattern(pattern_name, pattern)
            except Exception as e:
                logger.error("Cache warming pattern failed",
                           pattern=pattern_name,
                           error=str(e))

    async def _execute_warming_pattern(self, pattern_name: str, pattern: Dict[str, Any]):
        """Execute a specific warming pattern."""
        schedule = pattern["schedule"]
        last_run = pattern.get("last_run")

        # Check if warming is due
        if last_run:
            interval_minutes = schedule.get("interval_minutes", 60)
            next_run = last_run + timedelta(minutes=interval_minutes)
            if datetime.now(timezone.utc) < next_run:
                return

        # Generate keys to warm
        key_generator = pattern["key_generator"]
        value_generator = pattern["value_generator"]

        try:
            keys_to_warm = key_generator()
        except Exception as e:
            logger.error("Key generation failed", pattern=pattern_name, error=str(e))
            return

        # Prioritize keys based on access patterns
        prioritized_keys = self._prioritize_keys(keys_to_warm)

        # Limit warming batch size
        max_batch_size = schedule.get("max_batch_size", 100)
        keys_to_warm = prioritized_keys[:max_batch_size]

        # Prepare warming requests
        warm_requests = []
        for key in keys_to_warm:
            warm_requests.append({
                "key": key,
                "value_generator": lambda k=key: value_generator(k),
                "ttl": schedule.get("ttl"),
                "tags": schedule.get("tags")
            })

        # Execute batch warming
        results = await self.cache.batch_warm(warm_requests)

        # Update pattern metadata
        with self._lock:
            pattern["last_run"] = datetime.now(timezone.utc)

        successful_warms = sum(1 for success in results.values() if success)

        logger.info("Cache warming completed",
                   pattern=pattern_name,
                   keys_warmed=successful_warms,
                   total_keys=len(keys_to_warm))

    def _prioritize_keys(self, keys: List[str]) -> List[str]:
        """Prioritize keys based on access patterns."""
        key_scores = []

        for key in keys:
            accesses = self.access_patterns.get(key, [])

            # Calculate score based on recent access frequency
            recent_accesses = len(accesses)
            last_access = max(accesses) if accesses else datetime.min.replace(tzinfo=timezone.utc)
            time_since_access = (datetime.now(timezone.utc) - last_access).total_seconds()

            # Higher score = higher priority
            score = recent_accesses / (time_since_access / 3600 + 1)  # Normalize by hours

            key_scores.append((key, score))

        # Sort by score (descending)
        key_scores.sort(key=lambda x: x[1], reverse=True)

        return [key for key, score in key_scores]


# Global cache instances
l1_cache = L1MemoryCache(max_size=10000, max_memory_mb=100)
l2_cache = L2RedisCache()
l3_cache = L3DatabaseCache()
multi_layer_cache = MultiLayerCache(l1_cache, l2_cache, l3_cache)
cache_warmer = CacheWarmer(multi_layer_cache)


def get_cache_status() -> Dict[str, Any]:
    """Get comprehensive cache system status."""
    return {
        "multi_layer_stats": multi_layer_cache.get_comprehensive_stats(),
        "warming_patterns": len(cache_warmer.warming_patterns),
        "active_warming_tasks": len(multi_layer_cache.warming_tasks),
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
