"""
Compliance Monitoring Automation System

This module implements automated compliance checking with real-time monitoring
and intelligent compliance management. It provides continuous compliance validation,
automated remediation, and proactive compliance risk management.

Features:
- Automated compliance rule checking
- Real-time compliance monitoring
- Intelligent compliance risk assessment
- Automated remediation workflows
- Compliance drift detection
- Regulatory change monitoring
- Automated compliance reporting
- Compliance dashboard with alerts
"""

import asyncio
import json
import logging
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
import threading
import uuid

import structlog

from ..core.config import config
from ..core.logging_config import get_logger
from .audit_framework import (
    ComplianceStandard, DataCategory, AuditEventType, RiskLevel,
    AuditTrail, GDPRComplianceManager, ComplianceViolationManager,
    ComplianceReportGenerator, audit_trail, gdpr_manager, violation_manager
)
from ..monitoring.dashboard import metrics_collector, alert_manager, AlertSeverity

logger = get_logger(__name__)


class ComplianceRuleType(Enum):
    """Types of compliance rules."""
    DATA_RETENTION = "data_retention"
    ACCESS_CONTROL = "access_control"
    CONSENT_MANAGEMENT = "consent_management"
    DATA_ENCRYPTION = "data_encryption"
    AUDIT_LOGGING = "audit_logging"
    INCIDENT_RESPONSE = "incident_response"
    PRIVACY_BY_DESIGN = "privacy_by_design"
    DATA_MINIMIZATION = "data_minimization"


class ComplianceCheckStatus(Enum):
    """Status of compliance checks."""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    WARNING = "warning"
    UNKNOWN = "unknown"
    REMEDIATED = "remediated"


class RemediationAction(Enum):
    """Types of automated remediation actions."""
    DELETE_EXPIRED_DATA = "delete_expired_data"
    REVOKE_ACCESS = "revoke_access"
    ENCRYPT_DATA = "encrypt_data"
    NOTIFY_ADMIN = "notify_admin"
    GENERATE_REPORT = "generate_report"
    UPDATE_CONSENT = "update_consent"
    QUARANTINE_DATA = "quarantine_data"


@dataclass
class ComplianceRule:
    """Definition of a compliance rule."""
    rule_id: str
    rule_type: ComplianceRuleType
    standard: ComplianceStandard
    name: str
    description: str
    check_function: str  # Name of the function to execute
    severity: RiskLevel
    auto_remediation: bool = False
    remediation_actions: List[RemediationAction] = field(default_factory=list)
    check_interval_hours: int = 24
    enabled: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ComplianceCheckResult:
    """Result of a compliance rule check."""
    rule_id: str
    check_timestamp: datetime
    status: ComplianceCheckStatus
    details: Dict[str, Any]
    violations_found: int = 0
    remediation_applied: bool = False
    remediation_details: Optional[Dict[str, Any]] = None
    next_check_time: Optional[datetime] = None


@dataclass
class ComplianceAlert:
    """Compliance-related alert."""
    alert_id: str
    rule_id: str
    alert_type: str
    severity: AlertSeverity
    message: str
    timestamp: datetime
    affected_resources: List[str] = field(default_factory=list)
    remediation_required: bool = False
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class ComplianceMonitoringAutomation:
    """
    Automated compliance monitoring and management system.
    
    Provides continuous compliance validation, automated remediation,
    and intelligent compliance risk management.
    """
    
    def __init__(self, audit_trail: AuditTrail, gdpr_manager: GDPRComplianceManager,
                 violation_manager: ComplianceViolationManager):
        self.audit_trail = audit_trail
        self.gdpr_manager = gdpr_manager
        self.violation_manager = violation_manager
        
        self.compliance_rules: Dict[str, ComplianceRule] = {}
        self.check_results: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.compliance_alerts: Dict[str, ComplianceAlert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        self.remediation_history: deque = deque(maxlen=1000)
        self._lock = threading.RLock()
        
        # Monitoring configuration
        self.monitoring_enabled = True
        self.check_interval_seconds = 3600  # Check every hour
        self.alert_cooldown_minutes = 30    # Prevent alert spam
        
        # Statistics
        self.stats = {
            "total_checks_performed": 0,
            "compliance_violations_detected": 0,
            "automated_remediations_applied": 0,
            "compliance_alerts_triggered": 0,
            "last_monitoring_cycle": None
        }
        
        # Initialize default compliance rules
        self._initialize_default_rules()
        
        logger.info("ComplianceMonitoringAutomation initialized")
    
    def _initialize_default_rules(self):
        """Initialize default compliance rules."""
        default_rules = [
            ComplianceRule(
                rule_id="gdpr_data_retention",
                rule_type=ComplianceRuleType.DATA_RETENTION,
                standard=ComplianceStandard.GDPR,
                name="GDPR Data Retention Compliance",
                description="Ensure personal data is not retained beyond legal limits",
                check_function="check_data_retention_compliance",
                severity=RiskLevel.HIGH,
                auto_remediation=True,
                remediation_actions=[RemediationAction.DELETE_EXPIRED_DATA, RemediationAction.NOTIFY_ADMIN],
                check_interval_hours=24
            ),
            ComplianceRule(
                rule_id="gdpr_consent_management",
                rule_type=ComplianceRuleType.CONSENT_MANAGEMENT,
                standard=ComplianceStandard.GDPR,
                name="GDPR Consent Management",
                description="Verify all data processing has valid consent",
                check_function="check_consent_compliance",
                severity=RiskLevel.HIGH,
                auto_remediation=False,
                remediation_actions=[RemediationAction.NOTIFY_ADMIN],
                check_interval_hours=12
            ),
            ComplianceRule(
                rule_id="audit_log_integrity",
                rule_type=ComplianceRuleType.AUDIT_LOGGING,
                standard=ComplianceStandard.SOX,
                name="Audit Log Integrity",
                description="Ensure audit logs are complete and tamper-proof",
                check_function="check_audit_log_integrity",
                severity=RiskLevel.CRITICAL,
                auto_remediation=False,
                remediation_actions=[RemediationAction.NOTIFY_ADMIN, RemediationAction.GENERATE_REPORT],
                check_interval_hours=6
            ),
            ComplianceRule(
                rule_id="access_control_review",
                rule_type=ComplianceRuleType.ACCESS_CONTROL,
                standard=ComplianceStandard.SOC2,
                name="Access Control Review",
                description="Review and validate user access permissions",
                check_function="check_access_control_compliance",
                severity=RiskLevel.MEDIUM,
                auto_remediation=False,
                remediation_actions=[RemediationAction.NOTIFY_ADMIN],
                check_interval_hours=168  # Weekly
            ),
            ComplianceRule(
                rule_id="data_encryption_compliance",
                rule_type=ComplianceRuleType.DATA_ENCRYPTION,
                standard=ComplianceStandard.PCI_DSS,
                name="Data Encryption Compliance",
                description="Ensure sensitive data is properly encrypted",
                check_function="check_data_encryption_compliance",
                severity=RiskLevel.HIGH,
                auto_remediation=True,
                remediation_actions=[RemediationAction.ENCRYPT_DATA, RemediationAction.NOTIFY_ADMIN],
                check_interval_hours=24
            )
        ]
        
        for rule in default_rules:
            self.compliance_rules[rule.rule_id] = rule
        
        logger.info(f"Initialized {len(default_rules)} default compliance rules")
    
    async def perform_compliance_check(self, rule_id: str) -> ComplianceCheckResult:
        """Perform compliance check for a specific rule."""
        try:
            with self._lock:
                if rule_id not in self.compliance_rules:
                    raise ValueError(f"Unknown compliance rule: {rule_id}")
                
                rule = self.compliance_rules[rule_id]
                if not rule.enabled:
                    logger.debug(f"Skipping disabled rule: {rule_id}")
                    return ComplianceCheckResult(
                        rule_id=rule_id,
                        check_timestamp=datetime.now(timezone.utc),
                        status=ComplianceCheckStatus.UNKNOWN,
                        details={"reason": "rule_disabled"}
                    )
                
                self.stats["total_checks_performed"] += 1
            
            # Execute the compliance check
            check_result = await self._execute_compliance_check(rule)
            
            # Store result
            with self._lock:
                self.check_results[rule_id].append(check_result)
            
            # Handle non-compliance
            if check_result.status == ComplianceCheckStatus.NON_COMPLIANT:
                await self._handle_non_compliance(rule, check_result)
            
            # Update metrics
            metrics_collector.increment_counter("compliance_checks_total", 1, {
                "rule_id": rule_id,
                "status": check_result.status.value
            })
            
            logger.info("Compliance check completed",
                       rule_id=rule_id,
                       status=check_result.status.value,
                       violations=check_result.violations_found)
            
            return check_result
            
        except Exception as e:
            logger.error(f"Failed to perform compliance check for rule {rule_id}: {e}")
            return ComplianceCheckResult(
                rule_id=rule_id,
                check_timestamp=datetime.now(timezone.utc),
                status=ComplianceCheckStatus.UNKNOWN,
                details={"error": str(e)}
            )
    
    async def _execute_compliance_check(self, rule: ComplianceRule) -> ComplianceCheckResult:
        """Execute the actual compliance check based on rule type."""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Route to appropriate check function
            if rule.check_function == "check_data_retention_compliance":
                result = await self._check_data_retention_compliance(rule)
            elif rule.check_function == "check_consent_compliance":
                result = await self._check_consent_compliance(rule)
            elif rule.check_function == "check_audit_log_integrity":
                result = await self._check_audit_log_integrity(rule)
            elif rule.check_function == "check_access_control_compliance":
                result = await self._check_access_control_compliance(rule)
            elif rule.check_function == "check_data_encryption_compliance":
                result = await self._check_data_encryption_compliance(rule)
            else:
                result = ComplianceCheckResult(
                    rule_id=rule.rule_id,
                    check_timestamp=current_time,
                    status=ComplianceCheckStatus.UNKNOWN,
                    details={"error": f"Unknown check function: {rule.check_function}"}
                )
            
            # Set next check time
            result.next_check_time = current_time + timedelta(hours=rule.check_interval_hours)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute compliance check: {e}")
            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=datetime.now(timezone.utc),
                status=ComplianceCheckStatus.UNKNOWN,
                details={"error": str(e)}
            )
    
    async def _check_data_retention_compliance(self, rule: ComplianceRule) -> ComplianceCheckResult:
        """Check GDPR data retention compliance."""
        try:
            current_time = datetime.now(timezone.utc)
            violations = self.gdpr_manager.check_retention_compliance()
            
            status = ComplianceCheckStatus.COMPLIANT if len(violations) == 0 else ComplianceCheckStatus.NON_COMPLIANT
            
            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=current_time,
                status=status,
                details={
                    "violations_found": violations,
                    "total_data_subjects": len(self.gdpr_manager.data_subjects),
                    "check_type": "data_retention"
                },
                violations_found=len(violations)
            )
            
        except Exception as e:
            logger.error(f"Failed to check data retention compliance: {e}")
            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=datetime.now(timezone.utc),
                status=ComplianceCheckStatus.UNKNOWN,
                details={"error": str(e)}
            )
    
    async def _check_consent_compliance(self, rule: ComplianceRule) -> ComplianceCheckResult:
        """Check GDPR consent management compliance."""
        try:
            current_time = datetime.now(timezone.utc)
            violations = []
            
            # Check for data subjects without proper consent
            for subject_id, subject in self.gdpr_manager.data_subjects.items():
                if subject_id not in self.gdpr_manager.consent_records:
                    violations.append(f"No consent record for data subject: {subject_id}")
                else:
                    consent_record = self.gdpr_manager.consent_records[subject_id]
                    if not consent_record.get("marketing_consent", False) and subject.data_categories:
                        # Check if marketing data is being processed without consent
                        if DataCategory.BEHAVIORAL_DATA in subject.data_categories:
                            violations.append(f"Behavioral data processed without marketing consent: {subject_id}")
            
            status = ComplianceCheckStatus.COMPLIANT if len(violations) == 0 else ComplianceCheckStatus.NON_COMPLIANT
            
            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=current_time,
                status=status,
                details={
                    "violations_found": violations,
                    "total_consent_records": len(self.gdpr_manager.consent_records),
                    "check_type": "consent_management"
                },
                violations_found=len(violations)
            )
            
        except Exception as e:
            logger.error(f"Failed to check consent compliance: {e}")
            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=datetime.now(timezone.utc),
                status=ComplianceCheckStatus.UNKNOWN,
                details={"error": str(e)}
            )

    async def _check_audit_log_integrity(self, rule: ComplianceRule) -> ComplianceCheckResult:
        """Check audit log integrity compliance."""
        try:
            current_time = datetime.now(timezone.utc)
            violations = []

            # Check audit trail integrity
            integrity_verified = self.audit_trail.verify_integrity()
            if not integrity_verified:
                violations.append("Audit trail integrity verification failed")

            # Check for recent audit events
            recent_events = [e for e in self.audit_trail.events if (current_time - e.timestamp).days < 1]
            if len(recent_events) == 0:
                violations.append("No audit events recorded in the last 24 hours")

            status = ComplianceCheckStatus.COMPLIANT if len(violations) == 0 else ComplianceCheckStatus.NON_COMPLIANT

            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=current_time,
                status=status,
                details={
                    "violations_found": violations,
                    "total_audit_events": len(self.audit_trail.events),
                    "recent_events": len(recent_events),
                    "integrity_verified": integrity_verified,
                    "check_type": "audit_log_integrity"
                },
                violations_found=len(violations)
            )

        except Exception as e:
            logger.error(f"Failed to check audit log integrity: {e}")
            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=datetime.now(timezone.utc),
                status=ComplianceCheckStatus.UNKNOWN,
                details={"error": str(e)}
            )

    async def _check_access_control_compliance(self, rule: ComplianceRule) -> ComplianceCheckResult:
        """Check access control compliance."""
        try:
            current_time = datetime.now(timezone.utc)
            violations = []

            # Check for suspicious access patterns in audit logs
            recent_events = [e for e in self.audit_trail.events if (current_time - e.timestamp).days < 7]
            access_events = [e for e in recent_events if e.event_type == AuditEventType.DATA_ACCESS]

            # Group by user and check for unusual patterns
            user_access = defaultdict(list)
            for event in access_events:
                if event.user_id:
                    user_access[event.user_id].append(event)

            for user_id, events in user_access.items():
                # Check for excessive access
                if len(events) > 100:  # More than 100 accesses per week
                    violations.append(f"User {user_id} has excessive access activity: {len(events)} events")

            status = ComplianceCheckStatus.COMPLIANT if len(violations) == 0 else ComplianceCheckStatus.WARNING

            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=current_time,
                status=status,
                details={
                    "violations_found": violations,
                    "total_access_events": len(access_events),
                    "unique_users": len(user_access),
                    "check_type": "access_control"
                },
                violations_found=len(violations)
            )

        except Exception as e:
            logger.error(f"Failed to check access control compliance: {e}")
            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=datetime.now(timezone.utc),
                status=ComplianceCheckStatus.UNKNOWN,
                details={"error": str(e)}
            )

    async def _check_data_encryption_compliance(self, rule: ComplianceRule) -> ComplianceCheckResult:
        """Check data encryption compliance."""
        try:
            current_time = datetime.now(timezone.utc)
            violations = []

            # Check for unencrypted sensitive data access
            recent_events = [e for e in self.audit_trail.events if (current_time - e.timestamp).days < 1]

            for event in recent_events:
                if event.event_type == AuditEventType.DATA_ACCESS:
                    details = event.details or {}
                    if "data_category" in details:
                        category = details["data_category"]
                        if category in ["financial_data", "health_data", "biometric_data"]:
                            if "encrypted" not in details or not details.get("encrypted", False):
                                violations.append(f"Sensitive data access without encryption verification: {event.id}")

            status = ComplianceCheckStatus.COMPLIANT if len(violations) == 0 else ComplianceCheckStatus.NON_COMPLIANT

            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=current_time,
                status=status,
                details={
                    "violations_found": violations,
                    "sensitive_data_accesses": len([e for e in recent_events if e.event_type == AuditEventType.DATA_ACCESS]),
                    "check_type": "data_encryption"
                },
                violations_found=len(violations)
            )

        except Exception as e:
            logger.error(f"Failed to check data encryption compliance: {e}")
            return ComplianceCheckResult(
                rule_id=rule.rule_id,
                check_timestamp=datetime.now(timezone.utc),
                status=ComplianceCheckStatus.UNKNOWN,
                details={"error": str(e)}
            )

    async def start_monitoring(self):
        """Start automated compliance monitoring loop."""
        logger.info("Starting automated compliance monitoring")

        while self.monitoring_enabled:
            try:
                current_time = datetime.now(timezone.utc)

                # Check all enabled rules
                for rule_id, rule in self.compliance_rules.items():
                    if not rule.enabled:
                        continue

                    # Check if it's time for this rule
                    last_check = None
                    if rule_id in self.check_results and self.check_results[rule_id]:
                        last_check = self.check_results[rule_id][-1]

                    should_check = (
                        last_check is None or
                        last_check.next_check_time is None or
                        current_time >= last_check.next_check_time
                    )

                    if should_check:
                        logger.debug(f"Performing compliance check for rule: {rule_id}")
                        await self.perform_compliance_check(rule_id)

                with self._lock:
                    self.stats["last_monitoring_cycle"] = current_time

                # Update metrics
                metrics_collector.set_gauge("compliance_monitoring_active", 1)
                metrics_collector.set_gauge("compliance_rules_total", len(self.compliance_rules))
                metrics_collector.set_gauge("compliance_violations_total", self.stats["compliance_violations_detected"])

                # Wait for next monitoring cycle
                await asyncio.sleep(self.check_interval_seconds)

            except Exception as e:
                logger.error(f"Error in compliance monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry

    def generate_compliance_automation_report(self) -> Dict[str, Any]:
        """Generate comprehensive compliance automation report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Basic statistics
                report = {
                    "report_generated_at": current_time.isoformat(),
                    "summary": {
                        "total_rules": len(self.compliance_rules),
                        "enabled_rules": len([r for r in self.compliance_rules.values() if r.enabled]),
                        "total_checks_performed": self.stats["total_checks_performed"],
                        "compliance_violations_detected": self.stats["compliance_violations_detected"],
                        "automated_remediations_applied": self.stats["automated_remediations_applied"],
                        "compliance_alerts_triggered": self.stats["compliance_alerts_triggered"],
                        "last_monitoring_cycle": self.stats["last_monitoring_cycle"].isoformat() if self.stats["last_monitoring_cycle"] else None
                    }
                }

                # Rule status
                rule_status = {}
                for rule_id, rule in self.compliance_rules.items():
                    latest_result = None
                    if rule_id in self.check_results and self.check_results[rule_id]:
                        latest_result = self.check_results[rule_id][-1]

                    rule_status[rule_id] = {
                        "name": rule.name,
                        "enabled": rule.enabled,
                        "standard": rule.standard.value,
                        "severity": rule.severity.value,
                        "auto_remediation": rule.auto_remediation,
                        "last_check": latest_result.check_timestamp.isoformat() if latest_result else None,
                        "last_status": latest_result.status.value if latest_result else "never_checked",
                        "violations_found": latest_result.violations_found if latest_result else 0
                    }

                report["rule_status"] = rule_status

                # Active alerts
                active_alerts = []
                for alert in self.compliance_alerts.values():
                    if not alert.resolved:
                        active_alerts.append({
                            "alert_id": alert.alert_id,
                            "rule_id": alert.rule_id,
                            "severity": alert.severity.value,
                            "message": alert.message,
                            "timestamp": alert.timestamp.isoformat(),
                            "remediation_required": alert.remediation_required
                        })

                report["active_alerts"] = active_alerts

                # Recent remediation history
                recent_remediations = list(self.remediation_history)[-10:]  # Last 10 remediations
                report["recent_remediations"] = [
                    {
                        "timestamp": r["timestamp"].isoformat(),
                        "rule_id": r["rule_id"],
                        "actions": r["actions"]
                    }
                    for r in recent_remediations
                ]

                # Compliance recommendations
                report["recommendations"] = self._generate_compliance_recommendations()

                return report

        except Exception as e:
            logger.error(f"Failed to generate compliance automation report: {e}")
            return {"error": str(e)}

    def _generate_compliance_recommendations(self) -> List[str]:
        """Generate compliance automation recommendations."""
        recommendations = []

        try:
            # Check for rules with frequent violations
            for rule_id, results in self.check_results.items():
                if len(results) >= 5:
                    recent_results = list(results)[-5:]
                    violation_count = sum(1 for r in recent_results if r.status == ComplianceCheckStatus.NON_COMPLIANT)
                    if violation_count >= 3:
                        rule = self.compliance_rules[rule_id]
                        recommendations.append(f"Rule '{rule.name}' has frequent violations - consider reviewing controls")

            # Check for disabled rules
            disabled_rules = [r for r in self.compliance_rules.values() if not r.enabled]
            if disabled_rules:
                recommendations.append(f"{len(disabled_rules)} compliance rules are disabled - review if they should be re-enabled")

            # Check for rules without auto-remediation
            manual_rules = [r for r in self.compliance_rules.values() if not r.auto_remediation and r.severity in [RiskLevel.HIGH, RiskLevel.CRITICAL]]
            if manual_rules:
                recommendations.append(f"{len(manual_rules)} high-severity rules lack auto-remediation - consider enabling automation")

            # Check monitoring frequency
            if self.stats["last_monitoring_cycle"]:
                time_since_last = (datetime.now(timezone.utc) - self.stats["last_monitoring_cycle"]).total_seconds() / 3600
                if time_since_last > 2:  # More than 2 hours
                    recommendations.append("Compliance monitoring cycle delayed - check system health")

            if not recommendations:
                recommendations.append("Compliance automation is operating optimally")

        except Exception as e:
            logger.error(f"Failed to generate compliance recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations


# Global instance
compliance_automation = ComplianceMonitoringAutomation(audit_trail, gdpr_manager, violation_manager)
