"""
Container Orchestration Readiness
Kubernetes deployment configurations, health checks, and service discovery.
"""

import asyncio
import json
import os
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union
import threading

import structlog

from ..core.logging_config import get_logger
from ..monitoring import increment_counter, set_gauge, get_dashboard
from ..core.resilience import get_system_health


logger = get_logger(__name__)


class DeploymentEnvironment(Enum):
    """Deployment environments."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class ServiceType(Enum):
    """Service types."""
    API = "api"
    WORKER = "worker"
    SCHEDULER = "scheduler"
    DATABASE = "database"
    CACHE = "cache"
    MONITORING = "monitoring"


@dataclass
class HealthCheck:
    """Health check configuration."""
    path: str
    port: int
    interval_seconds: int = 30
    timeout_seconds: int = 5
    failure_threshold: int = 3
    success_threshold: int = 1
    initial_delay_seconds: int = 10
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "path": self.path,
            "port": self.port,
            "interval_seconds": self.interval_seconds,
            "timeout_seconds": self.timeout_seconds,
            "failure_threshold": self.failure_threshold,
            "success_threshold": self.success_threshold,
            "initial_delay_seconds": self.initial_delay_seconds
        }


@dataclass
class ResourceRequirements:
    """Resource requirements for containers."""
    cpu_request: str = "100m"
    cpu_limit: str = "500m"
    memory_request: str = "128Mi"
    memory_limit: str = "512Mi"
    storage_request: str = "1Gi"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "requests": {
                "cpu": self.cpu_request,
                "memory": self.memory_request,
                "storage": self.storage_request
            },
            "limits": {
                "cpu": self.cpu_limit,
                "memory": self.memory_limit
            }
        }


@dataclass
class ServiceConfiguration:
    """Service configuration for container orchestration."""
    name: str
    service_type: ServiceType
    image: str
    port: int
    environment: DeploymentEnvironment
    replicas: int = 1
    health_check: Optional[HealthCheck] = None
    resources: Optional[ResourceRequirements] = None
    environment_variables: Dict[str, str] = field(default_factory=dict)
    volumes: List[Dict[str, str]] = field(default_factory=list)
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "name": self.name,
            "service_type": self.service_type.value,
            "image": self.image,
            "port": self.port,
            "environment": self.environment.value,
            "replicas": self.replicas,
            "health_check": self.health_check.to_dict() if self.health_check else None,
            "resources": self.resources.to_dict() if self.resources else None,
            "environment_variables": self.environment_variables,
            "volumes": self.volumes,
            "labels": self.labels,
            "annotations": self.annotations
        }


class KubernetesManifestGenerator:
    """Generates Kubernetes deployment manifests."""
    
    def __init__(self):
        self.namespace = "crypto-analytics"
        self.app_name = "token-analysis-platform"
        
        logger.info("KubernetesManifestGenerator initialized")
    
    def generate_deployment(self, service_config: ServiceConfiguration) -> Dict[str, Any]:
        """Generate Kubernetes Deployment manifest."""
        deployment = {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": f"{service_config.name}-deployment",
                "namespace": self.namespace,
                "labels": {
                    "app": self.app_name,
                    "service": service_config.name,
                    "type": service_config.service_type.value,
                    "environment": service_config.environment.value,
                    **service_config.labels
                },
                "annotations": {
                    "deployment.kubernetes.io/revision": "1",
                    **service_config.annotations
                }
            },
            "spec": {
                "replicas": service_config.replicas,
                "selector": {
                    "matchLabels": {
                        "app": self.app_name,
                        "service": service_config.name
                    }
                },
                "template": {
                    "metadata": {
                        "labels": {
                            "app": self.app_name,
                            "service": service_config.name,
                            "type": service_config.service_type.value,
                            **service_config.labels
                        }
                    },
                    "spec": {
                        "containers": [
                            {
                                "name": service_config.name,
                                "image": service_config.image,
                                "ports": [
                                    {
                                        "containerPort": service_config.port,
                                        "protocol": "TCP"
                                    }
                                ],
                                "env": [
                                    {"name": key, "value": value}
                                    for key, value in service_config.environment_variables.items()
                                ]
                            }
                        ]
                    }
                }
            }
        }
        
        # Add resource requirements
        if service_config.resources:
            deployment["spec"]["template"]["spec"]["containers"][0]["resources"] = service_config.resources.to_dict()
        
        # Add health checks
        if service_config.health_check:
            health_check = service_config.health_check
            
            # Liveness probe
            deployment["spec"]["template"]["spec"]["containers"][0]["livenessProbe"] = {
                "httpGet": {
                    "path": health_check.path,
                    "port": health_check.port
                },
                "initialDelaySeconds": health_check.initial_delay_seconds,
                "periodSeconds": health_check.interval_seconds,
                "timeoutSeconds": health_check.timeout_seconds,
                "failureThreshold": health_check.failure_threshold
            }
            
            # Readiness probe
            deployment["spec"]["template"]["spec"]["containers"][0]["readinessProbe"] = {
                "httpGet": {
                    "path": health_check.path,
                    "port": health_check.port
                },
                "initialDelaySeconds": 5,
                "periodSeconds": 10,
                "timeoutSeconds": health_check.timeout_seconds,
                "successThreshold": health_check.success_threshold,
                "failureThreshold": health_check.failure_threshold
            }
        
        # Add volumes
        if service_config.volumes:
            deployment["spec"]["template"]["spec"]["volumes"] = service_config.volumes
            deployment["spec"]["template"]["spec"]["containers"][0]["volumeMounts"] = [
                {
                    "name": volume["name"],
                    "mountPath": volume["mountPath"]
                }
                for volume in service_config.volumes
            ]
        
        return deployment
    
    def generate_service(self, service_config: ServiceConfiguration) -> Dict[str, Any]:
        """Generate Kubernetes Service manifest."""
        service = {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": f"{service_config.name}-service",
                "namespace": self.namespace,
                "labels": {
                    "app": self.app_name,
                    "service": service_config.name,
                    **service_config.labels
                }
            },
            "spec": {
                "selector": {
                    "app": self.app_name,
                    "service": service_config.name
                },
                "ports": [
                    {
                        "port": service_config.port,
                        "targetPort": service_config.port,
                        "protocol": "TCP"
                    }
                ],
                "type": "ClusterIP"
            }
        }
        
        return service
    
    def generate_configmap(self, name: str, data: Dict[str, str]) -> Dict[str, Any]:
        """Generate Kubernetes ConfigMap manifest."""
        configmap = {
            "apiVersion": "v1",
            "kind": "ConfigMap",
            "metadata": {
                "name": f"{name}-config",
                "namespace": self.namespace,
                "labels": {
                    "app": self.app_name
                }
            },
            "data": data
        }
        
        return configmap
    
    def generate_secret(self, name: str, data: Dict[str, str]) -> Dict[str, Any]:
        """Generate Kubernetes Secret manifest."""
        import base64
        
        secret = {
            "apiVersion": "v1",
            "kind": "Secret",
            "metadata": {
                "name": f"{name}-secret",
                "namespace": self.namespace,
                "labels": {
                    "app": self.app_name
                }
            },
            "type": "Opaque",
            "data": {
                key: base64.b64encode(value.encode()).decode()
                for key, value in data.items()
            }
        }
        
        return secret
    
    def generate_ingress(self, service_configs: List[ServiceConfiguration], 
                        domain: str = "crypto-analytics.local") -> Dict[str, Any]:
        """Generate Kubernetes Ingress manifest."""
        ingress = {
            "apiVersion": "networking.k8s.io/v1",
            "kind": "Ingress",
            "metadata": {
                "name": f"{self.app_name}-ingress",
                "namespace": self.namespace,
                "labels": {
                    "app": self.app_name
                },
                "annotations": {
                    "nginx.ingress.kubernetes.io/rewrite-target": "/",
                    "nginx.ingress.kubernetes.io/ssl-redirect": "true",
                    "cert-manager.io/cluster-issuer": "letsencrypt-prod"
                }
            },
            "spec": {
                "tls": [
                    {
                        "hosts": [domain],
                        "secretName": f"{self.app_name}-tls"
                    }
                ],
                "rules": [
                    {
                        "host": domain,
                        "http": {
                            "paths": []
                        }
                    }
                ]
            }
        }
        
        # Add paths for each service
        for service_config in service_configs:
            if service_config.service_type == ServiceType.API:
                path = {
                    "path": f"/{service_config.name}",
                    "pathType": "Prefix",
                    "backend": {
                        "service": {
                            "name": f"{service_config.name}-service",
                            "port": {
                                "number": service_config.port
                            }
                        }
                    }
                }
                ingress["spec"]["rules"][0]["http"]["paths"].append(path)
        
        return ingress


class DockerfileGenerator:
    """Generates optimized Dockerfiles for different services."""
    
    def __init__(self):
        logger.info("DockerfileGenerator initialized")
    
    def generate_python_api_dockerfile(self, service_name: str) -> str:
        """Generate Dockerfile for Python API service."""
        dockerfile = f"""# Multi-stage build for {service_name}
FROM python:3.12-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.12-slim

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy Python packages from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Set working directory
WORKDIR /app

# Copy application code
COPY src/ ./src/
COPY config/ ./config/

# Set ownership
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Set environment variables
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \\
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Run application
CMD ["python", "-m", "uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
"""
        return dockerfile
    
    def generate_worker_dockerfile(self, service_name: str) -> str:
        """Generate Dockerfile for worker service."""
        dockerfile = f"""# Multi-stage build for {service_name}
FROM python:3.12-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.12-slim

# Create non-root user
RUN groupadd -r worker && useradd -r -g worker worker

# Copy Python packages from builder
COPY --from=builder /root/.local /home/<USER>/.local

# Set working directory
WORKDIR /app

# Copy application code
COPY src/ ./src/
COPY config/ ./config/

# Set ownership
RUN chown -R worker:worker /app

# Switch to non-root user
USER worker

# Set environment variables
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Run worker
CMD ["python", "-m", "src.workers.main"]
"""
        return dockerfile
    
    def generate_docker_compose(self, service_configs: List[ServiceConfiguration]) -> str:
        """Generate Docker Compose file for local development."""
        compose = {
            "version": "3.8",
            "services": {},
            "networks": {
                "crypto-analytics": {
                    "driver": "bridge"
                }
            },
            "volumes": {
                "postgres_data": {},
                "redis_data": {}
            }
        }
        
        # Add services
        for service_config in service_configs:
            service = {
                "build": {
                    "context": ".",
                    "dockerfile": f"docker/{service_config.name}.Dockerfile"
                },
                "ports": [f"{service_config.port}:{service_config.port}"],
                "environment": service_config.environment_variables,
                "networks": ["crypto-analytics"],
                "restart": "unless-stopped"
            }
            
            # Add health check
            if service_config.health_check:
                service["healthcheck"] = {
                    "test": f"curl -f http://localhost:{service_config.port}{service_config.health_check.path} || exit 1",
                    "interval": f"{service_config.health_check.interval_seconds}s",
                    "timeout": f"{service_config.health_check.timeout_seconds}s",
                    "retries": service_config.health_check.failure_threshold,
                    "start_period": f"{service_config.health_check.initial_delay_seconds}s"
                }
            
            # Add volumes
            if service_config.volumes:
                service["volumes"] = [
                    f"{volume['name']}:{volume['mountPath']}"
                    for volume in service_config.volumes
                ]
            
            compose["services"][service_config.name] = service
        
        # Add infrastructure services
        compose["services"]["postgres"] = {
            "image": "postgres:15-alpine",
            "environment": {
                "POSTGRES_DB": "crypto_analytics",
                "POSTGRES_USER": "postgres",
                "POSTGRES_PASSWORD": "postgres"
            },
            "volumes": ["postgres_data:/var/lib/postgresql/data"],
            "ports": ["5432:5432"],
            "networks": ["crypto-analytics"],
            "restart": "unless-stopped"
        }
        
        compose["services"]["redis"] = {
            "image": "redis:7-alpine",
            "volumes": ["redis_data:/data"],
            "ports": ["6379:6379"],
            "networks": ["crypto-analytics"],
            "restart": "unless-stopped"
        }
        
        return f"# Docker Compose for Crypto Analytics Platform\n{json.dumps(compose, indent=2)}"


class HealthCheckEndpoint:
    """Health check endpoint for container orchestration."""
    
    def __init__(self):
        self.start_time = datetime.now(timezone.utc)
        self.ready = False
        self._lock = threading.RLock()
        
        logger.info("HealthCheckEndpoint initialized")
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check."""
        with self._lock:
            current_time = datetime.now(timezone.utc)
            uptime = (current_time - self.start_time).total_seconds()
            
            # Get system health
            system_health = get_system_health()
            
            # Get monitoring dashboard status
            try:
                dashboard = get_dashboard()
                monitoring_healthy = dashboard is not None
            except:
                monitoring_healthy = False
            
            # Determine overall health
            healthy = (
                monitoring_healthy and
                system_health.get("circuit_breakers", {}).get("healthy", True) and
                uptime > 10  # Minimum uptime
            )
            
            return {
                "status": "healthy" if healthy else "unhealthy",
                "timestamp": current_time.isoformat(),
                "uptime_seconds": uptime,
                "ready": self.ready,
                "checks": {
                    "monitoring": monitoring_healthy,
                    "system_health": system_health,
                    "uptime_ok": uptime > 10
                }
            }
    
    async def readiness_check(self) -> Dict[str, Any]:
        """Readiness check for traffic routing."""
        with self._lock:
            # Check if service is ready to handle traffic
            health_result = await self.health_check()
            
            ready = (
                health_result["status"] == "healthy" and
                self.ready
            )
            
            return {
                "status": "ready" if ready else "not_ready",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "ready": ready
            }
    
    def set_ready(self, ready: bool = True):
        """Set readiness status."""
        with self._lock:
            self.ready = ready
            logger.info("Readiness status updated", ready=ready)


# Global instances
manifest_generator = KubernetesManifestGenerator()
dockerfile_generator = DockerfileGenerator()
health_endpoint = HealthCheckEndpoint()


def get_orchestration_status() -> Dict[str, Any]:
    """Get container orchestration status."""
    return {
        "manifest_generator": "initialized",
        "dockerfile_generator": "initialized",
        "health_endpoint": "initialized",
        "namespace": manifest_generator.namespace,
        "app_name": manifest_generator.app_name,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
