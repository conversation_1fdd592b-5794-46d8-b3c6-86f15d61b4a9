"""
Advanced Scam/Rug Detection System

This module implements comprehensive scam and rug pull detection using:
- Contract analysis for dangerous patterns
- Social signal validation
- Trading pattern analysis
- Honeypot detection
- Liquidity lock verification
- Team background checks
"""

import asyncio
import aiohttp
import logging
import re
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple

import aiohttp
from web3 import Web3

from ..core.cache import CacheManager
from ..core.config import config
from ..utils.rate_limit import RateLimiter, RateLimitConfig, RateLimitStrategy, _global_rate_limiter
from ..utils.error_handling import (
    fault_tolerant, API_CIRCUIT_CONFIG, EXTERNAL_SERVICE_RETRY_CONFIG,
    get_fault_tolerant_executor
)


logger = logging.getLogger(__name__)


class ScamType(Enum):
    """Types of scam/rug patterns."""
    HONEYPOT = "honeypot"
    RUG_PULL = "rug_pull"
    PUMP_DUMP = "pump_dump"
    FAKE_PROJECT = "fake_project"
    LIQUIDITY_DRAIN = "liquidity_drain"
    MINT_EXPLOIT = "mint_exploit"
    OWNERSHIP_EXPLOIT = "ownership_exploit"
    SOCIAL_ENGINEERING = "social_engineering"


class RiskLevel(Enum):
    """Risk assessment levels."""
    SAFE = "safe"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ScamDetectionResult:
    """Result of scam detection analysis."""
    token_address: str
    chain_id: int
    is_scam: bool
    risk_level: RiskLevel
    scam_types: List[ScamType]
    confidence_score: float  # 0-100
    red_flags: List[str]
    warnings: List[str]
    analysis_details: Dict[str, Any]
    analyzed_at: datetime


class AdvancedScamDetector:
    """Advanced scam and rug pull detection system."""

    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        self.session: Optional[aiohttp.ClientSession] = None
        # Configure rate limiter for API calls
        rate_config = RateLimitConfig(
            requests_per_second=2,  # Conservative rate for free APIs
            burst_size=5,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        )
        self.rate_limiter = RateLimiter(rate_config)
        
        # Detection thresholds
        self.HIGH_RISK_THRESHOLD = 70.0
        self.MEDIUM_RISK_THRESHOLD = 40.0
        self.LOW_RISK_THRESHOLD = 20.0
        
        # Contract analysis patterns
        self.DANGEROUS_FUNCTIONS = [
            "mint", "burn", "pause", "unpause", "blacklist", "whitelist",
            "setTaxFee", "setLiquidityFee", "excludeFromFee", "includeInFee",
            "setMaxTxAmount", "setSwapAndLiquifyEnabled", "emergencyWithdraw"
        ]
        
        self.HONEYPOT_PATTERNS = [
            "transfer.*revert", "approve.*false", "balanceOf.*0",
            "allowance.*0", "transferFrom.*revert"
        ]

    async def initialize(self):
        """Initialize the scam detector."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={"User-Agent": "TokenAnalyzer-ScamDetector/1.0"}
        )
        logger.info("Advanced scam detector initialized")

    async def shutdown(self):
        """Shutdown the scam detector."""
        if self.session:
            await self.session.close()
        logger.info("Advanced scam detector shutdown")

    async def analyze_token(
        self, 
        token_address: str, 
        chain_id: int,
        force_refresh: bool = False
    ) -> ScamDetectionResult:
        """
        Perform comprehensive scam detection analysis.
        
        Args:
            token_address: Token contract address
            chain_id: Blockchain ID
            force_refresh: Force refresh cached results
            
        Returns:
            ScamDetectionResult with detailed analysis
        """
        cache_key = f"scam_detection:{chain_id}:{token_address.lower()}"
        
        # Check cache first
        if not force_refresh:
            cached = await self.cache_manager.get(cache_key)
            if cached:
                logger.info(f"Using cached scam analysis for {token_address}")
                return ScamDetectionResult(**cached)

        logger.info(f"Starting scam analysis for {token_address} on chain {chain_id}")
        
        try:
            # Initialize analysis tracking
            red_flags = []
            warnings = []
            scam_types = []
            analysis_details = {}
            risk_score = 0.0

            # 1. Contract Analysis
            logger.info("Analyzing contract for scam patterns...")
            contract_analysis = await self._analyze_contract_patterns(token_address, chain_id)
            analysis_details["contract"] = contract_analysis
            
            contract_risk, contract_flags = self._evaluate_contract_risk(contract_analysis)
            risk_score += contract_risk
            red_flags.extend(contract_flags)

            # 2. Honeypot Detection
            logger.info("Performing honeypot detection...")
            honeypot_analysis = await self._detect_honeypot(token_address, chain_id)
            analysis_details["honeypot"] = honeypot_analysis
            
            if honeypot_analysis.get("is_honeypot", False):
                risk_score += 50.0
                red_flags.append("Token identified as honeypot")
                scam_types.append(ScamType.HONEYPOT)

            # 3. Liquidity Analysis
            logger.info("Analyzing liquidity patterns...")
            liquidity_analysis = await self._analyze_liquidity_patterns(token_address, chain_id)
            analysis_details["liquidity"] = liquidity_analysis
            
            liquidity_risk, liquidity_flags = self._evaluate_liquidity_risk(liquidity_analysis)
            risk_score += liquidity_risk
            red_flags.extend(liquidity_flags)

            # 4. Trading Pattern Analysis
            logger.info("Analyzing trading patterns...")
            trading_analysis = await self._analyze_trading_patterns(token_address, chain_id)
            analysis_details["trading"] = trading_analysis
            
            trading_risk, trading_flags = self._evaluate_trading_risk(trading_analysis)
            risk_score += trading_risk
            red_flags.extend(trading_flags)

            # 5. Social Signal Analysis
            logger.info("Analyzing social signals...")
            social_analysis = await self._analyze_social_signals(token_address, chain_id)
            analysis_details["social"] = social_analysis
            
            social_risk, social_flags = self._evaluate_social_risk(social_analysis)
            risk_score += social_risk
            red_flags.extend(social_flags)

            # 6. Team and Project Analysis
            logger.info("Analyzing team and project legitimacy...")
            team_analysis = await self._analyze_team_legitimacy(token_address, chain_id)
            analysis_details["team"] = team_analysis
            
            team_risk, team_flags = self._evaluate_team_risk(team_analysis)
            risk_score += team_risk
            red_flags.extend(team_flags)

            # 7. Historical Pattern Matching
            logger.info("Checking against known scam patterns...")
            pattern_analysis = await self._check_historical_patterns(token_address, chain_id)
            analysis_details["patterns"] = pattern_analysis
            
            pattern_risk, pattern_flags = self._evaluate_pattern_risk(pattern_analysis)
            risk_score += pattern_risk
            red_flags.extend(pattern_flags)

            # Calculate final assessment
            confidence_score = min(100.0, risk_score)
            risk_level = self._determine_risk_level(confidence_score)
            is_scam = confidence_score >= self.MEDIUM_RISK_THRESHOLD

            # Determine specific scam types
            scam_types.extend(self._identify_scam_types(analysis_details, red_flags))

            # Create result
            result = ScamDetectionResult(
                token_address=token_address.lower(),
                chain_id=chain_id,
                is_scam=is_scam,
                risk_level=risk_level,
                scam_types=list(set(scam_types)),  # Remove duplicates
                confidence_score=confidence_score,
                red_flags=red_flags,
                warnings=warnings,
                analysis_details=analysis_details,
                analyzed_at=datetime.now()
            )

            # Cache result
            await self.cache_manager.set(
                cache_key, 
                result.__dict__, 
                ttl=1800  # Cache for 30 minutes
            )

            logger.info(
                f"Scam analysis completed for {token_address}: "
                f"Risk={risk_level.value}, Confidence={confidence_score:.1f}%, "
                f"Scam={is_scam}"
            )

            return result

        except Exception as e:
            logger.error(f"Scam analysis failed for {token_address}: {e}")
            
            # Return high-risk result on failure
            return ScamDetectionResult(
                token_address=token_address.lower(),
                chain_id=chain_id,
                is_scam=True,
                risk_level=RiskLevel.CRITICAL,
                scam_types=[],
                confidence_score=100.0,
                red_flags=[f"Analysis failed: {str(e)}"],
                warnings=[],
                analysis_details={"error": str(e)},
                analyzed_at=datetime.now()
            )

    async def _analyze_contract_patterns(
        self, token_address: str, chain_id: int
    ) -> Dict[str, Any]:
        """Analyze contract for dangerous patterns using GoPlus Labs API."""
        try:
            # Get comprehensive security data from GoPlus
            goplus_result = await self._check_goplus_security(token_address, chain_id)

            if not goplus_result.get('simulation_success', False):
                logger.warning(f"GoPlus API failed for contract analysis: {token_address}")
                return self._get_default_contract_analysis()

            raw_data = goplus_result.get('raw_data', {})

            # Extract contract security information
            is_open_source = raw_data.get("is_open_source", "0") == "1"
            is_proxy = raw_data.get("is_proxy", "0") == "1"
            creator_address = raw_data.get("creator_address", "")
            creator_balance = float(raw_data.get("creator_balance", "0"))
            creator_percent = float(raw_data.get("creator_percent", "0"))

            # Analyze dangerous patterns based on GoPlus data
            dangerous_functions = []
            has_dangerous_functions = False

            # Check for high taxes (potential rug pull mechanism)
            buy_tax = goplus_result.get('buy_tax', 0)
            sell_tax = goplus_result.get('sell_tax', 0)

            if buy_tax > 10 or sell_tax > 10:
                dangerous_functions.append("high_tax_functions")
                has_dangerous_functions = True

            # Check for trading restrictions
            if not goplus_result.get('can_buy', True):
                dangerous_functions.append("buy_restriction")
                has_dangerous_functions = True

            if not goplus_result.get('can_sell', True):
                dangerous_functions.append("sell_restriction")
                has_dangerous_functions = True

            # Check for potential hidden mint (high creator balance)
            has_hidden_mint = creator_percent > 50.0  # Creator holds >50% of supply

            # Check ownership controls
            has_ownership_controls = is_proxy or creator_balance > 0
            ownership_renounced = creator_balance == 0 and creator_percent == 0

            return {
                "has_dangerous_functions": has_dangerous_functions,
                "dangerous_functions": dangerous_functions,
                "has_hidden_mint": has_hidden_mint,
                "has_ownership_controls": has_ownership_controls,
                "ownership_renounced": ownership_renounced,
                "proxy_contract": is_proxy,
                "verified_source": is_open_source,
                "compiler_version": None,  # Not available in GoPlus
                "creation_date": None,     # Not available in GoPlus
                "creator_address": creator_address,
                "creator_balance": creator_balance,
                "creator_percent": creator_percent,
                "buy_tax": buy_tax,
                "sell_tax": sell_tax,
                "holder_count": goplus_result.get('holder_count', 0),
                "raw_security_data": raw_data
            }

        except Exception as e:
            logger.error(f"Contract analysis failed for {token_address}: {e}")
            return self._get_default_contract_analysis()

    def _get_default_contract_analysis(self) -> Dict[str, Any]:
        """Return safe default contract analysis on error."""
        return {
            "has_dangerous_functions": True,  # Assume dangerous on error for safety
            "dangerous_functions": ["analysis_failed"],
            "has_hidden_mint": True,
            "has_ownership_controls": True,
            "ownership_renounced": False,
            "proxy_contract": True,
            "verified_source": False,
            "compiler_version": None,
            "creation_date": None,
            "creator_address": None,
            "error": "Contract analysis failed"
        }

    def _evaluate_contract_risk(self, analysis: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Evaluate contract-based risk factors using real security data."""
        risk_score = 0.0
        flags = []

        # Dangerous functions analysis
        if analysis.get("has_dangerous_functions", False):
            dangerous_funcs = analysis.get("dangerous_functions", [])
            if "high_tax_functions" in dangerous_funcs:
                risk_score += 30.0
                buy_tax = analysis.get("buy_tax", 0)
                sell_tax = analysis.get("sell_tax", 0)
                flags.append(f"High taxes detected: {max(buy_tax, sell_tax)}%")

            if "buy_restriction" in dangerous_funcs:
                risk_score += 40.0
                flags.append("Token cannot be purchased")

            if "sell_restriction" in dangerous_funcs:
                risk_score += 50.0
                flags.append("Token cannot be sold (potential honeypot)")

        # Hidden mint analysis
        if analysis.get("has_hidden_mint", False):
            creator_percent = analysis.get("creator_percent", 0)
            risk_score += 35.0
            flags.append(f"Creator holds {creator_percent:.1f}% of supply")

        # Ownership analysis
        if not analysis.get("ownership_renounced", False):
            creator_balance = analysis.get("creator_balance", 0)
            if creator_balance > 1000000:  # Large creator balance
                risk_score += 25.0
                flags.append(f"Creator holds large balance: {creator_balance:,.0f} tokens")
            else:
                risk_score += 15.0
                flags.append("Contract ownership not renounced")

        # Source code verification
        if not analysis.get("verified_source", False):
            risk_score += 20.0
            flags.append("Contract source code not verified")

        # Proxy contract risks
        if analysis.get("proxy_contract", False):
            risk_score += 15.0
            flags.append("Proxy contract (upgradeable)")

        # Low holder count risk
        holder_count = analysis.get("holder_count", 0)
        if holder_count < 100:
            risk_score += 20.0
            flags.append(f"Very low holder count: {holder_count}")
        elif holder_count < 1000:
            risk_score += 10.0
            flags.append(f"Low holder count: {holder_count}")

        return risk_score, flags

    @fault_tolerant("honeypot_detection", EXTERNAL_SERVICE_RETRY_CONFIG, API_CIRCUIT_CONFIG)
    async def _detect_honeypot(
        self, token_address: str, chain_id: int
    ) -> Dict[str, Any]:
        """Detect honeypot patterns using Honeypot.is and GoPlus APIs."""
        try:
            # Use both APIs for comprehensive analysis
            honeypot_result = await self._check_honeypot_is(token_address, chain_id)
            goplus_result = await self._check_goplus_security(token_address, chain_id)

            # Combine results for comprehensive analysis
            is_honeypot = (
                honeypot_result.get("is_honeypot", False) or
                goplus_result.get("is_honeypot", False)
            )

            # Get tax information from both sources
            buy_tax = max(
                honeypot_result.get("buy_tax", 0.0),
                goplus_result.get("buy_tax", 0.0)
            )
            sell_tax = max(
                honeypot_result.get("sell_tax", 0.0),
                goplus_result.get("sell_tax", 0.0)
            )

            # Determine if trading is possible
            can_buy = (
                honeypot_result.get("can_buy", True) and
                goplus_result.get("can_buy", True)
            )
            can_sell = (
                honeypot_result.get("can_sell", True) and
                goplus_result.get("can_sell", True)
            )

            simulation_success = (
                honeypot_result.get("simulation_success", False) or
                goplus_result.get("simulation_success", False)
            )

            return {
                "is_honeypot": is_honeypot,
                "buy_tax": buy_tax,
                "sell_tax": sell_tax,
                "can_buy": can_buy,
                "can_sell": can_sell,
                "simulation_success": simulation_success,
                "honeypot_details": honeypot_result,
                "security_details": goplus_result
            }

        except Exception as e:
            logger.error(f"Honeypot detection failed for {token_address}: {e}")
            # Return safe defaults on error
            return {
                "is_honeypot": True,  # Assume honeypot on error for safety
                "buy_tax": 100.0,
                "sell_tax": 100.0,
                "can_buy": False,
                "can_sell": False,
                "simulation_success": False,
                "error": str(e)
            }

    def _determine_risk_level(self, confidence_score: float) -> RiskLevel:
        """Determine risk level based on confidence score."""
        if confidence_score >= self.HIGH_RISK_THRESHOLD:
            return RiskLevel.CRITICAL
        elif confidence_score >= self.MEDIUM_RISK_THRESHOLD:
            return RiskLevel.HIGH
        elif confidence_score >= self.LOW_RISK_THRESHOLD:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def _identify_scam_types(
        self, analysis_details: Dict[str, Any], red_flags: List[str]
    ) -> List[ScamType]:
        """Identify specific types of scams based on analysis."""
        scam_types = []
        
        # Check for honeypot
        if analysis_details.get("honeypot", {}).get("is_honeypot", False):
            scam_types.append(ScamType.HONEYPOT)
        
        # Check for rug pull indicators
        liquidity = analysis_details.get("liquidity", {})
        if liquidity.get("unlocked_liquidity", 0) > 80:
            scam_types.append(ScamType.RUG_PULL)
        
        # Check for pump and dump patterns
        trading = analysis_details.get("trading", {})
        if trading.get("suspicious_volume_pattern", False):
            scam_types.append(ScamType.PUMP_DUMP)
        
        return scam_types

    async def _analyze_liquidity_patterns(
        self, token_address: str, chain_id: int
    ) -> Dict[str, Any]:
        """Analyze liquidity patterns for rug pull indicators."""
        try:
            # This would integrate with DEX APIs to analyze liquidity
            return {
                "total_liquidity_usd": 0,
                "locked_liquidity_percentage": 0,
                "unlocked_liquidity": 100,
                "liquidity_providers_count": 0,
                "top_lp_concentration": 100,
                "liquidity_history": [],
                "recent_liquidity_changes": [],
                "lock_duration_days": 0,
                "lock_contract_verified": False
            }
        except Exception as e:
            logger.error(f"Liquidity analysis failed: {e}")
            return {"error": str(e)}

    def _evaluate_liquidity_risk(self, analysis: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Evaluate liquidity-based risk factors."""
        risk_score = 0.0
        flags = []

        unlocked_pct = analysis.get("unlocked_liquidity", 100)
        if unlocked_pct > 80:
            risk_score += 40.0
            flags.append(f"High unlocked liquidity: {unlocked_pct}%")

        lp_concentration = analysis.get("top_lp_concentration", 100)
        if lp_concentration > 90:
            risk_score += 20.0
            flags.append(f"High LP concentration: {lp_concentration}%")

        lock_duration = analysis.get("lock_duration_days", 0)
        if lock_duration < 30:
            risk_score += 15.0
            flags.append(f"Short liquidity lock: {lock_duration} days")

        return risk_score, flags

    async def _analyze_trading_patterns(
        self, token_address: str, chain_id: int
    ) -> Dict[str, Any]:
        """Analyze trading patterns for suspicious activity."""
        try:
            # This would analyze trading data from DEXs
            return {
                "volume_24h_usd": 0,
                "volume_pattern_suspicious": False,
                "large_sells_detected": False,
                "wash_trading_detected": False,
                "bot_trading_percentage": 0,
                "unique_traders_24h": 0,
                "avg_trade_size_usd": 0,
                "price_manipulation_detected": False,
                "coordinated_buying": False,
                "dump_pattern_detected": False
            }
        except Exception as e:
            logger.error(f"Trading analysis failed: {e}")
            return {"error": str(e)}

    def _evaluate_trading_risk(self, analysis: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Evaluate trading pattern risks."""
        risk_score = 0.0
        flags = []

        if analysis.get("wash_trading_detected", False):
            risk_score += 30.0
            flags.append("Wash trading detected")

        if analysis.get("price_manipulation_detected", False):
            risk_score += 35.0
            flags.append("Price manipulation detected")

        if analysis.get("dump_pattern_detected", False):
            risk_score += 25.0
            flags.append("Dump pattern detected")

        bot_percentage = analysis.get("bot_trading_percentage", 0)
        if bot_percentage > 70:
            risk_score += 20.0
            flags.append(f"High bot trading: {bot_percentage}%")

        return risk_score, flags

    async def _analyze_social_signals(
        self, token_address: str, chain_id: int
    ) -> Dict[str, Any]:
        """Analyze social media signals for legitimacy."""
        try:
            # This would scrape social media for project info
            return {
                "twitter_verified": False,
                "twitter_followers": 0,
                "twitter_account_age_days": 0,
                "telegram_members": 0,
                "discord_members": 0,
                "website_exists": False,
                "website_ssl": False,
                "whitepaper_exists": False,
                "github_repository": False,
                "team_doxxed": False,
                "influencer_promotions": 0,
                "paid_promotion_detected": False,
                "fake_followers_percentage": 0
            }
        except Exception as e:
            logger.error(f"Social analysis failed: {e}")
            return {"error": str(e)}

    def _evaluate_social_risk(self, analysis: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Evaluate social signal risks."""
        risk_score = 0.0
        flags = []

        if not analysis.get("website_exists", False):
            risk_score += 15.0
            flags.append("No official website")

        if analysis.get("paid_promotion_detected", False):
            risk_score += 20.0
            flags.append("Paid promotion detected")

        fake_followers = analysis.get("fake_followers_percentage", 0)
        if fake_followers > 50:
            risk_score += 25.0
            flags.append(f"High fake followers: {fake_followers}%")

        twitter_age = analysis.get("twitter_account_age_days", 0)
        if twitter_age < 30:
            risk_score += 10.0
            flags.append(f"New Twitter account: {twitter_age} days")

        return risk_score, flags

    async def _analyze_team_legitimacy(
        self, token_address: str, chain_id: int
    ) -> Dict[str, Any]:
        """Analyze team and project legitimacy."""
        try:
            # This would research team backgrounds
            return {
                "team_identified": False,
                "team_linkedin_profiles": 0,
                "team_previous_projects": 0,
                "team_reputation_score": 0,
                "anonymous_team": True,
                "kyc_completed": False,
                "audit_completed": False,
                "audit_firm_reputable": False,
                "previous_rug_pulls": 0,
                "developer_wallet_analysis": {},
                "team_token_holdings": 0
            }
        except Exception as e:
            logger.error(f"Team analysis failed: {e}")
            return {"error": str(e)}

    def _evaluate_team_risk(self, analysis: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Evaluate team-based risks."""
        risk_score = 0.0
        flags = []

        if analysis.get("anonymous_team", True):
            risk_score += 20.0
            flags.append("Anonymous team")

        if not analysis.get("kyc_completed", False):
            risk_score += 15.0
            flags.append("No KYC completed")

        previous_rugs = analysis.get("previous_rug_pulls", 0)
        if previous_rugs > 0:
            risk_score += 50.0
            flags.append(f"Team involved in {previous_rugs} previous rug pulls")

        team_holdings = analysis.get("team_token_holdings", 0)
        if team_holdings > 20:
            risk_score += 25.0
            flags.append(f"High team token holdings: {team_holdings}%")

        return risk_score, flags

    async def _check_historical_patterns(
        self, token_address: str, chain_id: int
    ) -> Dict[str, Any]:
        """Check against known scam patterns and databases."""
        try:
            # This would check against scam databases
            return {
                "known_scam_database": False,
                "similar_contract_patterns": [],
                "creator_previous_scams": 0,
                "contract_similarity_score": 0,
                "blacklisted_addresses": [],
                "warning_lists": [],
                "community_reports": 0,
                "scam_pattern_matches": []
            }
        except Exception as e:
            logger.error(f"Pattern analysis failed: {e}")
            return {"error": str(e)}

    def _evaluate_pattern_risk(self, analysis: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Evaluate historical pattern risks."""
        risk_score = 0.0
        flags = []

        if analysis.get("known_scam_database", False):
            risk_score += 100.0
            flags.append("Token found in known scam database")

        creator_scams = analysis.get("creator_previous_scams", 0)
        if creator_scams > 0:
            risk_score += 60.0
            flags.append(f"Creator involved in {creator_scams} previous scams")

        community_reports = analysis.get("community_reports", 0)
        if community_reports > 5:
            risk_score += 30.0
            flags.append(f"Multiple community scam reports: {community_reports}")

        return risk_score, flags

    async def _check_honeypot_is(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Check token using Honeypot.is API."""
        try:
            url = "https://api.honeypot.is/v2/IsHoneypot"
            params = {
                "address": token_address,
                "chainID": chain_id
            }

            await _global_rate_limiter.acquire("honeypot_is")
            async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params, timeout=30) as response:
                        if response.status == 200:
                            data = await response.json()

                            # Extract key information
                            honeypot_result = data.get("honeypotResult", {})
                            simulation_result = data.get("simulationResult", {})
                            summary = data.get("summary", {})

                            return {
                                "is_honeypot": honeypot_result.get("isHoneypot", False),
                                "buy_tax": simulation_result.get("buyTax", 0.0),
                                "sell_tax": simulation_result.get("sellTax", 0.0),
                                "can_buy": not honeypot_result.get("isHoneypot", False),
                                "can_sell": not honeypot_result.get("isHoneypot", False),
                                "simulation_success": data.get("simulationSuccess", False),
                                "risk_level": summary.get("riskLevel", 0),
                                "risk": summary.get("risk", "unknown"),
                                "flags": summary.get("flags", []),
                                "raw_data": data
                            }
                        else:
                            logger.warning(f"Honeypot.is API returned {response.status} for {token_address}")
                            return {"simulation_success": False, "error": f"HTTP {response.status}"}

        except Exception as e:
            logger.error(f"Honeypot.is API error for {token_address}: {e}")
            return {"simulation_success": False, "error": str(e)}

    @fault_tolerant("goplus_security", EXTERNAL_SERVICE_RETRY_CONFIG, API_CIRCUIT_CONFIG)
    async def _check_goplus_security(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Check token using GoPlus Labs Security API."""
        try:
            url = f"https://api.gopluslabs.io/api/v1/token_security/{chain_id}"
            params = {
                "contract_addresses": token_address.lower()
            }

            await _global_rate_limiter.acquire("goplus")
            async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params, timeout=30) as response:
                        if response.status == 200:
                            data = await response.json()

                            if data.get("code") == 1 and "result" in data:
                                token_data = data["result"].get(token_address.lower(), {})

                                if not token_data:
                                    return {"simulation_success": False, "error": "Token not found"}

                                # Parse GoPlus security data
                                is_honeypot = token_data.get("is_honeypot", "0") == "1"
                                buy_tax = float(token_data.get("buy_tax", "0")) * 100  # Convert to percentage
                                sell_tax = float(token_data.get("sell_tax", "0")) * 100
                                cannot_buy = token_data.get("cannot_buy", "0") == "1"
                                cannot_sell_all = token_data.get("cannot_sell_all", "0") == "1"

                                return {
                                    "is_honeypot": is_honeypot,
                                    "buy_tax": buy_tax,
                                    "sell_tax": sell_tax,
                                    "can_buy": not cannot_buy,
                                    "can_sell": not cannot_sell_all,
                                    "simulation_success": True,
                                    "is_open_source": token_data.get("is_open_source", "0") == "1",
                                    "is_proxy": token_data.get("is_proxy", "0") == "1",
                                    "holder_count": int(token_data.get("holder_count", "0")),
                                    "creator_balance": float(token_data.get("creator_balance", "0")),
                                    "creator_percent": float(token_data.get("creator_percent", "0")),
                                    "raw_data": token_data
                                }
                            else:
                                return {"simulation_success": False, "error": data.get("message", "Unknown error")}
                        else:
                            logger.warning(f"GoPlus API returned {response.status} for {token_address}")
                            return {"simulation_success": False, "error": f"HTTP {response.status}"}

        except Exception as e:
            logger.error(f"GoPlus API error for {token_address}: {e}")
            return {"simulation_success": False, "error": str(e)}
