"""
ValidatorAgent - Validates discovered tokens for legitimacy and safety.

This agent performs comprehensive validation checks on discovered tokens including:
- Contract verification and auditing
- Liquidity and volume validation
- <PERSON><PERSON> and rug pull detection
- Social media presence verification
- Team and project legitimacy checks
"""

import logging
from dataclasses import asdict, dataclass
from datetime import datetime, timezone
from enum import Enum
from typing import Any

import aiohttp
from web3 import Web3

from ..core.cache import CacheManager
from ..core.config import settings
from ..core.database import DatabaseManager
from .scam_detector import AdvancedScamDetector, ScamDetectionResult
from ..security.token_whitelist import token_whitelist_manager


class ValidationStatus(Enum):
    """Token validation status."""

    PENDING = "pending"
    VALIDATED = "validated"
    SUSPICIOUS = "suspicious"
    DANGEROUS = "dangerous"
    FAILED = "failed"


class RiskLevel(Enum):
    """Risk assessment levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Validation result for a token."""

    token_address: str
    chain_id: int
    status: ValidationStatus
    risk_level: RiskLevel
    score: float  # 0-100, higher is better
    checks_passed: int
    checks_total: int
    issues: list[str]
    warnings: list[str]
    metadata: dict[str, Any]
    validated_at: datetime

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result["status"] = self.status.value
        result["risk_level"] = self.risk_level.value
        result["validated_at"] = self.validated_at.isoformat()
        return result


@dataclass
class ContractInfo:
    """Contract information and analysis."""

    address: str
    chain_id: int
    is_verified: bool
    source_code: str | None
    compiler_version: str | None
    creation_tx: str | None
    creator_address: str | None
    is_proxy: bool
    implementation_address: str | None
    has_mint_function: bool
    has_pause_function: bool
    has_blacklist_function: bool
    ownership_renounced: bool
    max_supply: int | None
    total_supply: int | None
    decimals: int | None
    functions: list[str]
    events: list[str]


class ValidatorAgent:
    """Agent for validating discovered tokens."""

    def __init__(
        self,
        db_manager: DatabaseManager,
        cache_manager: CacheManager,
        session: aiohttp.ClientSession | None = None,
    ):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.session = session
        self.logger = logging.getLogger(__name__)

        # Initialize advanced scam detector
        self.scam_detector = AdvancedScamDetector(cache_manager)

        # Web3 connections for different chains
        self.w3_connections = {}
        self._setup_web3_connections()

        # Validation thresholds
        self.min_liquidity_usd = 10000  # Minimum liquidity in USD
        self.min_volume_24h = 1000  # Minimum 24h volume
        self.min_holders = 50  # Minimum number of holders
        self.max_holder_concentration = 0.5  # Max % held by top holder

    def _setup_web3_connections(self):
        """Setup Web3 connections for different chains."""
        rpc_urls = {
            1: settings.ETHEREUM_RPC_URL,
            56: settings.BSC_RPC_URL,
            137: settings.POLYGON_RPC_URL,
            43114: settings.AVALANCHE_RPC_URL,
            250: settings.FANTOM_RPC_URL,
            42161: settings.ARBITRUM_RPC_URL,
            10: settings.OPTIMISM_RPC_URL,
        }

        for chain_id, rpc_url in rpc_urls.items():
            if rpc_url:
                try:
                    self.w3_connections[chain_id] = Web3(Web3.HTTPProvider(rpc_url))
                    self.logger.info(f"Connected to chain {chain_id}")
                except Exception as e:
                    self.logger.error(f"Failed to connect to chain {chain_id}: {e}")

    async def validate_token(
        self,
        token_address: str = None,
        chain_id: int = None,
        force_refresh: bool = False,
        # Compatibility parameters
        address: str = None,  # Alternative parameter name for backward compatibility
        **kwargs,
    ) -> ValidationResult:
        """
        Validate a token comprehensively.

        Args:
            token_address: Token contract address
            chain_id: Blockchain ID
            force_refresh: Force refresh cached data

        Returns:
            ValidationResult with comprehensive analysis
        """
        # Handle backward compatibility with 'address' parameter
        if address is not None and token_address is None:
            token_address = address

        if token_address is None:
            raise ValueError("token_address (or address) parameter is required")
        if chain_id is None:
            raise ValueError("chain_id parameter is required")

        cache_key = f"validation:{chain_id}:{token_address.lower()}"

        # Check if token is whitelisted first
        is_whitelisted = token_whitelist_manager.is_whitelisted(token_address, chain_id)
        if is_whitelisted:
            whitelist_entry = token_whitelist_manager.get_whitelist_entry(token_address, chain_id)
            self.logger.info(f"Token {token_address} is whitelisted as {whitelist_entry.symbol}")

            # Return optimized validation for whitelisted tokens
            return ValidationResult(
                token_address=token_address.lower(),
                chain_id=chain_id,
                status=ValidationStatus.VALID,
                risk_level=RiskLevel.VERY_LOW,
                score=95.0,  # High score for whitelisted tokens
                checks_passed=10,
                checks_total=10,
                issues=[],
                warnings=[],
                metadata={
                    "whitelisted": True,
                    "whitelist_category": whitelist_entry.category.value,
                    "whitelist_symbol": whitelist_entry.symbol,
                    "risk_adjustment": whitelist_entry.risk_score_adjustment
                },
                validated_at=datetime.now(timezone.utc),
            )

        # Check cache first
        if not force_refresh:
            cached = await self.cache_manager.get(cache_key)
            if cached:
                self.logger.info(f"Using cached validation for {token_address}")
                return ValidationResult(**cached)

        self.logger.info(
            f"Starting validation for token {token_address} on chain {chain_id}"
        )

        try:
            # Initialize validation tracking
            checks_passed = 0
            checks_total = 0
            issues = []
            warnings = []
            metadata = {}

            # Get Web3 connection
            w3 = self.w3_connections.get(chain_id)
            if not w3:
                raise ValueError(f"No Web3 connection for chain {chain_id}")

            # 1. Contract Analysis
            self.logger.info("Analyzing contract...")
            contract_info = await self._analyze_contract(w3, token_address)
            metadata["contract"] = asdict(contract_info)

            # Contract verification check
            checks_total += 1
            if contract_info.is_verified:
                checks_passed += 1
            else:
                issues.append("Contract source code not verified")

            # Dangerous functions check
            checks_total += 1
            dangerous_functions = self._check_dangerous_functions(contract_info)
            if not dangerous_functions:
                checks_passed += 1
            else:
                issues.extend(
                    [
                        f"Dangerous function detected: {func}"
                        for func in dangerous_functions
                    ]
                )

            # Ownership check
            checks_total += 1
            if contract_info.ownership_renounced:
                checks_passed += 1
            else:
                warnings.append("Contract ownership not renounced")

            # 2. Liquidity and Trading Analysis
            self.logger.info("Analyzing liquidity and trading...")
            liquidity_data = await self._analyze_liquidity(token_address, chain_id)
            metadata["liquidity"] = liquidity_data

            # Liquidity check
            checks_total += 1
            if liquidity_data.get("total_liquidity_usd", 0) >= self.min_liquidity_usd:
                checks_passed += 1
            else:
                issues.append(
                    f"Low liquidity: ${liquidity_data.get('total_liquidity_usd', 0):.2f}"
                )

            # Volume check
            checks_total += 1
            if liquidity_data.get("volume_24h_usd", 0) >= self.min_volume_24h:
                checks_passed += 1
            else:
                warnings.append(
                    f"Low 24h volume: ${liquidity_data.get('volume_24h_usd', 0):.2f}"
                )

            # 3. Holder Analysis
            self.logger.info("Analyzing token holders...")
            holder_data = await self._analyze_holders(w3, token_address, contract_info)
            metadata["holders"] = holder_data

            # Holder count check
            checks_total += 1
            if holder_data.get("holder_count", 0) >= self.min_holders:
                checks_passed += 1
            else:
                warnings.append(
                    f"Low holder count: {holder_data.get('holder_count', 0)}"
                )

            # Concentration check
            checks_total += 1
            top_holder_pct = holder_data.get("top_holder_percentage", 1.0)
            if top_holder_pct <= self.max_holder_concentration:
                checks_passed += 1
            else:
                issues.append(
                    f"High concentration: {top_holder_pct:.1%} held by top holder"
                )

            # 4. Honeypot Detection
            self.logger.info("Checking for honeypot...")
            honeypot_result = await self._check_honeypot(token_address, chain_id)
            metadata["honeypot"] = honeypot_result

            checks_total += 1
            if not honeypot_result.get("is_honeypot", True):
                checks_passed += 1
            else:
                issues.append("Token appears to be a honeypot")

            # 5. Advanced Scam Detection
            self.logger.info("Running advanced scam detection...")
            scam_result = await self.scam_detector.analyze_token(token_address, chain_id)
            metadata["scam_detection"] = {
                "is_scam": scam_result.is_scam,
                "risk_level": scam_result.risk_level.value,
                "confidence_score": scam_result.confidence_score,
                "scam_types": [st.value for st in scam_result.scam_types],
                "red_flags": scam_result.red_flags
            }

            checks_total += 1
            if not scam_result.is_scam:
                checks_passed += 1
            else:
                issues.extend(scam_result.red_flags)

            # 6. Social Media and Project Validation
            self.logger.info("Validating social presence...")
            social_data = await self._validate_social_presence(token_address, chain_id)
            metadata["social"] = social_data

            checks_total += 1
            if social_data.get("has_valid_social", False):
                checks_passed += 1
            else:
                warnings.append("No verified social media presence")

            # 6. Calculate final score and status
            score = (checks_passed / checks_total) * 100 if checks_total > 0 else 0

            # Determine status and risk level
            status, risk_level = self._determine_status_and_risk(
                score, len(issues), len(warnings), metadata
            )

            # Create validation result
            result = ValidationResult(
                token_address=token_address.lower(),
                chain_id=chain_id,
                status=status,
                risk_level=risk_level,
                score=score,
                checks_passed=checks_passed,
                checks_total=checks_total,
                issues=issues,
                warnings=warnings,
                metadata=metadata,
                validated_at=datetime.utcnow(),
            )

            # Cache result
            await self.cache_manager.set(
                cache_key, result.to_dict(), ttl=3600  # Cache for 1 hour
            )

            # Store in database
            await self._store_validation_result(result)

            self.logger.info(
                f"Validation completed for {token_address}: "
                f"Score={score:.1f}, Status={status.value}, Risk={risk_level.value}"
            )

            return result

        except Exception as e:
            self.logger.error(f"Validation failed for {token_address}: {e}")

            # Return failed validation
            return ValidationResult(
                token_address=token_address.lower(),
                chain_id=chain_id,
                status=ValidationStatus.FAILED,
                risk_level=RiskLevel.CRITICAL,
                score=0.0,
                checks_passed=0,
                checks_total=1,
                issues=[f"Validation failed: {str(e)}"],
                warnings=[],
                metadata={"error": str(e)},
                validated_at=datetime.utcnow(),
            )

    async def _analyze_contract(self, w3: Web3, token_address: str) -> ContractInfo:
        """Analyze token contract."""
        address = Web3.to_checksum_address(token_address)

        # Get contract code
        code = w3.eth.get_code(address)
        has_code = len(code) > 2  # More than just "0x"

        if not has_code:
            raise ValueError("Address is not a contract")

        # Try to get contract info from Etherscan-like API
        contract_info = await self._get_contract_verification_info(token_address)

        # Analyze contract bytecode for proxy patterns
        is_proxy = await self._check_proxy_pattern(w3, address)
        implementation_address = None
        if is_proxy:
            implementation_address = await self._get_implementation_address(w3, address)

        # Analyze contract functions
        functions, events = await self._analyze_contract_abi(contract_info.get("abi"))

        # Check for dangerous functions
        has_mint = any("mint" in func.lower() for func in functions)
        has_pause = any("pause" in func.lower() for func in functions)
        has_blacklist = any(
            any(keyword in func.lower() for keyword in ["blacklist", "block", "ban"])
            for func in functions
        )

        # Check ownership
        ownership_renounced = await self._check_ownership_renounced(
            w3, address, functions
        )

        # Get token info
        decimals, total_supply, max_supply = await self._get_token_info(w3, address)

        return ContractInfo(
            address=address,
            chain_id=w3.eth.chain_id,
            is_verified=contract_info.get("is_verified", False),
            source_code=contract_info.get("source_code"),
            compiler_version=contract_info.get("compiler_version"),
            creation_tx=contract_info.get("creation_tx"),
            creator_address=contract_info.get("creator_address"),
            is_proxy=is_proxy,
            implementation_address=implementation_address,
            has_mint_function=has_mint,
            has_pause_function=has_pause,
            has_blacklist_function=has_blacklist,
            ownership_renounced=ownership_renounced,
            max_supply=max_supply,
            total_supply=total_supply,
            decimals=decimals,
            functions=functions,
            events=events,
        )

    async def _get_contract_verification_info(
        self, token_address: str
    ) -> dict[str, Any]:
        """Get contract verification info from block explorer."""
        # This would integrate with Etherscan, BSCScan, etc.
        # For now, return mock data
        return {
            "is_verified": False,
            "source_code": None,
            "compiler_version": None,
            "creation_tx": None,
            "creator_address": None,
            "abi": None,
        }

    async def _check_proxy_pattern(self, w3: Web3, address: str) -> bool:
        """Check if contract follows proxy pattern."""
        try:
            # Check for common proxy storage slots
            implementation_slot = (
                "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc"
            )
            implementation = w3.eth.get_storage_at(address, implementation_slot)
            return implementation != b"\x00" * 32
        except:
            return False

    async def _get_implementation_address(
        self, w3: Web3, proxy_address: str
    ) -> str | None:
        """Get implementation address for proxy contracts."""
        try:
            implementation_slot = (
                "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc"
            )
            implementation = w3.eth.get_storage_at(proxy_address, implementation_slot)
            if implementation != b"\x00" * 32:
                return Web3.to_checksum_address(implementation[-20:])
        except:
            pass
        return None

    async def _analyze_contract_abi(
        self, abi: list | None
    ) -> tuple[list[str], list[str]]:
        """Analyze contract ABI to extract functions and events."""
        functions = []
        events = []

        if not abi:
            return functions, events

        for item in abi:
            if item.get("type") == "function":
                functions.append(item.get("name", ""))
            elif item.get("type") == "event":
                events.append(item.get("name", ""))

        return functions, events

    def _check_dangerous_functions(self, contract_info: ContractInfo) -> list[str]:
        """Check for dangerous contract functions."""
        dangerous = []

        if contract_info.has_mint_function:
            dangerous.append("mint")
        if contract_info.has_pause_function:
            dangerous.append("pause")
        if contract_info.has_blacklist_function:
            dangerous.append("blacklist")

        return dangerous

    async def _check_ownership_renounced(
        self, w3: Web3, address: str, functions: list[str]
    ) -> bool:
        """Check if contract ownership has been renounced."""
        try:
            # Standard ERC20 with Ownable
            if "owner" in functions:
                contract = w3.eth.contract(
                    address=address,
                    abi=[
                        {
                            "constant": True,
                            "inputs": [],
                            "name": "owner",
                            "outputs": [{"name": "", "type": "address"}],
                            "type": "function",
                        }
                    ],
                )
                owner = contract.functions.owner().call()
                return owner == "******************************************"
        except:
            pass

        return False

    async def _get_token_info(
        self, w3: Web3, address: str
    ) -> tuple[int | None, int | None, int | None]:
        """Get basic token information."""
        try:
            # Standard ERC20 ABI
            erc20_abi = [
                {
                    "constant": True,
                    "inputs": [],
                    "name": "decimals",
                    "outputs": [{"name": "", "type": "uint8"}],
                    "type": "function",
                },
                {
                    "constant": True,
                    "inputs": [],
                    "name": "totalSupply",
                    "outputs": [{"name": "", "type": "uint256"}],
                    "type": "function",
                },
            ]

            contract = w3.eth.contract(address=address, abi=erc20_abi)

            decimals = contract.functions.decimals().call()
            total_supply = contract.functions.totalSupply().call()

            # Max supply is often the same as total supply for standard tokens
            max_supply = total_supply

            return decimals, total_supply, max_supply

        except Exception as e:
            self.logger.warning(f"Failed to get token info for {address}: {e}")
            return None, None, None

    async def _analyze_liquidity(
        self, token_address: str, chain_id: int
    ) -> dict[str, Any]:
        """Analyze token liquidity across DEXes."""
        # This would integrate with DEX APIs
        # For now, return mock data
        return {
            "total_liquidity_usd": 50000.0,
            "volume_24h_usd": 5000.0,
            "dexes": ["Uniswap", "SushiSwap"],
            "largest_pool": {
                "dex": "Uniswap",
                "pair": f"{token_address}/WETH",
                "liquidity_usd": 30000.0,
            },
        }

    async def _analyze_holders(
        self, w3: Web3, token_address: str, contract_info: ContractInfo
    ) -> dict[str, Any]:
        """Analyze token holder distribution."""
        # This would require scanning blockchain or using specialized APIs
        # For now, return mock data
        return {
            "holder_count": 150,
            "top_holder_percentage": 0.25,
            "top_10_percentage": 0.60,
            "distribution_score": 75.0,
        }

    async def _check_honeypot(
        self, token_address: str, chain_id: int
    ) -> dict[str, Any]:
        """Check if token is a honeypot."""
        try:
            if not self.session:
                async with aiohttp.ClientSession() as session:
                    return await self._perform_honeypot_check(
                        session, token_address, chain_id
                    )
            else:
                return await self._perform_honeypot_check(
                    self.session, token_address, chain_id
                )
        except Exception as e:
            self.logger.error(f"Honeypot check failed: {e}")
            return {
                "is_honeypot": True,  # Fail safe
                "buy_tax": None,
                "sell_tax": None,
                "error": str(e),
            }

    async def _perform_honeypot_check(
        self, session: aiohttp.ClientSession, token_address: str, chain_id: int
    ) -> dict[str, Any]:
        """Perform actual honeypot check using external service."""
        # This would use services like honeypot.is or similar
        # For now, return mock analysis
        return {
            "is_honeypot": False,
            "buy_tax": 0.05,
            "sell_tax": 0.05,
            "simulation_success": True,
        }

    async def _validate_social_presence(
        self, token_address: str, chain_id: int
    ) -> dict[str, Any]:
        """Validate token's social media presence."""
        # This would check Twitter, Telegram, Discord, etc.
        # For now, return mock data
        return {
            "has_valid_social": True,
            "twitter": {
                "handle": "@example_token",
                "followers": 1500,
                "verified": False,
            },
            "telegram": {"group": "https://t.me/example_token", "members": 800},
            "website": "https://example-token.com",
        }

    def _determine_status_and_risk(
        self,
        score: float,
        issue_count: int,
        warning_count: int,
        metadata: dict[str, Any],
    ) -> tuple[ValidationStatus, RiskLevel]:
        """Determine final validation status and risk level."""
        # Critical issues that immediately mark as dangerous
        if issue_count > 0:
            critical_issues = ["honeypot", "high concentration", "dangerous function"]
            if any(
                any(critical in issue.lower() for critical in critical_issues)
                for issue in metadata.get("issues", [])
            ):
                return ValidationStatus.DANGEROUS, RiskLevel.CRITICAL

        # Score-based classification
        if score >= 80:
            if issue_count == 0:
                return ValidationStatus.VALIDATED, RiskLevel.LOW
            else:
                return ValidationStatus.SUSPICIOUS, RiskLevel.MEDIUM
        elif score >= 60:
            return ValidationStatus.SUSPICIOUS, RiskLevel.MEDIUM
        elif score >= 40:
            return ValidationStatus.SUSPICIOUS, RiskLevel.HIGH
        else:
            return ValidationStatus.DANGEROUS, RiskLevel.CRITICAL

    async def _store_validation_result(self, result: ValidationResult) -> None:
        """Store validation result in database."""
        try:
            await self.db_manager.execute(
                """
                INSERT OR REPLACE INTO token_validations (
                    token_address, chain_id, status, risk_level, score,
                    checks_passed, checks_total, issues, warnings,
                    metadata, validated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    result.token_address,
                    result.chain_id,
                    result.status.value,
                    result.risk_level.value,
                    result.score,
                    result.checks_passed,
                    result.checks_total,
                    "\n".join(result.issues),
                    "\n".join(result.warnings),
                    str(result.metadata),
                    result.validated_at.isoformat(),
                ),
            )

            self.logger.info(f"Stored validation result for {result.token_address}")

        except Exception as e:
            self.logger.error(f"Failed to store validation result: {e}")

    async def get_validation_summary(self, limit: int = 100) -> dict[str, Any]:
        """Get validation summary statistics."""
        try:
            results = await self.db_manager.fetch_all(
                """
                SELECT status, risk_level, COUNT(*) as count,
                       AVG(score) as avg_score
                FROM token_validations
                WHERE validated_at > datetime('now', '-24 hours')
                GROUP BY status, risk_level
                ORDER BY count DESC
                LIMIT ?
            """,
                (limit,),
            )

            return {
                "summary": results,
                "total_validated": sum(row["count"] for row in results),
                "generated_at": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            self.logger.error(f"Failed to get validation summary: {e}")
            return {"error": str(e)}

    async def cleanup_old_validations(self, days: int = 7) -> int:
        """Clean up old validation records."""
        try:
            result = await self.db_manager.execute(
                f"""
                DELETE FROM token_validations
                WHERE validated_at < datetime('now', '-{days} days')
            """
            )

            deleted_count = result.rowcount if hasattr(result, "rowcount") else 0
            self.logger.info(f"Cleaned up {deleted_count} old validation records")
            return deleted_count

        except Exception as e:
            self.logger.error(f"Failed to cleanup old validations: {e}")
            return 0
