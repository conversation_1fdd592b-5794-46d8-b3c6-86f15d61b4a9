"""
AutoGen-based multi-agent coordinator for token analysis.

This module implements a production-ready AutoGen GroupChat system that orchestrates
the analysis pipeline: Scheduler → Discovery → ChainInfo → MarketData → Trend → 
TA → Validator → Analyst → Audit.

Features:
- AutoGen GroupChat with proper agent orchestration
- Rate limiting integration for all external API calls
- Circuit breaker patterns for fault tolerance
- Comprehensive error handling and logging
- Production-ready monitoring and metrics
"""

import asyncio
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import json

import structlog
from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager

from ..core.config import settings
from ..core.database import DatabaseManager
from ..core.cache import CacheManager
from ..core.metrics import MetricsCollector
from ..utils.rate_limit import APIRateLimiter, EtherscanRateLimiter

logger = structlog.get_logger(__name__)


@dataclass
class AgentConfig:
    """Configuration for AutoGen agents."""
    name: str
    system_message: str
    llm_config: Dict[str, Any]
    max_consecutive_auto_reply: int = 3
    human_input_mode: str = "NEVER"


class AutoGenCoordinator:
    """
    AutoGen-based coordinator for multi-agent token analysis.
    
    Orchestrates the complete analysis pipeline using AutoGen's GroupChat
    functionality with proper agent sequencing and error handling.
    """
    
    def __init__(
        self,
        db_manager: DatabaseManager,
        cache_manager: CacheManager,
        metrics_collector: MetricsCollector
    ):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.metrics_collector = metrics_collector
        
        # Rate limiters
        self.api_rate_limiter = APIRateLimiter()
        self.etherscan_limiter = EtherscanRateLimiter(
            api_key=os.getenv("ETHERSCAN_API_KEY")
        )
        
        # AutoGen components
        self.agents: Dict[str, AssistantAgent] = {}
        self.user_proxy: Optional[UserProxyAgent] = None
        self.group_chat: Optional[GroupChat] = None
        self.group_chat_manager: Optional[GroupChatManager] = None
        
        # Analysis state
        self.current_analysis: Dict[str, Any] = {}
        self.analysis_results: Dict[str, Any] = {}
        
        logger.info("AutoGen coordinator initialized")
    
    async def initialize(self) -> None:
        """Initialize AutoGen agents and group chat."""
        try:
            # Setup LLM configuration
            llm_config = self._get_llm_config()
            
            # Create specialized agents
            await self._create_agents(llm_config)
            
            # Setup group chat
            await self._setup_group_chat()
            
            logger.info("AutoGen coordinator setup complete")
            
        except Exception as e:
            logger.error("Failed to initialize AutoGen coordinator", error=str(e))
            raise
    
    def _get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration for AutoGen agents."""
        openrouter_key = os.getenv("OPENROUTER_API_KEY")
        if not openrouter_key:
            raise ValueError("OPENROUTER_API_KEY environment variable required")
        
        return {
            "config_list": [
                {
                    "model": "deepseek/deepseek-r1-0528-qwen3-8b:free",
                    "api_key": openrouter_key,
                    "base_url": "https://openrouter.ai/api/v1",
                    "api_type": "openai"
                }
            ],
            "temperature": 0.1,
            "timeout": 120,
            "cache_seed": None  # Disable caching for production
        }
    
    async def _create_agents(self, llm_config: Dict[str, Any]) -> None:
        """Create specialized AutoGen agents for the analysis pipeline."""
        
        # 1. Scheduler Agent - Initiates and coordinates analysis
        self.agents["scheduler"] = AssistantAgent(
            name="scheduler",
            system_message="""You are the Scheduler Agent responsible for initiating and coordinating token analysis workflows.

Your responsibilities:
- Receive analysis requests with token addresses and parameters
- Validate input parameters and format
- Initiate the analysis pipeline by calling the Discovery Agent
- Monitor overall progress and handle timeouts
- Ensure proper sequencing of agent interactions

Always respond with structured JSON containing the next agent to call and required parameters.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=2
        )
        
        # 2. Discovery Agent - Token discovery and initial validation
        self.agents["discovery"] = AssistantAgent(
            name="discovery",
            system_message="""You are the Discovery Agent responsible for token discovery and initial validation.

Your responsibilities:
- Validate token addresses and chain compatibility
- Perform basic contract existence checks
- Gather initial token metadata (name, symbol, decimals)
- Check for known scam/rug-pull indicators
- Pass validated tokens to ChainInfo Agent

Use the provided rate-limited API helpers for all external calls. Always include confidence scores in your responses.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=3
        )
        
        # 3. ChainInfo Agent - Blockchain data analysis
        self.agents["chain_info"] = AssistantAgent(
            name="chain_info",
            system_message="""You are the ChainInfo Agent responsible for comprehensive blockchain data analysis.

Your responsibilities:
- Analyze contract code and security patterns
- Check liquidity pool information
- Examine holder distribution and whale activity
- Analyze transaction patterns and volume
- Detect potential rug-pull indicators
- Pass enriched data to MarketData Agent

Focus on security analysis and provide detailed risk assessments.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=3
        )
        
        # 4. MarketData Agent - Market data aggregation
        self.agents["market_data"] = AssistantAgent(
            name="market_data",
            system_message="""You are the MarketData Agent responsible for comprehensive market data aggregation.

Your responsibilities:
- Fetch price data from multiple sources (CoinGecko, DexScreener)
- Calculate price changes and volatility metrics
- Analyze trading volume and liquidity
- Gather market cap and supply information
- Pass market data to Trend Agent

Ensure data consistency across sources and flag any anomalies.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=3
        )
        
        # 5. Trend Agent - Sentiment and trend analysis
        self.agents["trend"] = AssistantAgent(
            name="trend",
            system_message="""You are the Trend Agent responsible for sentiment and trend analysis.

Your responsibilities:
- Analyze Google Trends data for token-related searches
- Fetch Fear & Greed Index for market sentiment
- Analyze social media sentiment (if available)
- Identify trending patterns and momentum
- Pass trend data to Technical Analysis Agent

Provide quantitative sentiment scores and trend indicators.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=3
        )
        
        # 6. Technical Analysis Agent - TA indicators and signals
        self.agents["technical"] = AssistantAgent(
            name="technical",
            system_message="""You are the Technical Analysis Agent responsible for technical indicator analysis.

Your responsibilities:
- Calculate technical indicators (RSI, MACD, Bollinger Bands, etc.)
- Identify support and resistance levels
- Analyze price patterns and chart formations
- Generate buy/sell signals based on technical analysis
- Pass technical data to Validator Agent

Provide clear technical signals with confidence levels.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=3
        )
        
        # 7. Validator Agent - Data validation and quality checks
        self.agents["validator"] = AssistantAgent(
            name="validator",
            system_message="""You are the Validator Agent responsible for data validation and quality assurance.

Your responsibilities:
- Validate data consistency across all sources
- Check for anomalies and outliers
- Verify calculation accuracy
- Ensure data completeness
- Flag potential data quality issues
- Pass validated data to Analyst Agent

Maintain high data quality standards and provide validation reports.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=2
        )
        
        # 8. Analyst Agent - Comprehensive analysis and scoring
        self.agents["analyst"] = AssistantAgent(
            name="analyst",
            system_message="""You are the Analyst Agent responsible for comprehensive token analysis and scoring.

Your responsibilities:
- Synthesize data from all previous agents
- Calculate overall risk scores and ratings
- Generate investment recommendations
- Provide detailed analysis reports
- Pass final analysis to Audit Agent

Provide clear, actionable insights with quantified risk assessments.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=3
        )
        
        # 9. Audit Agent - Final audit and report generation
        self.agents["audit"] = AssistantAgent(
            name="audit",
            system_message="""You are the Audit Agent responsible for final audit and report generation.

Your responsibilities:
- Audit all analysis results for consistency
- Generate final comprehensive reports
- Ensure compliance with analysis standards
- Create JSON output for frontend consumption
- Log analysis completion and metrics

Provide final quality assurance and generate production-ready outputs.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=2
        )
        
        # User Proxy Agent - Represents the system/user
        self.user_proxy = UserProxyAgent(
            name="user_proxy",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=0,
            code_execution_config=False
        )
        
        logger.info(f"Created {len(self.agents)} AutoGen agents")
    
    async def _setup_group_chat(self) -> None:
        """Setup AutoGen GroupChat with proper agent sequencing."""
        if not self.agents or not self.user_proxy:
            raise ValueError("Agents must be created before setting up group chat")
        
        # Define agent sequence for the analysis pipeline
        agent_list = [
            self.user_proxy,
            self.agents["scheduler"],
            self.agents["discovery"],
            self.agents["chain_info"],
            self.agents["market_data"],
            self.agents["trend"],
            self.agents["technical"],
            self.agents["validator"],
            self.agents["analyst"],
            self.agents["audit"]
        ]
        
        # Create group chat with custom speaker selection
        self.group_chat = GroupChat(
            agents=agent_list,
            messages=[],
            max_round=20,  # Maximum conversation rounds
            speaker_selection_method="round_robin",  # Can be customized
            allow_repeat_speaker=False
        )
        
        # Create group chat manager
        self.group_chat_manager = GroupChatManager(
            groupchat=self.group_chat,
            llm_config=self._get_llm_config()
        )
        
        logger.info("AutoGen GroupChat configured successfully")
    
    async def analyze_token(
        self,
        token_address: str,
        chain_id: int = 1,
        analysis_types: Optional[List[str]] = None,
        priority: str = "normal"
    ) -> Dict[str, Any]:
        """
        Analyze token using AutoGen multi-agent pipeline.
        
        Args:
            token_address: Token contract address
            chain_id: Blockchain chain ID (default: 1 for Ethereum)
            analysis_types: Types of analysis to perform
            priority: Analysis priority level
        
        Returns:
            Comprehensive analysis results
        """
        if not self.group_chat_manager:
            raise ValueError("AutoGen coordinator not initialized")
        
        # Prepare analysis request
        analysis_request = {
            "token_address": token_address,
            "chain_id": chain_id,
            "analysis_types": analysis_types or ["full"],
            "priority": priority,
            "timestamp": datetime.utcnow().isoformat(),
            "request_id": f"analysis_{int(datetime.utcnow().timestamp())}"
        }
        
        self.current_analysis = analysis_request
        
        logger.info(
            "Starting AutoGen token analysis",
            token_address=token_address,
            chain_id=chain_id,
            request_id=analysis_request["request_id"]
        )
        
        try:
            # Start the analysis conversation
            initial_message = f"""
Please analyze the token with the following parameters:
{json.dumps(analysis_request, indent=2)}

Begin the analysis pipeline following the proper sequence:
Scheduler → Discovery → ChainInfo → MarketData → Trend → Technical → Validator → Analyst → Audit

Each agent should:
1. Process the data from the previous agent
2. Perform their specialized analysis
3. Pass results to the next agent in the sequence
4. Use rate-limited API calls for external data
5. Provide structured JSON responses

Start the analysis now.
"""
            
            # Initiate group chat
            await self.user_proxy.a_initiate_chat(
                self.group_chat_manager,
                message=initial_message
            )
            
            # Extract results from conversation
            results = await self._extract_analysis_results()
            
            logger.info(
                "AutoGen token analysis completed",
                token_address=token_address,
                success=results.get("success", False),
                request_id=analysis_request["request_id"]
            )
            
            return results
            
        except Exception as e:
            logger.error(
                "AutoGen token analysis failed",
                token_address=token_address,
                error=str(e),
                request_id=analysis_request["request_id"]
            )
            
            return {
                "success": False,
                "error": str(e),
                "token_address": token_address,
                "chain_id": chain_id,
                "timestamp": datetime.utcnow().isoformat(),
                "request_id": analysis_request["request_id"]
            }
    
    async def _extract_analysis_results(self) -> Dict[str, Any]:
        """Extract and structure analysis results from group chat conversation."""
        if not self.group_chat or not self.group_chat.messages:
            return {"success": False, "error": "No conversation messages found"}
        
        # Process conversation messages to extract structured results
        results = {
            "success": True,
            "analysis_stages": {},
            "final_report": {},
            "conversation_log": [],
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Extract results from each agent's messages
        for message in self.group_chat.messages:
            agent_name = message.get("name", "unknown")
            content = message.get("content", "")
            
            # Store conversation log
            results["conversation_log"].append({
                "agent": agent_name,
                "content": content[:500],  # Truncate for storage
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # Try to extract JSON results from agent responses
            try:
                if "```json" in content:
                    json_start = content.find("```json") + 7
                    json_end = content.find("```", json_start)
                    if json_end > json_start:
                        json_content = content[json_start:json_end].strip()
                        agent_results = json.loads(json_content)
                        results["analysis_stages"][agent_name] = agent_results
            except (json.JSONDecodeError, ValueError):
                # Continue if JSON parsing fails
                pass
        
        # Generate final report from audit agent if available
        if "audit" in results["analysis_stages"]:
            results["final_report"] = results["analysis_stages"]["audit"]
        
        return results
    
    async def shutdown(self) -> None:
        """Shutdown AutoGen coordinator and cleanup resources."""
        try:
            # Clear group chat
            if self.group_chat:
                self.group_chat.messages.clear()
            
            # Clear agents
            self.agents.clear()
            self.user_proxy = None
            self.group_chat = None
            self.group_chat_manager = None
            
            logger.info("AutoGen coordinator shutdown complete")
            
        except Exception as e:
            logger.error("Error during AutoGen coordinator shutdown", error=str(e))
