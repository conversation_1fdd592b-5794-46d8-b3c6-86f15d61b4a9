"""
Advanced AutoGen Patterns for 2025
State management, distributed coordination, event sourcing, and sophisticated communication protocols.
"""

import asyncio
import itertools
import json
import time
import uuid
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union, Callable
import threading

import structlog

from ..core.logging_config import get_logger
from ..monitoring import increment_counter, set_gauge, record_timer


logger = get_logger(__name__)


class AgentState(Enum):
    """Agent states in the system."""
    IDLE = "idle"
    ACTIVE = "active"
    BUSY = "busy"
    ERROR = "error"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"


class MessageType(Enum):
    """Message types for agent communication."""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    STATUS_UPDATE = "status_update"
    COORDINATION = "coordination"
    EVENT_NOTIFICATION = "event_notification"
    HEARTBEAT = "heartbeat"
    DATA = "data"  # Added for general data exchange
    ERROR = "error"  # Added for error reporting
    ACK = "acknowledgment"  # Added for delivery guarantees


class EventType(Enum):
    """Event types for event sourcing."""
    AGENT_CREATED = "agent_created"
    AGENT_STATE_CHANGED = "agent_state_changed"
    TASK_ASSIGNED = "task_assigned"
    TASK_COMPLETED = "task_completed"
    MESSAGE_SENT = "message_sent"
    MESSAGE_RECEIVED = "message_received"
    ERROR_OCCURRED = "error_occurred"


@dataclass
class AgentMessage:
    """Message structure for agent communication with delivery guarantees."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""
    message_type: MessageType = MessageType.TASK_REQUEST
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    ttl_seconds: Optional[int] = None

    # Enhanced delivery guarantee fields
    delivery_guarantee: str = "at_least_once"  # at_least_once, at_most_once, exactly_once
    retry_count: int = 0
    max_retries: int = 3
    ack_required: bool = True
    error_info: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        try:
            return {
                "id": self.id,
                "sender_id": self.sender_id,
                "receiver_id": self.receiver_id,
                "message_type": self.message_type.value,
                "content": self.content,
                "timestamp": self.timestamp.isoformat(),
                "correlation_id": self.correlation_id,
                "reply_to": self.reply_to,
                "ttl_seconds": self.ttl_seconds,
                # Enhanced delivery fields
                "delivery_guarantee": self.delivery_guarantee,
                "retry_count": self.retry_count,
                "max_retries": self.max_retries,
                "ack_required": self.ack_required,
                "error_info": self.error_info
            }
        except Exception as e:
            logger.error("Error serializing message", error=str(e), message_id=self.id)
            # Return a minimal valid message in case of serialization errors
            return {
                "id": self.id,
                "sender_id": self.sender_id,
                "receiver_id": self.receiver_id,
                "message_type": MessageType.ERROR.value,
                "content": {"error": f"Serialization error: {str(e)}"},
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def is_expired(self) -> bool:
        """Check if message has expired."""
        if self.ttl_seconds is None:
            return False

        age = (datetime.now(timezone.utc) - self.timestamp).total_seconds()
        return age > self.ttl_seconds

    def create_ack(self) -> 'AgentMessage':
        """Create an acknowledgment message for this message."""
        return AgentMessage(
            sender_id=self.receiver_id,
            receiver_id=self.sender_id,
            message_type=MessageType.ACK,
            content={"original_message_id": self.id, "status": "received"},
            correlation_id=self.correlation_id,
            reply_to=self.id,
            ack_required=False  # Don't require acks for ack messages to avoid infinite loops
        )

    def create_error_response(self, error_message: str, error_code: str = "PROCESSING_ERROR") -> 'AgentMessage':
        """Create an error response for this message."""
        return AgentMessage(
            sender_id=self.receiver_id,
            receiver_id=self.sender_id,
            message_type=MessageType.ERROR,
            content={
                "original_message_id": self.id,
                "error_message": error_message,
                "error_code": error_code,
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            correlation_id=self.correlation_id,
            reply_to=self.id,
            error_info={
                "error_message": error_message,
                "error_code": error_code
            }
        )

    def should_retry(self) -> bool:
        """Check if message should be retried based on retry count and max retries."""
        return self.retry_count < self.max_retries


@dataclass
class AgentEvent:
    """Event structure for event sourcing."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = EventType.AGENT_CREATED
    agent_id: str = ""
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    version: int = 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "event_type": self.event_type.value,
            "agent_id": self.agent_id,
            "data": self.data,
            "timestamp": self.timestamp.isoformat(),
            "version": self.version
        }


@dataclass
class AgentSnapshot:
    """Agent state snapshot for event sourcing."""
    agent_id: str
    state: AgentState
    properties: Dict[str, Any]
    last_event_id: str
    version: int
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "agent_id": self.agent_id,
            "state": self.state.value,
            "properties": self.properties,
            "last_event_id": self.last_event_id,
            "version": self.version,
            "timestamp": self.timestamp.isoformat()
        }


class EventStore:
    """Event store for agent event sourcing."""
    
    def __init__(self):
        self.events: List[AgentEvent] = []
        self.snapshots: Dict[str, AgentSnapshot] = {}
        self.event_handlers: Dict[EventType, List[Callable]] = defaultdict(list)
        self._lock = threading.RLock()
        
        logger.info("EventStore initialized")
    
    def append_event(self, event: AgentEvent):
        """Append event to the store."""
        with self._lock:
            self.events.append(event)
            
            # Trigger event handlers
            for handler in self.event_handlers[event.event_type]:
                try:
                    handler(event)
                except Exception as e:
                    logger.exception("Event handler failed", 
                                   event_id=event.id, 
                                   handler=handler.__name__,
                                   error=str(e))
            
            increment_counter("events_stored", 1, {"event_type": event.event_type.value})
    
    def get_events(self, agent_id: str, from_version: int = 0) -> List[AgentEvent]:
        """Get events for an agent from a specific version."""
        with self._lock:
            return [
                event for event in self.events
                if event.agent_id == agent_id and event.version > from_version
            ]
    
    def save_snapshot(self, snapshot: AgentSnapshot):
        """Save agent state snapshot."""
        with self._lock:
            self.snapshots[snapshot.agent_id] = snapshot
            increment_counter("snapshots_saved", 1)
    
    def get_snapshot(self, agent_id: str) -> Optional[AgentSnapshot]:
        """Get latest snapshot for an agent."""
        with self._lock:
            return self.snapshots.get(agent_id)
    
    def register_event_handler(self, event_type: EventType, handler: Callable):
        """Register event handler."""
        with self._lock:
            self.event_handlers[event_type].append(handler)
            logger.info("Event handler registered", 
                       event_type=event_type.value,
                       handler=handler.__name__)


class MessageBus:
    """Advanced message bus for agent communication with delivery guarantees."""

    def __init__(self):
        self.message_queues: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.subscribers: Dict[str, Set[str]] = defaultdict(set)
        self.message_handlers: Dict[str, Dict[MessageType, Callable]] = defaultdict(dict)
        self.delivery_guarantees: Dict[str, str] = {}  # message_id -> delivery_status
        self._lock = threading.RLock()

        # Enhanced delivery guarantee tracking
        self.pending_acks: Dict[str, AgentMessage] = {}  # message_id -> original_message
        self.dead_letter_queue: deque = deque(maxlen=1000)  # Failed messages
        self.retry_queue: deque = deque(maxlen=1000)  # Messages to retry
        self.message_stats: Dict[str, int] = {
            "sent": 0,
            "delivered": 0,
            "failed": 0,
            "retried": 0,
            "dead_lettered": 0
        }

        logger.info("MessageBus initialized")
    
    async def send_message(self, message: AgentMessage) -> bool:
        """
        Send message with delivery guarantees.

        Supports three delivery guarantee modes:
        - at_least_once: Message will be delivered at least once, may be duplicated
        - at_most_once: Message will be delivered at most once, may be lost
        - exactly_once: Message will be delivered exactly once (requires acks)
        """
        try:
            with self._lock:
                # Check if message has expired
                if message.is_expired():
                    logger.warning("Message expired before sending", message_id=message.id)
                    self.message_stats["failed"] += 1
                    return False

                # Use message's delivery guarantee
                delivery_guarantee = message.delivery_guarantee
                self.delivery_guarantees[message.id] = delivery_guarantee

                # Add to receiver's queue
                try:
                    self.message_queues[message.receiver_id].append(message)

                    # Track pending acknowledgments for exactly_once and at_least_once delivery
                    if message.ack_required and delivery_guarantee in ["exactly_once", "at_least_once"]:
                        self.pending_acks[message.id] = message

                    # Notify subscribers
                    for subscriber in self.subscribers[message.receiver_id]:
                        try:
                            # In a real implementation, this would be async notification
                            pass
                        except Exception as e:
                            logger.warning("Subscriber notification failed",
                                        subscriber=subscriber,
                                        error=str(e))

                    # Update stats
                    self.message_stats["sent"] += 1

                    increment_counter("messages_sent", 1, {
                        "sender": message.sender_id,
                        "receiver": message.receiver_id,
                        "type": message.message_type.value,
                        "delivery_guarantee": delivery_guarantee
                    })

                    logger.debug("Message sent",
                                message_id=message.id,
                                sender=message.sender_id,
                                receiver=message.receiver_id,
                                delivery_guarantee=delivery_guarantee)

                    return True

                except Exception as e:
                    logger.error("Failed to send message",
                               message_id=message.id,
                               error=str(e))

                    # Handle retry logic for at_least_once delivery
                    if delivery_guarantee == "at_least_once" and message.should_retry():
                        message.retry_count += 1
                        self.retry_queue.append(message)
                        self.message_stats["retried"] += 1
                        logger.info("Message queued for retry",
                                  message_id=message.id,
                                  retry_count=message.retry_count)
                    else:
                        # Add to dead letter queue if retries exhausted or not at_least_once
                        self.dead_letter_queue.append(message)
                        self.message_stats["dead_lettered"] += 1
                        logger.warning("Message added to dead letter queue",
                                     message_id=message.id)

                    return False

        except Exception as e:
            logger.error("Unexpected error in send_message", error=str(e))
            return False
    
    async def receive_messages(self, agent_id: str, limit: int = 10) -> List[AgentMessage]:
        """Receive messages for an agent."""
        with self._lock:
            queue = self.message_queues[agent_id]
            messages = []
            
            for _ in range(min(limit, len(queue))):
                if queue:
                    message = queue.popleft()
                    
                    # Check if message has expired
                    if not message.is_expired():
                        messages.append(message)
                        
                        increment_counter("messages_received", 1, {
                            "receiver": agent_id,
                            "type": message.message_type.value
                        })
                    else:
                        increment_counter("messages_expired", 1)
            
            return messages
    
    def subscribe(self, agent_id: str, topic: str):
        """Subscribe agent to a topic."""
        with self._lock:
            self.subscribers[topic].add(agent_id)
            logger.info("Agent subscribed", agent_id=agent_id, topic=topic)
    
    def unsubscribe(self, agent_id: str, topic: str):
        """Unsubscribe agent from a topic."""
        with self._lock:
            self.subscribers[topic].discard(agent_id)
            logger.info("Agent unsubscribed", agent_id=agent_id, topic=topic)
    
    def register_message_handler(self, agent_id: str, message_type: MessageType, handler: Callable):
        """Register message handler for an agent."""
        with self._lock:
            self.message_handlers[agent_id][message_type] = handler
            logger.info("Message handler registered", 
                       agent_id=agent_id,
                       message_type=message_type.value)
    
    def get_queue_status(self, agent_id: str) -> Dict[str, Any]:
        """Get queue status for an agent."""
        with self._lock:
            queue = self.message_queues[agent_id]

            return {
                "agent_id": agent_id,
                "queue_size": len(queue),
                "max_queue_size": queue.maxlen,
                "subscriptions": list(self.subscribers.keys()),
                "registered_handlers": list(self.message_handlers[agent_id].keys()),
                # Enhanced status information
                "oldest_message_age": (datetime.now(timezone.utc) - queue[0].timestamp).total_seconds() if queue else 0,
                "newest_message_age": (datetime.now(timezone.utc) - queue[-1].timestamp).total_seconds() if queue else 0,
                "pending_acks": sum(1 for _, msg in self.pending_acks.items() if msg.sender_id == agent_id),
                "retry_queue_length": sum(1 for msg in self.retry_queue if msg.sender_id == agent_id),
                "dead_letter_count": sum(1 for msg in self.dead_letter_queue if msg.sender_id == agent_id),
                "message_stats": self.message_stats.copy()
            }

    async def process_acknowledgment(self, ack_message: AgentMessage) -> bool:
        """Process acknowledgment message and update delivery status."""
        try:
            with self._lock:
                if ack_message.message_type != MessageType.ACK:
                    logger.warning("Non-ACK message sent to acknowledgment processor",
                                 message_id=ack_message.id,
                                 message_type=ack_message.message_type.value)
                    return False

                # Extract original message ID from ACK content
                original_message_id = ack_message.content.get("original_message_id")
                if not original_message_id:
                    logger.warning("ACK message missing original_message_id",
                                 message_id=ack_message.id)
                    return False

                # Remove from pending acks if present
                if original_message_id in self.pending_acks:
                    del self.pending_acks[original_message_id]
                    self.message_stats["delivered"] += 1
                    logger.debug("Message acknowledged",
                               original_message_id=original_message_id,
                               ack_message_id=ack_message.id)
                    return True
                else:
                    logger.warning("Received ACK for unknown message",
                                 original_message_id=original_message_id,
                                 ack_message_id=ack_message.id)
                    return False

        except Exception as e:
            logger.error("Error processing acknowledgment",
                       message_id=ack_message.id,
                       error=str(e))
            return False

    async def retry_failed_messages(self) -> int:
        """Retry messages in the retry queue."""
        retry_count = 0
        try:
            with self._lock:
                # Copy retry queue to avoid modification during iteration
                messages_to_retry = list(self.retry_queue)
                self.retry_queue.clear()

                for message in messages_to_retry:
                    # Check if message should still be retried
                    if message.should_retry() and not message.is_expired():
                        # Increment retry count and send again
                        message.retry_count += 1
                        success = await self.send_message(message)
                        if success:
                            retry_count += 1
                    else:
                        # Add to dead letter queue if retries exhausted or expired
                        self.dead_letter_queue.append(message)
                        self.message_stats["dead_lettered"] += 1

                return retry_count

        except Exception as e:
            logger.error("Error retrying failed messages", error=str(e))
            return retry_count

    def get_dead_letter_messages(self, limit: int = 100) -> List[AgentMessage]:
        """Get messages from the dead letter queue."""
        with self._lock:
            # Return copy of messages to avoid modification issues
            return list(itertools.islice(self.dead_letter_queue, limit))

    def get_message_stats(self) -> Dict[str, int]:
        """Get message statistics."""
        with self._lock:
            return self.message_stats.copy()


class AdvancedAgent(ABC):
    """Advanced agent with state management and event sourcing."""
    
    def __init__(self, agent_id: str, event_store: EventStore, message_bus: MessageBus):
        self.agent_id = agent_id
        self.state = AgentState.IDLE
        self.properties: Dict[str, Any] = {}
        self.event_store = event_store
        self.message_bus = message_bus
        self.version = 0
        self.last_heartbeat = datetime.now(timezone.utc)
        self._lock = threading.RLock()
        
        # Register for own events
        self.event_store.register_event_handler(EventType.AGENT_STATE_CHANGED, self._handle_state_change)
        
        # Emit creation event
        self._emit_event(EventType.AGENT_CREATED, {"initial_state": self.state.value})
        
        logger.info("AdvancedAgent created", agent_id=agent_id)
    
    def _emit_event(self, event_type: EventType, data: Dict[str, Any]):
        """Emit an event to the event store."""
        with self._lock:
            self.version += 1
            
            event = AgentEvent(
                event_type=event_type,
                agent_id=self.agent_id,
                data=data,
                version=self.version
            )
            
            self.event_store.append_event(event)
    
    def _handle_state_change(self, event: AgentEvent):
        """Handle state change events."""
        if event.agent_id == self.agent_id:
            logger.info("Agent state changed", 
                       agent_id=self.agent_id,
                       old_state=self.state.value,
                       new_state=event.data.get("new_state"))
    
    def change_state(self, new_state: AgentState, reason: str = ""):
        """Change agent state with event emission."""
        with self._lock:
            old_state = self.state
            self.state = new_state
            
            self._emit_event(EventType.AGENT_STATE_CHANGED, {
                "old_state": old_state.value,
                "new_state": new_state.value,
                "reason": reason
            })
            
            set_gauge("agent_state", 1, {
                "agent_id": self.agent_id,
                "state": new_state.value
            })
    
    def set_property(self, key: str, value: Any):
        """Set agent property."""
        with self._lock:
            old_value = self.properties.get(key)
            self.properties[key] = value
            
            self._emit_event(EventType.AGENT_STATE_CHANGED, {
                "property_changed": key,
                "old_value": old_value,
                "new_value": value
            })
    
    async def send_message(self, receiver_id: str, message_type: MessageType, 
                          content: Dict[str, Any], correlation_id: Optional[str] = None) -> bool:
        """Send message to another agent."""
        message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            content=content,
            correlation_id=correlation_id,
            ttl_seconds=300  # 5 minutes TTL
        )
        
        success = await self.message_bus.send_message(message)
        
        if success:
            self._emit_event(EventType.MESSAGE_SENT, {
                "message_id": message.id,
                "receiver_id": receiver_id,
                "message_type": message_type.value
            })
        
        return success
    
    async def receive_messages(self, limit: int = 10) -> List[AgentMessage]:
        """Receive messages from message bus."""
        messages = await self.message_bus.receive_messages(self.agent_id, limit)
        
        for message in messages:
            self._emit_event(EventType.MESSAGE_RECEIVED, {
                "message_id": message.id,
                "sender_id": message.sender_id,
                "message_type": message.message_type.value
            })
        
        return messages
    
    async def heartbeat(self):
        """Send heartbeat to indicate agent is alive."""
        self.last_heartbeat = datetime.now(timezone.utc)
        
        heartbeat_message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id="system",
            message_type=MessageType.HEARTBEAT,
            content={"timestamp": self.last_heartbeat.isoformat()}
        )
        
        await self.message_bus.send_message(heartbeat_message)
    
    def create_snapshot(self) -> AgentSnapshot:
        """Create state snapshot."""
        with self._lock:
            return AgentSnapshot(
                agent_id=self.agent_id,
                state=self.state,
                properties=self.properties.copy(),
                last_event_id=str(self.version),
                version=self.version
            )
    
    def restore_from_snapshot(self, snapshot: AgentSnapshot):
        """Restore agent state from snapshot."""
        with self._lock:
            self.state = snapshot.state
            self.properties = snapshot.properties.copy()
            self.version = snapshot.version
            
            logger.info("Agent restored from snapshot", 
                       agent_id=self.agent_id,
                       snapshot_version=snapshot.version)
    
    @abstractmethod
    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process a task (to be implemented by subclasses)."""
        pass
    
    @abstractmethod
    async def handle_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle incoming message (to be implemented by subclasses)."""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status."""
        with self._lock:
            return {
                "agent_id": self.agent_id,
                "state": self.state.value,
                "properties": self.properties.copy(),
                "version": self.version,
                "last_heartbeat": self.last_heartbeat.isoformat(),
                "queue_status": self.message_bus.get_queue_status(self.agent_id)
            }


class AgentCoordinator:
    """Distributed agent coordination system."""

    def __init__(self, event_store: EventStore, message_bus: MessageBus):
        self.event_store = event_store
        self.message_bus = message_bus
        self.agents: Dict[str, AdvancedAgent] = {}
        self.task_queue: deque = deque()
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.coordination_strategies: Dict[str, Callable] = {}
        self._lock = threading.RLock()

        # Register for coordination events
        self.event_store.register_event_handler(EventType.TASK_ASSIGNED, self._handle_task_assigned)
        self.event_store.register_event_handler(EventType.TASK_COMPLETED, self._handle_task_completed)

        logger.info("AgentCoordinator initialized")

    def register_agent(self, agent: AdvancedAgent):
        """Register an agent with the coordinator."""
        with self._lock:
            self.agents[agent.agent_id] = agent

            # Subscribe agent to coordination messages
            self.message_bus.subscribe(agent.agent_id, "coordination")

            logger.info("Agent registered with coordinator", agent_id=agent.agent_id)

    def unregister_agent(self, agent_id: str):
        """Unregister an agent from the coordinator."""
        with self._lock:
            if agent_id in self.agents:
                del self.agents[agent_id]
                self.message_bus.unsubscribe(agent_id, "coordination")

                logger.info("Agent unregistered from coordinator", agent_id=agent_id)

    async def assign_task(self, task: Dict[str, Any], strategy: str = "round_robin") -> Optional[str]:
        """Assign task to an agent using specified strategy."""
        with self._lock:
            if strategy not in self.coordination_strategies:
                strategy = "round_robin"

            # Find suitable agent
            selected_agent = await self._select_agent_for_task(task, strategy)

            if not selected_agent:
                # No available agent, queue the task
                self.task_queue.append(task)
                logger.warning("No available agent for task, queued", task_id=task.get("id"))
                return None

            # Assign task to agent
            task_id = task.get("id", str(uuid.uuid4()))
            task["assigned_agent"] = selected_agent.agent_id
            task["assigned_at"] = datetime.now(timezone.utc).isoformat()

            self.active_tasks[task_id] = task

            # Send task to agent
            await selected_agent.send_message(
                receiver_id=selected_agent.agent_id,
                message_type=MessageType.TASK_REQUEST,
                content=task,
                correlation_id=task_id
            )

            # Change agent state
            selected_agent.change_state(AgentState.BUSY, f"Assigned task {task_id}")

            # Emit event
            event = AgentEvent(
                event_type=EventType.TASK_ASSIGNED,
                agent_id=selected_agent.agent_id,
                data={
                    "task_id": task_id,
                    "task_type": task.get("type"),
                    "strategy": strategy
                }
            )
            self.event_store.append_event(event)

            increment_counter("tasks_assigned", 1, {
                "agent_id": selected_agent.agent_id,
                "strategy": strategy
            })

            return task_id

    async def _select_agent_for_task(self, task: Dict[str, Any], strategy: str) -> Optional[AdvancedAgent]:
        """Select agent for task based on strategy."""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.state in [AgentState.IDLE, AgentState.ACTIVE]
        ]

        if not available_agents:
            return None

        if strategy == "round_robin":
            return self._round_robin_selection(available_agents)
        elif strategy == "least_loaded":
            return self._least_loaded_selection(available_agents)
        elif strategy == "capability_based":
            return self._capability_based_selection(available_agents, task)
        else:
            return available_agents[0]  # Default to first available

    def _round_robin_selection(self, agents: List[AdvancedAgent]) -> AdvancedAgent:
        """Round-robin agent selection."""
        # Simple round-robin based on agent_id hash
        return min(agents, key=lambda a: hash(a.agent_id))

    def _least_loaded_selection(self, agents: List[AdvancedAgent]) -> AdvancedAgent:
        """Select least loaded agent."""
        # Count active tasks per agent
        agent_loads = defaultdict(int)
        for task in self.active_tasks.values():
            assigned_agent = task.get("assigned_agent")
            if assigned_agent:
                agent_loads[assigned_agent] += 1

        return min(agents, key=lambda a: agent_loads[a.agent_id])

    def _capability_based_selection(self, agents: List[AdvancedAgent], task: Dict[str, Any]) -> AdvancedAgent:
        """Select agent based on capabilities."""
        task_type = task.get("type", "general")

        # Check agent capabilities
        capable_agents = []
        for agent in agents:
            capabilities = agent.properties.get("capabilities", [])
            if task_type in capabilities or "general" in capabilities:
                capable_agents.append(agent)

        if capable_agents:
            return self._least_loaded_selection(capable_agents)
        else:
            return self._least_loaded_selection(agents)

    def _handle_task_assigned(self, event: AgentEvent):
        """Handle task assigned event."""
        logger.info("Task assigned event handled",
                   agent_id=event.agent_id,
                   task_id=event.data.get("task_id"))

    def _handle_task_completed(self, event: AgentEvent):
        """Handle task completed event."""
        task_id = event.data.get("task_id")

        with self._lock:
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]

                # Try to assign queued tasks
                if self.task_queue:
                    queued_task = self.task_queue.popleft()
                    asyncio.create_task(self.assign_task(queued_task))

        logger.info("Task completed event handled",
                   agent_id=event.agent_id,
                   task_id=task_id)

    async def broadcast_message(self, message_type: MessageType, content: Dict[str, Any]):
        """Broadcast message to all agents."""
        for agent in self.agents.values():
            await agent.send_message(
                receiver_id=agent.agent_id,
                message_type=message_type,
                content=content
            )

        increment_counter("messages_broadcast", 1, {"type": message_type.value})

    def get_coordination_status(self) -> Dict[str, Any]:
        """Get coordination system status."""
        with self._lock:
            agent_states = defaultdict(int)
            for agent in self.agents.values():
                agent_states[agent.state.value] += 1

            return {
                "total_agents": len(self.agents),
                "agent_states": dict(agent_states),
                "active_tasks": len(self.active_tasks),
                "queued_tasks": len(self.task_queue),
                "coordination_strategies": list(self.coordination_strategies.keys())
            }


class TokenAnalysisAgent(AdvancedAgent):
    """Specialized agent for token analysis tasks."""

    def __init__(self, agent_id: str, event_store: EventStore, message_bus: MessageBus):
        super().__init__(agent_id, event_store, message_bus)

        # Set capabilities
        self.set_property("capabilities", ["token_analysis", "risk_assessment", "general"])
        self.set_property("specialization", "crypto_analysis")

        # Register message handlers
        self.message_bus.register_message_handler(
            self.agent_id,
            MessageType.TASK_REQUEST,
            self.handle_task_request
        )

    async def process_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Process token analysis task."""
        task_type = task.get("type", "unknown")

        try:
            self.change_state(AgentState.BUSY, f"Processing {task_type}")

            if task_type == "token_analysis":
                result = await self._analyze_token(task)
            elif task_type == "risk_assessment":
                result = await self._assess_risk(task)
            else:
                result = {"error": f"Unknown task type: {task_type}"}

            self.change_state(AgentState.ACTIVE, "Task completed")

            # Emit completion event
            self._emit_event(EventType.TASK_COMPLETED, {
                "task_id": task.get("id"),
                "task_type": task_type,
                "success": "error" not in result
            })

            return result

        except Exception as e:
            self.change_state(AgentState.ERROR, f"Task failed: {str(e)}")

            self._emit_event(EventType.ERROR_OCCURRED, {
                "task_id": task.get("id"),
                "error": str(e)
            })

            return {"error": str(e)}

    async def _analyze_token(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze token (simulated)."""
        token_address = task.get("token_address", "")

        # Simulate analysis time
        await asyncio.sleep(0.1)

        return {
            "token_address": token_address,
            "analysis_result": "completed",
            "metrics": {
                "price_volatility": 25.5,
                "volume_24h": 1000000,
                "liquidity_score": 0.8
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    async def _assess_risk(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Assess token risk (simulated)."""
        token_address = task.get("token_address", "")

        # Simulate risk assessment
        await asyncio.sleep(0.05)

        return {
            "token_address": token_address,
            "risk_level": "medium",
            "confidence": 0.85,
            "risk_factors": ["price_volatility", "low_liquidity"],
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    async def handle_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle incoming message."""
        if message.message_type == MessageType.TASK_REQUEST:
            return await self.handle_task_request(message)
        elif message.message_type == MessageType.STATUS_UPDATE:
            return await self.handle_status_request(message)
        else:
            logger.warning("Unhandled message type",
                         message_type=message.message_type.value,
                         agent_id=self.agent_id)
            return None

    async def handle_task_request(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle task request message."""
        task = message.content
        result = await self.process_task(task)

        # Send response
        response = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            message_type=MessageType.TASK_RESPONSE,
            content=result,
            correlation_id=message.correlation_id,
            reply_to=message.id
        )

        await self.message_bus.send_message(response)
        return response

    async def handle_status_request(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle status request message."""
        status = self.get_status()

        response = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            message_type=MessageType.STATUS_UPDATE,
            content=status,
            correlation_id=message.correlation_id,
            reply_to=message.id
        )

        await self.message_bus.send_message(response)
        return response


# Global instances
event_store = EventStore()
message_bus = MessageBus()
agent_coordinator = AgentCoordinator(event_store, message_bus)


def get_autogen_status() -> Dict[str, Any]:
    """Get AutoGen system status."""
    return {
        "event_store": {
            "total_events": len(event_store.events),
            "total_snapshots": len(event_store.snapshots),
            "event_handlers": {
                event_type.value: len(handlers)
                for event_type, handlers in event_store.event_handlers.items()
            }
        },
        "message_bus": {
            "total_queues": len(message_bus.message_queues),
            "total_subscribers": sum(len(subs) for subs in message_bus.subscribers.values()),
            "delivery_guarantees": len(message_bus.delivery_guarantees)
        },
        "coordination": agent_coordinator.get_coordination_status(),
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
