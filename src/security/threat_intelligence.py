"""
Advanced Threat Intelligence Integration System

This module integrates real-time threat intelligence feeds to stay ahead of emerging
attack patterns identified in 2025 security research. It provides comprehensive
threat analysis, IOC management, and automated threat response capabilities.

Features:
- Real-time threat intelligence feed integration
- IOC (Indicators of Compromise) management and correlation
- Advanced threat hunting and pattern recognition
- Automated threat response and mitigation
- Threat landscape analysis and reporting
- Integration with multiple threat intelligence sources
- Machine learning-based threat prediction
"""

import asyncio
import hashlib
import json
import logging
import re
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import threading
import aiohttp

import structlog

from ..core.config import config
from ..core.logging_config import get_logger
from ..compliance.audit_framework import AuditTrail, AuditEventType, RiskLevel
from ..monitoring.dashboard import metrics_collector, increment_counter, set_gauge

logger = get_logger(__name__)


class ThreatType(Enum):
    """Types of threats in threat intelligence."""
    MALWARE = "malware"
    BOTNET = "botnet"
    PHISHING = "phishing"
    RANSOMWARE = "ransomware"
    APT = "apt"
    CRYPTO_FRAUD = "crypto_fraud"
    DEFI_EXPLOIT = "defi_exploit"
    SMART_CONTRACT_VULNERABILITY = "smart_contract_vulnerability"
    EXCHANGE_HACK = "exchange_hack"
    WALLET_COMPROMISE = "wallet_compromise"
    UNKNOWN = "unknown"


class IOCType(Enum):
    """Types of Indicators of Compromise."""
    IP_ADDRESS = "ip_address"
    DOMAIN = "domain"
    URL = "url"
    FILE_HASH = "file_hash"
    EMAIL = "email"
    WALLET_ADDRESS = "wallet_address"
    CONTRACT_ADDRESS = "contract_address"
    USER_AGENT = "user_agent"
    SSL_CERT_HASH = "ssl_cert_hash"
    MUTEX = "mutex"


class ThreatSeverity(Enum):
    """Threat severity levels."""
    INFO = "info"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ThreatSource(Enum):
    """Threat intelligence sources."""
    INTERNAL = "internal"
    COMMERCIAL = "commercial"
    OPEN_SOURCE = "open_source"
    GOVERNMENT = "government"
    COMMUNITY = "community"
    HONEYPOT = "honeypot"


@dataclass
class IOC:
    """Indicator of Compromise."""
    id: str
    ioc_type: IOCType
    value: str
    threat_type: ThreatType
    severity: ThreatSeverity
    source: ThreatSource
    confidence: float
    first_seen: datetime
    last_seen: datetime
    description: str
    tags: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    ttl_hours: int = 168  # 7 days default


@dataclass
class ThreatIntelligenceReport:
    """Threat intelligence report."""
    report_id: str
    threat_type: ThreatType
    severity: ThreatSeverity
    title: str
    description: str
    iocs: List[IOC]
    attack_patterns: List[str]
    mitigation_strategies: List[str]
    source: ThreatSource
    published_date: datetime
    confidence: float
    tags: List[str] = field(default_factory=list)


@dataclass
class ThreatMatch:
    """Threat intelligence match result."""
    ioc: IOC
    matched_value: str
    match_type: str
    timestamp: datetime
    context: Dict[str, Any]
    risk_score: float


class ThreatIntelligenceEngine:
    """
    Advanced threat intelligence integration engine.
    
    Provides real-time threat intelligence feed integration, IOC management,
    and automated threat detection and response capabilities.
    """
    
    def __init__(self, audit_trail: Optional[AuditTrail] = None):
        self.audit_trail = audit_trail
        self.iocs: Dict[str, IOC] = {}
        self.threat_reports: List[ThreatIntelligenceReport] = []
        self.threat_matches: List[ThreatMatch] = []
        self.feed_sources = self._initialize_feed_sources()
        self._lock = threading.RLock()
        
        # Threat intelligence configuration
        self.update_interval_minutes = 15
        self.max_iocs = 100000
        self.confidence_threshold = 0.6
        self.auto_block_threshold = 0.8
        
        # Pattern matching
        self.crypto_patterns = self._initialize_crypto_patterns()
        self.attack_patterns = self._initialize_attack_patterns()
        
        # Statistics
        self.stats = {
            "total_iocs": 0,
            "active_feeds": 0,
            "matches_24h": 0,
            "last_update": None
        }
        
        logger.info("ThreatIntelligenceEngine initialized")
    
    def _initialize_feed_sources(self) -> Dict[str, Dict[str, Any]]:
        """Initialize threat intelligence feed sources."""
        return {
            # Open source threat intelligence feeds
            "abuse_ch_malware": {
                "url": "https://feodotracker.abuse.ch/downloads/ipblocklist.json",
                "type": "json",
                "ioc_type": IOCType.IP_ADDRESS,
                "threat_type": ThreatType.MALWARE,
                "update_interval": 3600,  # 1 hour
                "enabled": True,
                "source": ThreatSource.OPEN_SOURCE
            },
            "phishtank": {
                "url": "http://data.phishtank.com/data/online-valid.json",
                "type": "json",
                "ioc_type": IOCType.URL,
                "threat_type": ThreatType.PHISHING,
                "update_interval": 1800,  # 30 minutes
                "enabled": True,
                "source": ThreatSource.COMMUNITY
            },
            "crypto_scam_db": {
                "url": "https://api.cryptoscamdb.org/v1/addresses",
                "type": "json",
                "ioc_type": IOCType.WALLET_ADDRESS,
                "threat_type": ThreatType.CRYPTO_FRAUD,
                "update_interval": 3600,
                "enabled": True,
                "source": ThreatSource.COMMUNITY
            },
            # Internal honeypot data
            "internal_honeypot": {
                "type": "internal",
                "ioc_type": IOCType.IP_ADDRESS,
                "threat_type": ThreatType.BOTNET,
                "update_interval": 300,  # 5 minutes
                "enabled": True,
                "source": ThreatSource.HONEYPOT
            },
            # Custom crypto threat intelligence
            "defi_exploit_tracker": {
                "url": "https://api.defipulse.com/exploits",
                "type": "json",
                "ioc_type": IOCType.CONTRACT_ADDRESS,
                "threat_type": ThreatType.DEFI_EXPLOIT,
                "update_interval": 1800,
                "enabled": True,
                "source": ThreatSource.COMMUNITY
            }
        }
    
    def _initialize_crypto_patterns(self) -> Dict[str, List[str]]:
        """Initialize crypto-specific threat patterns."""
        return {
            "wallet_drainer_patterns": [
                r"approve.*0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
                r"transferFrom.*drain",
                r"setApprovalForAll.*true"
            ],
            "defi_exploit_patterns": [
                r"flashloan.*exploit",
                r"reentrancy.*attack",
                r"oracle.*manipulation",
                r"sandwich.*attack"
            ],
            "phishing_patterns": [
                r"metamask.*connect",
                r"wallet.*verification",
                r"claim.*airdrop.*urgent",
                r"security.*update.*required"
            ],
            "ponzi_patterns": [
                r"guaranteed.*returns",
                r"double.*your.*crypto",
                r"risk.*free.*investment",
                r"limited.*time.*offer.*crypto"
            ]
        }
    
    def _initialize_attack_patterns(self) -> Dict[str, List[str]]:
        """Initialize general attack patterns."""
        return {
            "api_abuse_patterns": [
                r"bulk.*download",
                r"scrape.*all.*data",
                r"enumerate.*endpoints",
                r"rate.*limit.*bypass"
            ],
            "injection_patterns": [
                r"union.*select.*from",
                r"drop.*table.*users",
                r"exec.*xp_cmdshell",
                r"<script.*>.*</script>"
            ],
            "reconnaissance_patterns": [
                r"\.well-known.*security",
                r"robots\.txt",
                r"sitemap\.xml",
                r"admin.*panel.*finder"
            ]
        }
    
    async def start_threat_intelligence_feeds(self):
        """Start threat intelligence feed updates."""
        logger.info("Starting threat intelligence feeds")
        
        # Start feed update tasks
        for feed_name, feed_config in self.feed_sources.items():
            if feed_config.get("enabled", False):
                asyncio.create_task(self._update_feed_loop(feed_name, feed_config))
        
        # Start cleanup task
        asyncio.create_task(self._cleanup_expired_iocs())
        
        logger.info("Threat intelligence feeds started")
    
    async def _update_feed_loop(self, feed_name: str, feed_config: Dict[str, Any]):
        """Update loop for a specific threat intelligence feed."""
        while True:
            try:
                await self._update_threat_feed(feed_name, feed_config)
                await asyncio.sleep(feed_config.get("update_interval", 3600))
            except Exception as e:
                logger.error(f"Failed to update threat feed {feed_name}: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retry
    
    async def _update_threat_feed(self, feed_name: str, feed_config: Dict[str, Any]):
        """Update a specific threat intelligence feed."""
        try:
            logger.debug(f"Updating threat feed: {feed_name}")
            
            if feed_config["type"] == "internal":
                await self._update_internal_feed(feed_name, feed_config)
            else:
                await self._update_external_feed(feed_name, feed_config)
            
            # Update statistics
            with self._lock:
                self.stats["last_update"] = datetime.now(timezone.utc)
                self.stats["active_feeds"] = sum(1 for f in self.feed_sources.values() if f.get("enabled"))
            
            increment_counter("threat_feed_updates", 1, {"feed": feed_name})
            logger.debug(f"Successfully updated threat feed: {feed_name}")
            
        except Exception as e:
            logger.error(f"Failed to update threat feed {feed_name}: {e}")
            increment_counter("threat_feed_errors", 1, {"feed": feed_name})
    
    async def _update_external_feed(self, feed_name: str, feed_config: Dict[str, Any]):
        """Update external threat intelligence feed."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(feed_config["url"], timeout=30) as response:
                    if response.status == 200:
                        data = await response.json()
                        await self._process_feed_data(feed_name, feed_config, data)
                    else:
                        logger.warning(f"Feed {feed_name} returned status {response.status}")
        except Exception as e:
            logger.error(f"Failed to fetch external feed {feed_name}: {e}")
    
    async def _update_internal_feed(self, feed_name: str, feed_config: Dict[str, Any]):
        """Update internal threat intelligence feed (e.g., honeypot data)."""
        try:
            # This would integrate with internal honeypot or security systems
            # For now, we'll simulate with some example data
            internal_data = await self._get_internal_threat_data(feed_name)
            await self._process_feed_data(feed_name, feed_config, internal_data)
        except Exception as e:
            logger.error(f"Failed to update internal feed {feed_name}: {e}")
    
    async def _get_internal_threat_data(self, feed_name: str) -> List[Dict[str, Any]]:
        """Get internal threat data (placeholder for actual implementation)."""
        # In a real implementation, this would query internal security systems
        return []

    async def _process_feed_data(self, feed_name: str, feed_config: Dict[str, Any], data: Any):
        """Process threat intelligence feed data."""
        try:
            iocs_added = 0

            if isinstance(data, list):
                for item in data:
                    ioc = await self._create_ioc_from_feed_item(feed_name, feed_config, item)
                    if ioc:
                        await self._add_ioc(ioc)
                        iocs_added += 1
            elif isinstance(data, dict):
                # Handle different feed formats
                if "data" in data:
                    for item in data["data"]:
                        ioc = await self._create_ioc_from_feed_item(feed_name, feed_config, item)
                        if ioc:
                            await self._add_ioc(ioc)
                            iocs_added += 1

            logger.info(f"Processed {iocs_added} IOCs from feed {feed_name}")
            increment_counter("threat_iocs_processed", iocs_added, {"feed": feed_name})

        except Exception as e:
            logger.error(f"Failed to process feed data for {feed_name}: {e}")

    async def _create_ioc_from_feed_item(self, feed_name: str, feed_config: Dict[str, Any],
                                       item: Dict[str, Any]) -> Optional[IOC]:
        """Create IOC from feed item."""
        try:
            # Extract IOC value based on feed type
            ioc_value = None
            if feed_config["ioc_type"] == IOCType.IP_ADDRESS:
                ioc_value = item.get("ip") or item.get("ip_address") or item.get("address")
            elif feed_config["ioc_type"] == IOCType.DOMAIN:
                ioc_value = item.get("domain") or item.get("hostname")
            elif feed_config["ioc_type"] == IOCType.URL:
                ioc_value = item.get("url") or item.get("link")
            elif feed_config["ioc_type"] == IOCType.WALLET_ADDRESS:
                ioc_value = item.get("address") or item.get("wallet")
            elif feed_config["ioc_type"] == IOCType.CONTRACT_ADDRESS:
                ioc_value = item.get("contract") or item.get("address")

            if not ioc_value:
                return None

            # Generate IOC ID
            ioc_id = hashlib.md5(f"{feed_name}_{ioc_value}".encode()).hexdigest()

            # Extract metadata
            confidence = float(item.get("confidence", 0.7))
            description = item.get("description", f"IOC from {feed_name}")
            tags = item.get("tags", [])

            # Create IOC
            current_time = datetime.now(timezone.utc)
            ioc = IOC(
                id=ioc_id,
                ioc_type=feed_config["ioc_type"],
                value=ioc_value,
                threat_type=feed_config["threat_type"],
                severity=ThreatSeverity(item.get("severity", "medium")),
                source=feed_config["source"],
                confidence=confidence,
                first_seen=current_time,
                last_seen=current_time,
                description=description,
                tags=tags,
                context={"feed": feed_name, "raw_data": item}
            )

            return ioc

        except Exception as e:
            logger.error(f"Failed to create IOC from feed item: {e}")
            return None

    async def _add_ioc(self, ioc: IOC):
        """Add or update IOC in the database."""
        try:
            with self._lock:
                existing_ioc = self.iocs.get(ioc.id)

                if existing_ioc:
                    # Update existing IOC
                    existing_ioc.last_seen = ioc.last_seen
                    existing_ioc.confidence = max(existing_ioc.confidence, ioc.confidence)
                    existing_ioc.tags = list(set(existing_ioc.tags + ioc.tags))
                else:
                    # Add new IOC
                    self.iocs[ioc.id] = ioc
                    self.stats["total_iocs"] = len(self.iocs)

                # Enforce IOC limit
                if len(self.iocs) > self.max_iocs:
                    await self._cleanup_old_iocs()

        except Exception as e:
            logger.error(f"Failed to add IOC {ioc.id}: {e}")

    async def check_threat_intelligence(self, indicators: Dict[str, Any]) -> List[ThreatMatch]:
        """Check indicators against threat intelligence database."""
        matches = []

        try:
            # Check different types of indicators
            for indicator_type, value in indicators.items():
                if not value:
                    continue

                # Direct IOC matching
                direct_matches = await self._check_direct_ioc_match(indicator_type, value)
                matches.extend(direct_matches)

                # Pattern-based matching
                pattern_matches = await self._check_pattern_matches(indicator_type, value)
                matches.extend(pattern_matches)

                # Fuzzy matching for similar indicators
                fuzzy_matches = await self._check_fuzzy_matches(indicator_type, value)
                matches.extend(fuzzy_matches)

            # Update statistics
            if matches:
                with self._lock:
                    self.stats["matches_24h"] += len(matches)
                    self.threat_matches.extend(matches)

                    # Keep only recent matches
                    cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
                    self.threat_matches = [
                        m for m in self.threat_matches if m.timestamp > cutoff_time
                    ]

                increment_counter("threat_matches_found", len(matches))
                set_gauge("threat_intelligence_matches_24h", self.stats["matches_24h"])

        except Exception as e:
            logger.error(f"Failed to check threat intelligence: {e}")

        return matches

    async def _check_direct_ioc_match(self, indicator_type: str, value: str) -> List[ThreatMatch]:
        """Check for direct IOC matches."""
        matches = []

        try:
            # Map indicator types to IOC types
            ioc_type_mapping = {
                "ip_address": IOCType.IP_ADDRESS,
                "domain": IOCType.DOMAIN,
                "url": IOCType.URL,
                "wallet_address": IOCType.WALLET_ADDRESS,
                "contract_address": IOCType.CONTRACT_ADDRESS,
                "user_agent": IOCType.USER_AGENT
            }

            target_ioc_type = ioc_type_mapping.get(indicator_type)
            if not target_ioc_type:
                return matches

            # Search for matching IOCs
            with self._lock:
                for ioc in self.iocs.values():
                    if (ioc.ioc_type == target_ioc_type and
                        ioc.value.lower() == value.lower() and
                        ioc.confidence >= self.confidence_threshold):

                        match = ThreatMatch(
                            ioc=ioc,
                            matched_value=value,
                            match_type="direct",
                            timestamp=datetime.now(timezone.utc),
                            context={"indicator_type": indicator_type},
                            risk_score=ioc.confidence * self._get_severity_multiplier(ioc.severity)
                        )
                        matches.append(match)

        except Exception as e:
            logger.error(f"Failed to check direct IOC match: {e}")

        return matches

    async def _check_pattern_matches(self, indicator_type: str, value: str) -> List[ThreatMatch]:
        """Check for pattern-based matches."""
        matches = []

        try:
            # Check crypto-specific patterns
            if indicator_type in ["url", "content", "payload"]:
                for pattern_category, patterns in self.crypto_patterns.items():
                    for pattern in patterns:
                        if re.search(pattern, value, re.IGNORECASE):
                            # Create synthetic IOC for pattern match
                            ioc = IOC(
                                id=f"pattern_{hashlib.md5(pattern.encode()).hexdigest()[:8]}",
                                ioc_type=IOCType.URL,
                                value=pattern,
                                threat_type=ThreatType.CRYPTO_FRAUD,
                                severity=ThreatSeverity.HIGH,
                                source=ThreatSource.INTERNAL,
                                confidence=0.8,
                                first_seen=datetime.now(timezone.utc),
                                last_seen=datetime.now(timezone.utc),
                                description=f"Pattern match: {pattern_category}",
                                tags=[pattern_category, "pattern_match"]
                            )

                            match = ThreatMatch(
                                ioc=ioc,
                                matched_value=value,
                                match_type="pattern",
                                timestamp=datetime.now(timezone.utc),
                                context={
                                    "pattern_category": pattern_category,
                                    "matched_pattern": pattern
                                },
                                risk_score=0.7
                            )
                            matches.append(match)

            # Check general attack patterns
            for pattern_category, patterns in self.attack_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, value, re.IGNORECASE):
                        ioc = IOC(
                            id=f"attack_pattern_{hashlib.md5(pattern.encode()).hexdigest()[:8]}",
                            ioc_type=IOCType.URL,
                            value=pattern,
                            threat_type=ThreatType.MALWARE,
                            severity=ThreatSeverity.MEDIUM,
                            source=ThreatSource.INTERNAL,
                            confidence=0.7,
                            first_seen=datetime.now(timezone.utc),
                            last_seen=datetime.now(timezone.utc),
                            description=f"Attack pattern: {pattern_category}",
                            tags=[pattern_category, "attack_pattern"]
                        )

                        match = ThreatMatch(
                            ioc=ioc,
                            matched_value=value,
                            match_type="attack_pattern",
                            timestamp=datetime.now(timezone.utc),
                            context={
                                "pattern_category": pattern_category,
                                "matched_pattern": pattern
                            },
                            risk_score=0.6
                        )
                        matches.append(match)

        except Exception as e:
            logger.error(f"Failed to check pattern matches: {e}")

        return matches

    async def _check_fuzzy_matches(self, indicator_type: str, value: str) -> List[ThreatMatch]:
        """Check for fuzzy/similarity-based matches."""
        matches = []

        try:
            # For now, implement basic substring matching for domains and URLs
            if indicator_type in ["domain", "url"]:
                with self._lock:
                    for ioc in self.iocs.values():
                        if (ioc.ioc_type in [IOCType.DOMAIN, IOCType.URL] and
                            ioc.confidence >= self.confidence_threshold):

                            # Check for substring matches (e.g., suspicious domains)
                            if (len(ioc.value) > 5 and ioc.value.lower() in value.lower()) or \
                               (len(value) > 5 and value.lower() in ioc.value.lower()):

                                match = ThreatMatch(
                                    ioc=ioc,
                                    matched_value=value,
                                    match_type="fuzzy",
                                    timestamp=datetime.now(timezone.utc),
                                    context={"similarity_type": "substring"},
                                    risk_score=ioc.confidence * 0.7  # Lower confidence for fuzzy matches
                                )
                                matches.append(match)

        except Exception as e:
            logger.error(f"Failed to check fuzzy matches: {e}")

        return matches

    def _get_severity_multiplier(self, severity: ThreatSeverity) -> float:
        """Get risk score multiplier based on threat severity."""
        multipliers = {
            ThreatSeverity.INFO: 0.2,
            ThreatSeverity.LOW: 0.4,
            ThreatSeverity.MEDIUM: 0.6,
            ThreatSeverity.HIGH: 0.8,
            ThreatSeverity.CRITICAL: 1.0
        }
        return multipliers.get(severity, 0.6)

    async def _cleanup_expired_iocs(self):
        """Cleanup expired IOCs periodically."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour

                current_time = datetime.now(timezone.utc)
                expired_count = 0

                with self._lock:
                    expired_iocs = []
                    for ioc_id, ioc in self.iocs.items():
                        # Check if IOC has expired
                        expiry_time = ioc.last_seen + timedelta(hours=ioc.ttl_hours)
                        if current_time > expiry_time:
                            expired_iocs.append(ioc_id)

                    # Remove expired IOCs
                    for ioc_id in expired_iocs:
                        del self.iocs[ioc_id]
                        expired_count += 1

                    self.stats["total_iocs"] = len(self.iocs)

                if expired_count > 0:
                    logger.info(f"Cleaned up {expired_count} expired IOCs")
                    increment_counter("threat_iocs_expired", expired_count)

            except Exception as e:
                logger.error(f"Failed to cleanup expired IOCs: {e}")

    async def _cleanup_old_iocs(self):
        """Remove oldest IOCs when limit is exceeded."""
        try:
            with self._lock:
                if len(self.iocs) <= self.max_iocs:
                    return

                # Sort IOCs by last_seen timestamp
                sorted_iocs = sorted(
                    self.iocs.items(),
                    key=lambda x: x[1].last_seen
                )

                # Remove oldest IOCs
                remove_count = len(self.iocs) - self.max_iocs + 1000  # Remove extra for buffer
                for i in range(remove_count):
                    ioc_id, _ = sorted_iocs[i]
                    del self.iocs[ioc_id]

                self.stats["total_iocs"] = len(self.iocs)
                logger.info(f"Removed {remove_count} old IOCs to maintain limit")

        except Exception as e:
            logger.error(f"Failed to cleanup old IOCs: {e}")

    def generate_threat_intelligence_report(self) -> Dict[str, Any]:
        """Generate comprehensive threat intelligence report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Basic statistics
                total_iocs = len(self.iocs)
                active_feeds = sum(1 for f in self.feed_sources.values() if f.get("enabled"))

                # IOC distribution by type
                ioc_type_stats = defaultdict(int)
                threat_type_stats = defaultdict(int)
                severity_stats = defaultdict(int)
                source_stats = defaultdict(int)

                for ioc in self.iocs.values():
                    ioc_type_stats[ioc.ioc_type.value] += 1
                    threat_type_stats[ioc.threat_type.value] += 1
                    severity_stats[ioc.severity.value] += 1
                    source_stats[ioc.source.value] += 1

                # Recent matches analysis
                recent_matches = [
                    m for m in self.threat_matches
                    if m.timestamp > current_time - timedelta(hours=24)
                ]

                match_type_stats = defaultdict(int)
                high_risk_matches = []

                for match in recent_matches:
                    match_type_stats[match.match_type] += 1
                    if match.risk_score > 0.8:
                        high_risk_matches.append({
                            "matched_value": match.matched_value,
                            "threat_type": match.ioc.threat_type.value,
                            "severity": match.ioc.severity.value,
                            "risk_score": match.risk_score,
                            "timestamp": match.timestamp.isoformat()
                        })

                # Top threat types
                top_threat_types = sorted(
                    threat_type_stats.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:10]

                return {
                    "report_generated_at": current_time.isoformat(),
                    "summary": {
                        "total_iocs": total_iocs,
                        "active_feeds": active_feeds,
                        "matches_24h": len(recent_matches),
                        "high_risk_matches_24h": len(high_risk_matches),
                        "last_feed_update": self.stats["last_update"].isoformat() if self.stats["last_update"] else None
                    },
                    "ioc_distribution": {
                        "by_type": dict(ioc_type_stats),
                        "by_threat_type": dict(threat_type_stats),
                        "by_severity": dict(severity_stats),
                        "by_source": dict(source_stats)
                    },
                    "match_analysis": {
                        "by_match_type": dict(match_type_stats),
                        "high_risk_matches": high_risk_matches[:20]  # Top 20
                    },
                    "top_threat_types": [{"threat_type": tt, "count": count} for tt, count in top_threat_types],
                    "feed_status": {
                        feed_name: {
                            "enabled": feed_config.get("enabled", False),
                            "last_update": "unknown",  # Would track per-feed in production
                            "ioc_type": feed_config.get("ioc_type", {}).value if hasattr(feed_config.get("ioc_type", {}), 'value') else str(feed_config.get("ioc_type", "unknown"))
                        }
                        for feed_name, feed_config in self.feed_sources.items()
                    },
                    "recommendations": self._generate_threat_recommendations(threat_type_stats, severity_stats, recent_matches)
                }

        except Exception as e:
            logger.error(f"Failed to generate threat intelligence report: {e}")
            return {"error": str(e)}

    def _generate_threat_recommendations(self, threat_type_stats: Dict[str, int],
                                       severity_stats: Dict[str, int],
                                       recent_matches: List[ThreatMatch]) -> List[str]:
        """Generate threat intelligence recommendations."""
        recommendations = []

        try:
            # Check for high threat activity
            total_threats = sum(threat_type_stats.values())
            if total_threats > 10000:
                recommendations.append("High threat intelligence volume - consider increasing IOC retention limits")

            # Check for critical threats
            critical_threats = severity_stats.get("critical", 0)
            if critical_threats > 100:
                recommendations.append("CRITICAL: High number of critical threats detected - immediate review required")

            # Check for crypto-specific threats
            crypto_threats = (threat_type_stats.get("crypto_fraud", 0) +
                            threat_type_stats.get("defi_exploit", 0) +
                            threat_type_stats.get("wallet_compromise", 0))
            if crypto_threats > 50:
                recommendations.append("Elevated crypto threat activity - enhance wallet and DeFi monitoring")

            # Check for recent high-risk matches
            high_risk_recent = sum(1 for m in recent_matches if m.risk_score > 0.8)
            if high_risk_recent > 10:
                recommendations.append("Multiple high-risk threat matches in last 24h - investigate immediately")

            # Check for botnet activity
            if threat_type_stats.get("botnet", 0) > 20:
                recommendations.append("Botnet activity detected - implement enhanced IP blocking")

            # Check for APT indicators
            if threat_type_stats.get("apt", 0) > 5:
                recommendations.append("APT indicators detected - initiate advanced threat hunting procedures")

            if not recommendations:
                recommendations.append("Threat intelligence posture is normal - continue monitoring")

        except Exception as e:
            logger.error(f"Failed to generate threat recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations

    def get_ioc_details(self, ioc_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific IOC."""
        try:
            with self._lock:
                ioc = self.iocs.get(ioc_id)
                if not ioc:
                    return None

                # Get related matches
                related_matches = [
                    {
                        "matched_value": match.matched_value,
                        "match_type": match.match_type,
                        "risk_score": match.risk_score,
                        "timestamp": match.timestamp.isoformat(),
                        "context": match.context
                    }
                    for match in self.threat_matches
                    if match.ioc.id == ioc_id
                ]

                return {
                    "ioc": {
                        "id": ioc.id,
                        "type": ioc.ioc_type.value,
                        "value": ioc.value,
                        "threat_type": ioc.threat_type.value,
                        "severity": ioc.severity.value,
                        "source": ioc.source.value,
                        "confidence": ioc.confidence,
                        "first_seen": ioc.first_seen.isoformat(),
                        "last_seen": ioc.last_seen.isoformat(),
                        "description": ioc.description,
                        "tags": ioc.tags,
                        "ttl_hours": ioc.ttl_hours
                    },
                    "statistics": {
                        "total_matches": len(related_matches),
                        "recent_matches_24h": len([m for m in related_matches
                                                 if datetime.fromisoformat(m["timestamp"].replace('Z', '+00:00')) >
                                                 datetime.now(timezone.utc) - timedelta(hours=24)])
                    },
                    "related_matches": related_matches[-10:]  # Last 10 matches
                }

        except Exception as e:
            logger.error(f"Failed to get IOC details for {ioc_id}: {e}")
            return None


# Global instance
threat_intelligence_engine = ThreatIntelligenceEngine()
