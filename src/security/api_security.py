"""
2025 API Security Standards Implementation
Advanced API security with request signing, IP whitelisting, rate limiting, and comprehensive validation.
"""

import hashlib
import hmac
import json
import time
import uuid
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union
import ipaddress
import secrets
import threading
from functools import wraps

import structlog

try:
    from fastapi import HTTPException, Request, Response
    from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
    from pydantic import BaseModel, validator
except ImportError:
    # Mock for testing
    HTTPException = Exception
    Request = object
    Response = object
    HTTPBearer = object
    HTTPAuthorizationCredentials = object
    BaseModel = object
    validator = lambda x: x

from ..core.config import config
from ..core.logging_config import get_logger, CorrelationContext
from ..monitoring import increment_counter
from .api_inventory import api_inventory_manager
from .bopla_detector import bopla_detector
from .ai_bot_detector import ai_bot_detector, set_gauge, record_timer


logger = get_logger(__name__)


class SecurityLevel(Enum):
    """Security levels for different operations."""
    PUBLIC = "public"
    AUTHENTICATED = "authenticated"
    PRIVILEGED = "privileged"
    ADMIN = "admin"


class ThreatLevel(Enum):
    """Threat assessment levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityContext:
    """Security context for requests."""
    user_id: Optional[str] = None
    api_key: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    correlation_id: Optional[str] = None
    security_level: SecurityLevel = SecurityLevel.PUBLIC
    threat_level: ThreatLevel = ThreatLevel.LOW
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return {
            "user_id": self.user_id,
            "api_key": self.api_key[:8] + "..." if self.api_key else None,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "timestamp": self.timestamp.isoformat(),
            "correlation_id": self.correlation_id,
            "security_level": self.security_level.value,
            "threat_level": self.threat_level.value,
            "metadata": self.metadata
        }


class RequestSignature:
    """Request signature validation using HMAC."""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key.encode('utf-8')
        logger.info("RequestSignature initialized")
    
    def generate_signature(self, method: str, path: str, body: str, 
                          timestamp: str, nonce: str) -> str:
        """Generate HMAC signature for request."""
        message = f"{method}|{path}|{body}|{timestamp}|{nonce}"
        signature = hmac.new(
            self.secret_key,
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def validate_signature(self, method: str, path: str, body: str,
                          timestamp: str, nonce: str, signature: str) -> bool:
        """Validate request signature."""
        expected_signature = self.generate_signature(method, path, body, timestamp, nonce)
        
        # Use constant-time comparison to prevent timing attacks
        return hmac.compare_digest(signature, expected_signature)
    
    def validate_timestamp(self, timestamp: str, max_age_seconds: int = 300) -> bool:
        """Validate request timestamp to prevent replay attacks."""
        try:
            request_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            current_time = datetime.now(timezone.utc)
            age = (current_time - request_time).total_seconds()
            
            return 0 <= age <= max_age_seconds
        except (ValueError, TypeError):
            return False


class IPWhitelist:
    """IP address whitelisting with CIDR support."""
    
    def __init__(self):
        self.whitelist: Set[ipaddress.IPv4Network] = set()
        self.blacklist: Set[ipaddress.IPv4Network] = set()
        self._lock = threading.RLock()
        
        # Add default safe networks
        self._add_default_whitelist()
        
        logger.info("IPWhitelist initialized")
    
    def _add_default_whitelist(self):
        """Add default whitelisted networks."""
        default_whitelist = [
            "*********/8",    # Localhost
            "10.0.0.0/8",     # Private network
            "**********/12",  # Private network
            "***********/16", # Private network
        ]
        
        for network in default_whitelist:
            self.add_to_whitelist(network)
    
    def add_to_whitelist(self, network: str):
        """Add network to whitelist."""
        with self._lock:
            try:
                network_obj = ipaddress.IPv4Network(network, strict=False)
                self.whitelist.add(network_obj)
                logger.info("Network added to whitelist", network=network)
            except ValueError as e:
                logger.error("Invalid network for whitelist", network=network, error=str(e))
    
    def add_to_blacklist(self, network: str):
        """Add network to blacklist."""
        with self._lock:
            try:
                network_obj = ipaddress.IPv4Network(network, strict=False)
                self.blacklist.add(network_obj)
                logger.warning("Network added to blacklist", network=network)
            except ValueError as e:
                logger.error("Invalid network for blacklist", network=network, error=str(e))
    
    def is_allowed(self, ip_address: str) -> bool:
        """Check if IP address is allowed."""
        with self._lock:
            try:
                ip = ipaddress.IPv4Address(ip_address)
                
                # Check blacklist first
                for network in self.blacklist:
                    if ip in network:
                        logger.warning("IP blocked by blacklist", ip=ip_address)
                        return False
                
                # Check whitelist
                for network in self.whitelist:
                    if ip in network:
                        return True
                
                # Not in whitelist
                logger.warning("IP not in whitelist", ip=ip_address)
                return False
                
            except ValueError:
                logger.error("Invalid IP address", ip=ip_address)
                return False


class AdvancedRateLimiter:
    """Advanced rate limiting with multiple algorithms and user-specific limits."""
    
    def __init__(self):
        self.token_buckets: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.sliding_windows: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.user_limits: Dict[str, Dict[str, int]] = defaultdict(dict)
        self._lock = threading.RLock()
        
        # Default rate limits
        self.default_limits = {
            "requests_per_minute": 60,
            "requests_per_hour": 1000,
            "burst_limit": 10
        }
        
        logger.info("AdvancedRateLimiter initialized")
    
    def set_user_limits(self, user_id: str, limits: Dict[str, int]):
        """Set custom rate limits for a user."""
        with self._lock:
            self.user_limits[user_id] = limits
            logger.info("Custom rate limits set", user_id=user_id, limits=limits)
    
    def check_rate_limit(self, identifier: str, user_id: Optional[str] = None) -> bool:
        """Check if request is within rate limits."""
        with self._lock:
            current_time = time.time()
            
            # Get applicable limits
            limits = self.user_limits.get(user_id, self.default_limits)
            
            # Token bucket algorithm for burst control
            if not self._check_token_bucket(identifier, limits.get("burst_limit", 10), current_time):
                increment_counter("rate_limit_exceeded", 1, {"type": "burst", "identifier": identifier})
                return False
            
            # Sliding window for sustained rate
            if not self._check_sliding_window(identifier, limits.get("requests_per_minute", 60), 60, current_time):
                increment_counter("rate_limit_exceeded", 1, {"type": "sustained", "identifier": identifier})
                return False
            
            return True
    
    def _check_token_bucket(self, identifier: str, limit: int, current_time: float) -> bool:
        """Check token bucket rate limit."""
        bucket = self.token_buckets[identifier]
        
        if "tokens" not in bucket:
            bucket["tokens"] = limit
            bucket["last_refill"] = current_time
        
        # Refill tokens
        time_passed = current_time - bucket["last_refill"]
        tokens_to_add = time_passed * (limit / 60)  # Refill rate per second
        bucket["tokens"] = min(limit, bucket["tokens"] + tokens_to_add)
        bucket["last_refill"] = current_time
        
        # Check if tokens available
        if bucket["tokens"] >= 1:
            bucket["tokens"] -= 1
            return True
        
        return False
    
    def _check_sliding_window(self, identifier: str, limit: int, window_seconds: int, current_time: float) -> bool:
        """Check sliding window rate limit."""
        window = self.sliding_windows[identifier]
        
        # Remove old entries
        cutoff_time = current_time - window_seconds
        while window and window[0] < cutoff_time:
            window.popleft()
        
        # Check limit
        if len(window) < limit:
            window.append(current_time)
            return True
        
        return False
    
    def get_rate_limit_status(self, identifier: str) -> Dict[str, Any]:
        """Get current rate limit status."""
        with self._lock:
            current_time = time.time()
            
            bucket = self.token_buckets.get(identifier, {})
            window = self.sliding_windows.get(identifier, deque())
            
            # Clean old entries
            cutoff_time = current_time - 60
            while window and window[0] < cutoff_time:
                window.popleft()
            
            return {
                "tokens_remaining": bucket.get("tokens", 0),
                "requests_in_window": len(window),
                "window_reset_time": current_time + 60,
                "timestamp": current_time
            }


class ThreatDetector:
    """Advanced threat detection and analysis."""
    
    def __init__(self):
        self.suspicious_patterns = {
            "sql_injection": [
                r"union\s+select", r"drop\s+table", r"insert\s+into",
                r"delete\s+from", r"update\s+set", r"exec\s*\("
            ],
            "xss": [
                r"<script", r"javascript:", r"onerror=", r"onload=",
                r"eval\s*\(", r"document\.cookie"
            ],
            "path_traversal": [
                r"\.\./", r"\.\.\\", r"%2e%2e%2f", r"%2e%2e%5c"
            ],
            "command_injection": [
                r";\s*rm\s", r";\s*cat\s", r";\s*ls\s", r"&&\s*rm\s",
                r"\|\s*nc\s", r">\s*/dev/"
            ]
        }
        
        self.threat_scores: Dict[str, float] = defaultdict(float)
        self.request_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self._lock = threading.RLock()
        
        logger.info("ThreatDetector initialized")
    
    def analyze_request(self, request_data: Dict[str, Any], 
                       security_context: SecurityContext) -> ThreatLevel:
        """Analyze request for threats."""
        with self._lock:
            threat_score = 0.0
            detected_threats = []
            
            # Analyze request content
            content = json.dumps(request_data).lower()
            
            for threat_type, patterns in self.suspicious_patterns.items():
                import re
                for pattern in patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        threat_score += 10.0
                        detected_threats.append(threat_type)
                        logger.warning("Suspicious pattern detected",
                                     threat_type=threat_type,
                                     pattern=pattern,
                                     ip=security_context.ip_address)
            
            # Analyze request frequency
            if security_context.ip_address:
                history = self.request_history[security_context.ip_address]
                history.append(time.time())
                
                # Check for rapid requests
                recent_requests = [t for t in history if time.time() - t < 60]
                if len(recent_requests) > 100:
                    threat_score += 20.0
                    detected_threats.append("rapid_requests")
            
            # Update threat score
            identifier = security_context.ip_address or "unknown"
            self.threat_scores[identifier] = max(self.threat_scores[identifier] * 0.9, threat_score)
            
            # Determine threat level
            total_score = self.threat_scores[identifier]
            if total_score >= 50:
                threat_level = ThreatLevel.CRITICAL
            elif total_score >= 30:
                threat_level = ThreatLevel.HIGH
            elif total_score >= 10:
                threat_level = ThreatLevel.MEDIUM
            else:
                threat_level = ThreatLevel.LOW
            
            if detected_threats:
                logger.warning("Threats detected",
                             threats=detected_threats,
                             threat_score=total_score,
                             threat_level=threat_level.value,
                             ip=security_context.ip_address)
                
                increment_counter("threats_detected", 1, {
                    "threat_level": threat_level.value,
                    "ip": security_context.ip_address or "unknown"
                })
            
            return threat_level


class InputValidator:
    """Comprehensive input validation with sanitization."""
    
    def __init__(self):
        self.max_string_length = 10000
        self.max_array_length = 1000
        self.max_object_depth = 10
        
        logger.info("InputValidator initialized")
    
    def validate_and_sanitize(self, data: Any, depth: int = 0) -> Any:
        """Validate and sanitize input data."""
        if depth > self.max_object_depth:
            raise ValueError(f"Object depth exceeds maximum of {self.max_object_depth}")
        
        if isinstance(data, str):
            return self._sanitize_string(data)
        elif isinstance(data, dict):
            return self._sanitize_dict(data, depth)
        elif isinstance(data, list):
            return self._sanitize_list(data, depth)
        elif isinstance(data, (int, float, bool)) or data is None:
            return data
        else:
            # Convert unknown types to string and sanitize
            return self._sanitize_string(str(data))
    
    def _sanitize_string(self, value: str) -> str:
        """Sanitize string input."""
        if len(value) > self.max_string_length:
            raise ValueError(f"String length exceeds maximum of {self.max_string_length}")
        
        # Remove null bytes and control characters
        sanitized = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
        
        # Basic HTML/script tag removal
        import re
        sanitized = re.sub(r'<script[^>]*>.*?</script>', '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        sanitized = re.sub(r'<[^>]+>', '', sanitized)
        
        return sanitized
    
    def _sanitize_dict(self, data: dict, depth: int) -> dict:
        """Sanitize dictionary input."""
        if len(data) > 100:  # Limit number of keys
            raise ValueError("Dictionary has too many keys")
        
        sanitized = {}
        for key, value in data.items():
            sanitized_key = self._sanitize_string(str(key))
            sanitized_value = self.validate_and_sanitize(value, depth + 1)
            sanitized[sanitized_key] = sanitized_value
        
        return sanitized
    
    def _sanitize_list(self, data: list, depth: int) -> list:
        """Sanitize list input."""
        if len(data) > self.max_array_length:
            raise ValueError(f"Array length exceeds maximum of {self.max_array_length}")
        
        return [self.validate_and_sanitize(item, depth + 1) for item in data]


# Global security instances
request_signer = RequestSignature(config.security.secret_key if hasattr(config, 'security') else "default-secret-key")
ip_whitelist = IPWhitelist()
rate_limiter = AdvancedRateLimiter()
threat_detector = ThreatDetector()
input_validator = InputValidator()


class SecurityMiddleware:
    """Comprehensive security middleware for FastAPI."""
    
    def __init__(self):
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
        
        logger.info("SecurityMiddleware initialized")
    
    async def __call__(self, request: Request, call_next):
        """Process request through security middleware."""
        start_time = time.time()
        
        try:
            # Create security context
            security_context = await self._create_security_context(request)
            
            # Security checks
            await self._perform_security_checks(request, security_context)
            
            # Process request
            response = await call_next(request)

            # Add security headers
            self._add_security_headers(response)

            # Monitor API traffic for inventory management
            duration = (time.time() - start_time) * 1000
            try:
                # Get request and response bodies for analysis (if small enough)
                request_body = None
                response_body = None

                # Only capture small payloads for analysis
                if hasattr(request, '_body') and len(getattr(request, '_body', b'')) < 10000:
                    request_body = getattr(request, '_body', b'').decode('utf-8', errors='ignore')

                if hasattr(response, 'body') and len(getattr(response, 'body', b'')) < 10000:
                    response_body = getattr(response, 'body', b'').decode('utf-8', errors='ignore')

                # Monitor traffic asynchronously
                asyncio.create_task(
                    api_inventory_manager.monitor_api_traffic(
                        request, response, duration, request_body, response_body
                    )
                )

                # Monitor for BOPLA violations
                try:
                    # Parse request and response data for field-level analysis
                    request_data = None
                    response_data = None

                    if request_body:
                        try:
                            request_data = json.loads(request_body)
                        except json.JSONDecodeError:
                            pass  # Not JSON data

                    if response_body:
                        try:
                            response_data = json.loads(response_body)
                        except json.JSONDecodeError:
                            pass  # Not JSON data

                    # Extract user context from request
                    user_context = {
                        "user_id": request.headers.get("x-user-id"),
                        "role": request.headers.get("x-user-role", "anonymous"),
                        "authenticated": bool(request.headers.get("authorization"))
                    }

                    # Monitor field access asynchronously
                    asyncio.create_task(
                        bopla_detector.monitor_field_access(
                            request, response, request_data, response_data, user_context
                        )
                    )

                    # AI-powered bot detection
                    asyncio.create_task(
                        ai_bot_detector.analyze_request(
                            request, response, request_data, response_data
                        )
                    )
                except Exception as e:
                    logger.debug(f"Security monitoring failed: {e}")
            except Exception as e:
                logger.debug(f"API inventory monitoring failed: {e}")

            # Log successful request
            logger.info("Request processed successfully",
                       method=request.method,
                       path=request.url.path,
                       duration_ms=duration,
                       security_level=security_context.security_level.value)
            
            record_timer("request_duration", duration, {
                "method": request.method,
                "security_level": security_context.security_level.value
            })
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.exception("Security middleware error", error=str(e))
            raise HTTPException(status_code=500, detail="Internal security error")
    
    async def _create_security_context(self, request: Request) -> SecurityContext:
        """Create security context from request."""
        # Get client IP (handle proxy headers)
        client_ip = request.headers.get("X-Forwarded-For", "").split(",")[0].strip()
        if not client_ip:
            client_ip = request.headers.get("X-Real-IP", "")
        if not client_ip and request.client:
            client_ip = request.client.host
        
        return SecurityContext(
            ip_address=client_ip,
            user_agent=request.headers.get("User-Agent", ""),
            correlation_id=request.headers.get("X-Correlation-ID"),
            timestamp=datetime.now(timezone.utc)
        )
    
    async def _perform_security_checks(self, request: Request, security_context: SecurityContext):
        """Perform comprehensive security checks."""
        
        # IP whitelist check
        if security_context.ip_address and not ip_whitelist.is_allowed(security_context.ip_address):
            logger.warning("IP not whitelisted", ip=security_context.ip_address)
            increment_counter("security_violations", 1, {"type": "ip_not_whitelisted"})
            raise HTTPException(status_code=403, detail="IP address not allowed")
        
        # Rate limiting
        identifier = security_context.ip_address or "unknown"
        if not rate_limiter.check_rate_limit(identifier, security_context.user_id):
            logger.warning("Rate limit exceeded", identifier=identifier)
            increment_counter("security_violations", 1, {"type": "rate_limit_exceeded"})
            raise HTTPException(status_code=429, detail="Rate limit exceeded")
        
        # Threat detection
        try:
            body = await request.body()
            request_data = {
                "method": request.method,
                "path": str(request.url.path),
                "query": str(request.url.query),
                "headers": dict(request.headers),
                "body": body.decode('utf-8', errors='ignore') if body else ""
            }
            
            threat_level = threat_detector.analyze_request(request_data, security_context)
            security_context.threat_level = threat_level
            
            if threat_level in [ThreatLevel.HIGH, ThreatLevel.CRITICAL]:
                logger.error("High threat level detected",
                           threat_level=threat_level.value,
                           ip=security_context.ip_address)
                increment_counter("security_violations", 1, {"type": "high_threat"})
                raise HTTPException(status_code=403, detail="Request blocked due to security concerns")
                
        except UnicodeDecodeError:
            logger.warning("Invalid request body encoding", ip=security_context.ip_address)
            increment_counter("security_violations", 1, {"type": "invalid_encoding"})
            raise HTTPException(status_code=400, detail="Invalid request encoding")
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response."""
        for header, value in self.security_headers.items():
            response.headers[header] = value


# Security decorators
def require_security_level(level: SecurityLevel):
    """Decorator to require specific security level."""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Security level check would be implemented here
            # For now, just log the requirement
            logger.info("Security level required", level=level.value, function=func.__name__)
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def validate_input(func):
    """Decorator to validate and sanitize input."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Sanitize kwargs
        sanitized_kwargs = {}
        for key, value in kwargs.items():
            try:
                sanitized_kwargs[key] = input_validator.validate_and_sanitize(value)
            except ValueError as e:
                logger.warning("Input validation failed", key=key, error=str(e))
                raise HTTPException(status_code=400, detail=f"Invalid input for {key}: {str(e)}")
        
        return await func(*args, **sanitized_kwargs)
    return wrapper


# Initialize security middleware
security_middleware = SecurityMiddleware()


def get_security_status() -> Dict[str, Any]:
    """Get comprehensive security system status."""
    return {
        "ip_whitelist": {
            "whitelist_count": len(ip_whitelist.whitelist),
            "blacklist_count": len(ip_whitelist.blacklist)
        },
        "rate_limiter": {
            "active_buckets": len(rate_limiter.token_buckets),
            "active_windows": len(rate_limiter.sliding_windows),
            "custom_limits": len(rate_limiter.user_limits)
        },
        "threat_detector": {
            "tracked_ips": len(threat_detector.threat_scores),
            "pattern_categories": len(threat_detector.suspicious_patterns)
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
