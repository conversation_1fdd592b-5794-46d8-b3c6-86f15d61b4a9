"""
Conservative Risk Threshold Calibration System

This module implements gradual threshold tuning with A/B testing to reduce false positives
while maintaining security, using production data for calibration. It provides systematic
threshold optimization across all security systems.

Features:
- A/B testing framework for threshold optimization
- Gradual threshold adjustment with safety controls
- False positive/negative tracking and analysis
- Production data-driven calibration
- Multi-system threshold coordination
- Automated rollback on performance degradation
- Statistical significance testing
"""

import asyncio
import json
import logging
import math
import random
import statistics
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import threading
import uuid

import structlog

from ..core.config import config
from ..core.logging_config import get_logger
from ..compliance.audit_framework import AuditTrail, AuditEventType, RiskLevel
from ..monitoring.dashboard import metrics_collector, increment_counter, set_gauge

logger = get_logger(__name__)


class ThresholdType(Enum):
    """Types of security thresholds."""
    BOT_DETECTION = "bot_detection"
    THREAT_INTELLIGENCE = "threat_intelligence"
    BOPLA_DETECTION = "bopla_detection"
    RATE_LIMITING = "rate_limiting"
    RISK_ASSESSMENT = "risk_assessment"
    ANOMALY_DETECTION = "anomaly_detection"


class ExperimentStatus(Enum):
    """A/B test experiment status."""
    PLANNING = "planning"
    RUNNING = "running"
    ANALYZING = "analyzing"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


class ThresholdDirection(Enum):
    """Direction of threshold adjustment."""
    INCREASE = "increase"  # More restrictive
    DECREASE = "decrease"  # Less restrictive
    MAINTAIN = "maintain"


@dataclass
class ThresholdConfig:
    """Configuration for a security threshold."""
    threshold_type: ThresholdType
    current_value: float
    min_value: float
    max_value: float
    step_size: float
    target_false_positive_rate: float
    target_false_negative_rate: float
    description: str
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class ExperimentGroup:
    """A/B test experiment group configuration."""
    group_id: str
    threshold_value: float
    traffic_percentage: float
    description: str


@dataclass
class ExperimentMetrics:
    """Metrics for threshold experiment evaluation."""
    true_positives: int = 0
    false_positives: int = 0
    true_negatives: int = 0
    false_negatives: int = 0
    total_requests: int = 0
    avg_response_time: float = 0.0
    error_rate: float = 0.0
    user_satisfaction_score: float = 0.0


@dataclass
class ThresholdExperiment:
    """A/B test experiment for threshold optimization."""
    experiment_id: str
    threshold_type: ThresholdType
    control_group: ExperimentGroup
    test_groups: List[ExperimentGroup]
    start_time: datetime
    planned_duration_hours: int
    status: ExperimentStatus
    metrics: Dict[str, ExperimentMetrics] = field(default_factory=dict)
    statistical_significance: float = 0.0
    winner_group_id: Optional[str] = None
    end_time: Optional[datetime] = None
    rollback_reason: Optional[str] = None


@dataclass
class CalibrationResult:
    """Result of threshold calibration analysis."""
    threshold_type: ThresholdType
    recommended_value: float
    current_value: float
    confidence: float
    expected_improvement: Dict[str, float]
    risk_assessment: str
    requires_experiment: bool


class ConservativeThresholdCalibrator:
    """
    Conservative threshold calibration system with A/B testing.
    
    Implements gradual threshold tuning to optimize security effectiveness
    while minimizing false positives through systematic experimentation.
    """
    
    def __init__(self, audit_trail: Optional[AuditTrail] = None):
        self.audit_trail = audit_trail
        self.threshold_configs: Dict[ThresholdType, ThresholdConfig] = {}
        self.active_experiments: Dict[str, ThresholdExperiment] = {}
        self.experiment_history: List[ThresholdExperiment] = []
        self.performance_history: Dict[ThresholdType, deque] = defaultdict(lambda: deque(maxlen=10000))
        self._lock = threading.RLock()
        
        # Calibration parameters
        self.min_experiment_duration_hours = 24
        self.max_experiment_duration_hours = 168  # 1 week
        self.min_statistical_significance = 0.95
        self.max_false_positive_rate = 0.05  # 5%
        self.min_true_positive_rate = 0.95   # 95%
        self.safety_margin = 0.1  # 10% safety margin for adjustments
        
        # A/B testing configuration
        self.control_group_percentage = 0.5
        self.test_group_percentage = 0.5
        self.min_sample_size = 1000
        
        # Initialize default threshold configurations
        self._initialize_threshold_configs()
        
        logger.info("ConservativeThresholdCalibrator initialized")
    
    def _initialize_threshold_configs(self):
        """Initialize default threshold configurations."""
        self.threshold_configs = {
            ThresholdType.BOT_DETECTION: ThresholdConfig(
                threshold_type=ThresholdType.BOT_DETECTION,
                current_value=0.7,
                min_value=0.5,
                max_value=0.95,
                step_size=0.05,
                target_false_positive_rate=0.02,
                target_false_negative_rate=0.05,
                description="AI bot detection probability threshold"
            ),
            ThresholdType.THREAT_INTELLIGENCE: ThresholdConfig(
                threshold_type=ThresholdType.THREAT_INTELLIGENCE,
                current_value=0.6,
                min_value=0.3,
                max_value=0.9,
                step_size=0.1,
                target_false_positive_rate=0.01,
                target_false_negative_rate=0.03,
                description="Threat intelligence confidence threshold"
            ),
            ThresholdType.BOPLA_DETECTION: ThresholdConfig(
                threshold_type=ThresholdType.BOPLA_DETECTION,
                current_value=0.7,
                min_value=0.5,
                max_value=0.9,
                step_size=0.05,
                target_false_positive_rate=0.03,
                target_false_negative_rate=0.05,
                description="BOPLA anomaly detection threshold"
            ),
            ThresholdType.RISK_ASSESSMENT: ThresholdConfig(
                threshold_type=ThresholdType.RISK_ASSESSMENT,
                current_value=0.65,
                min_value=0.4,
                max_value=0.85,
                step_size=0.05,
                target_false_positive_rate=0.05,
                target_false_negative_rate=0.02,
                description="ML risk assessment threshold"
            ),
            ThresholdType.ANOMALY_DETECTION: ThresholdConfig(
                threshold_type=ThresholdType.ANOMALY_DETECTION,
                current_value=0.8,
                min_value=0.6,
                max_value=0.95,
                step_size=0.05,
                target_false_positive_rate=0.02,
                target_false_negative_rate=0.08,
                description="General anomaly detection threshold"
            )
        }
    
    async def analyze_threshold_performance(self, threshold_type: ThresholdType,
                                          time_window_hours: int = 24) -> CalibrationResult:
        """Analyze threshold performance and recommend adjustments."""
        try:
            config = self.threshold_configs.get(threshold_type)
            if not config:
                raise ValueError(f"Unknown threshold type: {threshold_type}")
            
            # Collect performance data
            performance_data = await self._collect_performance_data(threshold_type, time_window_hours)
            
            # Calculate current metrics
            current_metrics = self._calculate_performance_metrics(performance_data)
            
            # Analyze performance trends
            trend_analysis = self._analyze_performance_trends(threshold_type, time_window_hours)
            
            # Determine optimal threshold
            optimal_threshold = await self._calculate_optimal_threshold(
                threshold_type, current_metrics, trend_analysis
            )
            
            # Assess risk of threshold change
            risk_assessment = self._assess_threshold_change_risk(
                config.current_value, optimal_threshold, current_metrics
            )
            
            # Calculate expected improvement
            expected_improvement = self._calculate_expected_improvement(
                current_metrics, optimal_threshold, config.current_value
            )
            
            # Determine if experiment is needed
            requires_experiment = abs(optimal_threshold - config.current_value) > config.step_size
            
            result = CalibrationResult(
                threshold_type=threshold_type,
                recommended_value=optimal_threshold,
                current_value=config.current_value,
                confidence=self._calculate_recommendation_confidence(current_metrics, trend_analysis),
                expected_improvement=expected_improvement,
                risk_assessment=risk_assessment,
                requires_experiment=requires_experiment
            )
            
            logger.info("Threshold performance analysis completed",
                       threshold_type=threshold_type.value,
                       current_value=config.current_value,
                       recommended_value=optimal_threshold,
                       requires_experiment=requires_experiment)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze threshold performance: {e}")
            raise
    
    async def _collect_performance_data(self, threshold_type: ThresholdType, 
                                      time_window_hours: int) -> Dict[str, Any]:
        """Collect performance data for threshold analysis."""
        try:
            # This would integrate with actual monitoring systems
            # For now, we'll simulate realistic performance data
            
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=time_window_hours)
            
            # Simulate performance data based on threshold type
            if threshold_type == ThresholdType.BOT_DETECTION:
                return {
                    "total_requests": random.randint(10000, 50000),
                    "bot_detections": random.randint(500, 2000),
                    "false_positives": random.randint(10, 100),
                    "false_negatives": random.randint(5, 50),
                    "avg_response_time": random.uniform(50, 200),
                    "user_complaints": random.randint(0, 10)
                }
            elif threshold_type == ThresholdType.THREAT_INTELLIGENCE:
                return {
                    "total_requests": random.randint(5000, 25000),
                    "threat_matches": random.randint(50, 200),
                    "false_positives": random.randint(2, 20),
                    "false_negatives": random.randint(1, 10),
                    "avg_response_time": random.uniform(30, 100),
                    "security_incidents": random.randint(0, 5)
                }
            else:
                # Generic performance data
                return {
                    "total_requests": random.randint(1000, 10000),
                    "detections": random.randint(50, 500),
                    "false_positives": random.randint(5, 50),
                    "false_negatives": random.randint(2, 25),
                    "avg_response_time": random.uniform(20, 150)
                }
                
        except Exception as e:
            logger.error(f"Failed to collect performance data: {e}")
            return {}
    
    def _calculate_performance_metrics(self, performance_data: Dict[str, Any]) -> ExperimentMetrics:
        """Calculate performance metrics from collected data."""
        try:
            total_requests = performance_data.get("total_requests", 0)
            detections = performance_data.get("detections", performance_data.get("bot_detections", performance_data.get("threat_matches", 0)))
            false_positives = performance_data.get("false_positives", 0)
            false_negatives = performance_data.get("false_negatives", 0)
            
            # Calculate true positives and negatives
            true_positives = max(0, detections - false_positives)
            true_negatives = max(0, total_requests - detections - false_negatives)
            
            return ExperimentMetrics(
                true_positives=true_positives,
                false_positives=false_positives,
                true_negatives=true_negatives,
                false_negatives=false_negatives,
                total_requests=total_requests,
                avg_response_time=performance_data.get("avg_response_time", 0.0),
                error_rate=performance_data.get("error_rate", 0.0),
                user_satisfaction_score=max(0.0, 1.0 - (performance_data.get("user_complaints", 0) / max(1, total_requests)))
            )
            
        except Exception as e:
            logger.error(f"Failed to calculate performance metrics: {e}")
            return ExperimentMetrics()

    def _analyze_performance_trends(self, threshold_type: ThresholdType,
                                  time_window_hours: int) -> Dict[str, Any]:
        """Analyze performance trends over time."""
        try:
            with self._lock:
                history = list(self.performance_history[threshold_type])

            if len(history) < 10:
                return {"trend": "insufficient_data", "stability": "unknown"}

            # Calculate trend metrics
            recent_data = history[-time_window_hours:]
            older_data = history[-time_window_hours*2:-time_window_hours] if len(history) >= time_window_hours*2 else []

            if not older_data:
                return {"trend": "insufficient_historical_data", "stability": "unknown"}

            # Calculate false positive rate trends
            recent_fp_rate = statistics.mean([d.get("false_positive_rate", 0) for d in recent_data])
            older_fp_rate = statistics.mean([d.get("false_positive_rate", 0) for d in older_data])

            # Calculate detection rate trends
            recent_detection_rate = statistics.mean([d.get("detection_rate", 0) for d in recent_data])
            older_detection_rate = statistics.mean([d.get("detection_rate", 0) for d in older_data])

            # Determine trend direction
            fp_trend = "increasing" if recent_fp_rate > older_fp_rate * 1.1 else "decreasing" if recent_fp_rate < older_fp_rate * 0.9 else "stable"
            detection_trend = "increasing" if recent_detection_rate > older_detection_rate * 1.1 else "decreasing" if recent_detection_rate < older_detection_rate * 0.9 else "stable"

            # Calculate stability
            fp_variance = statistics.variance([d.get("false_positive_rate", 0) for d in recent_data]) if len(recent_data) > 1 else 0
            stability = "stable" if fp_variance < 0.01 else "moderate" if fp_variance < 0.05 else "unstable"

            return {
                "false_positive_trend": fp_trend,
                "detection_trend": detection_trend,
                "stability": stability,
                "recent_fp_rate": recent_fp_rate,
                "recent_detection_rate": recent_detection_rate,
                "variance": fp_variance
            }

        except Exception as e:
            logger.error(f"Failed to analyze performance trends: {e}")
            return {"trend": "error", "stability": "unknown"}

    async def _calculate_optimal_threshold(self, threshold_type: ThresholdType,
                                         current_metrics: ExperimentMetrics,
                                         trend_analysis: Dict[str, Any]) -> float:
        """Calculate optimal threshold value based on performance data."""
        try:
            config = self.threshold_configs[threshold_type]
            current_threshold = config.current_value

            # Calculate current rates
            total_positives = current_metrics.true_positives + current_metrics.false_negatives
            total_negatives = current_metrics.true_negatives + current_metrics.false_positives

            if total_positives == 0 or total_negatives == 0:
                return current_threshold  # Insufficient data

            current_fp_rate = current_metrics.false_positives / total_negatives
            current_fn_rate = current_metrics.false_negatives / total_positives

            # Determine adjustment direction based on performance vs targets
            fp_over_target = current_fp_rate > config.target_false_positive_rate
            fn_over_target = current_fn_rate > config.target_false_negative_rate

            # Conservative adjustment logic
            if fp_over_target and not fn_over_target:
                # Too many false positives - increase threshold (more restrictive)
                adjustment = min(config.step_size, (current_fp_rate - config.target_false_positive_rate) * 2)
                new_threshold = min(config.max_value, current_threshold + adjustment)
            elif fn_over_target and not fp_over_target:
                # Too many false negatives - decrease threshold (less restrictive)
                adjustment = min(config.step_size, (current_fn_rate - config.target_false_negative_rate) * 2)
                new_threshold = max(config.min_value, current_threshold - adjustment)
            elif fp_over_target and fn_over_target:
                # Both rates are high - prioritize reducing false positives (conservative approach)
                adjustment = min(config.step_size * 0.5, (current_fp_rate - config.target_false_positive_rate))
                new_threshold = min(config.max_value, current_threshold + adjustment)
            else:
                # Both rates are acceptable - make small optimization based on trends
                if trend_analysis.get("false_positive_trend") == "increasing":
                    new_threshold = min(config.max_value, current_threshold + config.step_size * 0.5)
                elif trend_analysis.get("detection_trend") == "decreasing":
                    new_threshold = max(config.min_value, current_threshold - config.step_size * 0.5)
                else:
                    new_threshold = current_threshold  # No change needed

            # Apply safety margin
            if new_threshold != current_threshold:
                direction = 1 if new_threshold > current_threshold else -1
                safety_adjustment = config.step_size * self.safety_margin * direction
                new_threshold = current_threshold + (new_threshold - current_threshold) * (1 - self.safety_margin)

            return round(new_threshold, 3)

        except Exception as e:
            logger.error(f"Failed to calculate optimal threshold: {e}")
            return config.current_value

    def _assess_threshold_change_risk(self, current_value: float, new_value: float,
                                    current_metrics: ExperimentMetrics) -> str:
        """Assess risk of changing threshold value."""
        try:
            if abs(new_value - current_value) < 0.01:
                return "minimal"

            change_magnitude = abs(new_value - current_value) / current_value

            # Calculate current system performance
            total_requests = current_metrics.total_requests
            if total_requests == 0:
                return "high"  # No data to assess risk

            current_accuracy = (current_metrics.true_positives + current_metrics.true_negatives) / total_requests

            # Risk assessment based on change magnitude and current performance
            if change_magnitude > 0.2:  # >20% change
                return "high"
            elif change_magnitude > 0.1:  # >10% change
                return "medium" if current_accuracy > 0.9 else "high"
            elif change_magnitude > 0.05:  # >5% change
                return "low" if current_accuracy > 0.95 else "medium"
            else:
                return "minimal"

        except Exception as e:
            logger.error(f"Failed to assess threshold change risk: {e}")
            return "high"

    def _calculate_expected_improvement(self, current_metrics: ExperimentMetrics,
                                      new_threshold: float, current_threshold: float) -> Dict[str, float]:
        """Calculate expected improvement from threshold change."""
        try:
            if abs(new_threshold - current_threshold) < 0.01:
                return {"false_positive_reduction": 0.0, "detection_improvement": 0.0}

            # Estimate improvement based on threshold direction and magnitude
            threshold_change = new_threshold - current_threshold
            change_magnitude = abs(threshold_change) / current_threshold

            # Conservative estimates
            if threshold_change > 0:  # Increasing threshold (more restrictive)
                fp_reduction = min(0.5, change_magnitude * 2)  # Up to 50% FP reduction
                detection_loss = min(0.1, change_magnitude)    # Up to 10% detection loss
                return {
                    "false_positive_reduction": fp_reduction,
                    "detection_improvement": -detection_loss
                }
            else:  # Decreasing threshold (less restrictive)
                detection_gain = min(0.3, change_magnitude * 1.5)  # Up to 30% detection gain
                fp_increase = min(0.2, change_magnitude)           # Up to 20% FP increase
                return {
                    "false_positive_reduction": -fp_increase,
                    "detection_improvement": detection_gain
                }

        except Exception as e:
            logger.error(f"Failed to calculate expected improvement: {e}")
            return {"false_positive_reduction": 0.0, "detection_improvement": 0.0}

    def _calculate_recommendation_confidence(self, current_metrics: ExperimentMetrics,
                                           trend_analysis: Dict[str, Any]) -> float:
        """Calculate confidence in threshold recommendation."""
        try:
            confidence_factors = []

            # Data volume factor
            if current_metrics.total_requests > 10000:
                confidence_factors.append(0.9)
            elif current_metrics.total_requests > 1000:
                confidence_factors.append(0.7)
            else:
                confidence_factors.append(0.3)

            # Stability factor
            stability = trend_analysis.get("stability", "unknown")
            if stability == "stable":
                confidence_factors.append(0.9)
            elif stability == "moderate":
                confidence_factors.append(0.6)
            else:
                confidence_factors.append(0.3)

            # Performance clarity factor
            total_events = current_metrics.true_positives + current_metrics.false_positives
            if total_events > 100:
                confidence_factors.append(0.8)
            elif total_events > 50:
                confidence_factors.append(0.6)
            else:
                confidence_factors.append(0.4)

            # Calculate weighted average
            return sum(confidence_factors) / len(confidence_factors)

        except Exception as e:
            logger.error(f"Failed to calculate recommendation confidence: {e}")
            return 0.5

    async def create_threshold_experiment(self, threshold_type: ThresholdType,
                                        test_threshold: float,
                                        duration_hours: int = 48) -> str:
        """Create A/B test experiment for threshold optimization."""
        try:
            config = self.threshold_configs.get(threshold_type)
            if not config:
                raise ValueError(f"Unknown threshold type: {threshold_type}")

            # Validate test threshold
            if not (config.min_value <= test_threshold <= config.max_value):
                raise ValueError(f"Test threshold {test_threshold} outside valid range [{config.min_value}, {config.max_value}]")

            # Check if there's already an active experiment
            active_experiments = [exp for exp in self.active_experiments.values()
                                if exp.threshold_type == threshold_type and exp.status == ExperimentStatus.RUNNING]

            if active_experiments:
                raise ValueError(f"Active experiment already running for {threshold_type.value}")

            # Create experiment
            experiment_id = str(uuid.uuid4())

            control_group = ExperimentGroup(
                group_id="control",
                threshold_value=config.current_value,
                traffic_percentage=self.control_group_percentage,
                description=f"Control group with current threshold {config.current_value}"
            )

            test_group = ExperimentGroup(
                group_id="test",
                threshold_value=test_threshold,
                traffic_percentage=self.test_group_percentage,
                description=f"Test group with new threshold {test_threshold}"
            )

            experiment = ThresholdExperiment(
                experiment_id=experiment_id,
                threshold_type=threshold_type,
                control_group=control_group,
                test_groups=[test_group],
                start_time=datetime.now(timezone.utc),
                planned_duration_hours=duration_hours,
                status=ExperimentStatus.RUNNING,
                metrics={
                    "control": ExperimentMetrics(),
                    "test": ExperimentMetrics()
                }
            )

            # Store experiment
            with self._lock:
                self.active_experiments[experiment_id] = experiment

            # Log experiment creation
            logger.info("Threshold experiment created",
                       experiment_id=experiment_id,
                       threshold_type=threshold_type.value,
                       control_threshold=config.current_value,
                       test_threshold=test_threshold,
                       duration_hours=duration_hours)

            if self.audit_trail:
                self.audit_trail.log_event(
                    AuditEventType.SYSTEM_ACCESS,
                    None,
                    "threshold_calibrator",
                    "experiment_created",
                    {
                        "experiment_id": experiment_id,
                        "threshold_type": threshold_type.value,
                        "control_threshold": config.current_value,
                        "test_threshold": test_threshold
                    },
                    risk_level=RiskLevel.MEDIUM
                )

            increment_counter("threshold_experiments_created", 1, {"type": threshold_type.value})

            return experiment_id

        except Exception as e:
            logger.error(f"Failed to create threshold experiment: {e}")
            raise

    async def record_experiment_result(self, experiment_id: str, group_id: str,
                                     is_true_positive: bool, is_false_positive: bool,
                                     response_time_ms: float = 0.0):
        """Record result for A/B test experiment."""
        try:
            with self._lock:
                experiment = self.active_experiments.get(experiment_id)
                if not experiment or experiment.status != ExperimentStatus.RUNNING:
                    return  # Experiment not active

                metrics = experiment.metrics.get(group_id)
                if not metrics:
                    return  # Invalid group

                # Update metrics
                metrics.total_requests += 1

                if is_true_positive:
                    metrics.true_positives += 1
                elif is_false_positive:
                    metrics.false_positives += 1
                else:
                    metrics.true_negatives += 1

                # Update response time (moving average)
                if metrics.total_requests == 1:
                    metrics.avg_response_time = response_time_ms
                else:
                    metrics.avg_response_time = (metrics.avg_response_time * 0.9 + response_time_ms * 0.1)

        except Exception as e:
            logger.error(f"Failed to record experiment result: {e}")

    async def analyze_experiment(self, experiment_id: str) -> Dict[str, Any]:
        """Analyze A/B test experiment results."""
        try:
            with self._lock:
                experiment = self.active_experiments.get(experiment_id)
                if not experiment:
                    experiment = next((exp for exp in self.experiment_history if exp.experiment_id == experiment_id), None)
                    if not experiment:
                        raise ValueError(f"Experiment {experiment_id} not found")

            # Check if experiment has sufficient data
            control_metrics = experiment.metrics.get("control", ExperimentMetrics())
            test_metrics = experiment.metrics.get("test", ExperimentMetrics())

            if control_metrics.total_requests < self.min_sample_size or test_metrics.total_requests < self.min_sample_size:
                return {
                    "status": "insufficient_data",
                    "control_samples": control_metrics.total_requests,
                    "test_samples": test_metrics.total_requests,
                    "min_required": self.min_sample_size
                }

            # Calculate performance metrics
            control_performance = self._calculate_group_performance(control_metrics)
            test_performance = self._calculate_group_performance(test_metrics)

            # Calculate statistical significance
            significance = self._calculate_statistical_significance(control_metrics, test_metrics)

            # Determine winner
            winner = self._determine_experiment_winner(control_performance, test_performance, significance)

            # Calculate improvement metrics
            improvement = self._calculate_improvement_metrics(control_performance, test_performance)

            return {
                "experiment_id": experiment_id,
                "status": "completed" if significance >= self.min_statistical_significance else "running",
                "statistical_significance": significance,
                "control_performance": control_performance,
                "test_performance": test_performance,
                "winner": winner,
                "improvement": improvement,
                "recommendation": self._generate_experiment_recommendation(winner, improvement, significance)
            }

        except Exception as e:
            logger.error(f"Failed to analyze experiment: {e}")
            return {"status": "error", "error": str(e)}

    def _calculate_group_performance(self, metrics: ExperimentMetrics) -> Dict[str, float]:
        """Calculate performance metrics for an experiment group."""
        try:
            if metrics.total_requests == 0:
                return {"accuracy": 0.0, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "false_positive_rate": 0.0}

            total_positives = metrics.true_positives + metrics.false_negatives
            total_negatives = metrics.true_negatives + metrics.false_positives

            # Calculate metrics
            accuracy = (metrics.true_positives + metrics.true_negatives) / metrics.total_requests
            precision = metrics.true_positives / (metrics.true_positives + metrics.false_positives) if (metrics.true_positives + metrics.false_positives) > 0 else 0
            recall = metrics.true_positives / total_positives if total_positives > 0 else 0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            false_positive_rate = metrics.false_positives / total_negatives if total_negatives > 0 else 0

            return {
                "accuracy": accuracy,
                "precision": precision,
                "recall": recall,
                "f1_score": f1_score,
                "false_positive_rate": false_positive_rate,
                "avg_response_time": metrics.avg_response_time
            }

        except Exception as e:
            logger.error(f"Failed to calculate group performance: {e}")
            return {"accuracy": 0.0, "precision": 0.0, "recall": 0.0, "f1_score": 0.0, "false_positive_rate": 0.0}

    def _calculate_statistical_significance(self, control_metrics: ExperimentMetrics,
                                          test_metrics: ExperimentMetrics) -> float:
        """Calculate statistical significance using simplified z-test."""
        try:
            # Calculate success rates (using accuracy as success metric)
            control_success_rate = (control_metrics.true_positives + control_metrics.true_negatives) / control_metrics.total_requests if control_metrics.total_requests > 0 else 0
            test_success_rate = (test_metrics.true_positives + test_metrics.true_negatives) / test_metrics.total_requests if test_metrics.total_requests > 0 else 0

            # Calculate pooled standard error
            pooled_rate = ((control_metrics.true_positives + control_metrics.true_negatives) +
                          (test_metrics.true_positives + test_metrics.true_negatives)) / (control_metrics.total_requests + test_metrics.total_requests)

            pooled_se = math.sqrt(pooled_rate * (1 - pooled_rate) * (1/control_metrics.total_requests + 1/test_metrics.total_requests))

            if pooled_se == 0:
                return 0.0

            # Calculate z-score
            z_score = abs(test_success_rate - control_success_rate) / pooled_se

            # Convert to confidence level (simplified)
            # This is a simplified calculation - in production, use proper statistical libraries
            if z_score > 2.58:  # 99% confidence
                return 0.99
            elif z_score > 1.96:  # 95% confidence
                return 0.95
            elif z_score > 1.64:  # 90% confidence
                return 0.90
            else:
                return min(0.89, z_score / 1.64 * 0.90)

        except Exception as e:
            logger.error(f"Failed to calculate statistical significance: {e}")
            return 0.0

    def _determine_experiment_winner(self, control_performance: Dict[str, float],
                                   test_performance: Dict[str, float],
                                   significance: float) -> str:
        """Determine winner of A/B test experiment."""
        try:
            if significance < self.min_statistical_significance:
                return "inconclusive"

            # Conservative approach: prioritize false positive reduction
            control_fp_rate = control_performance.get("false_positive_rate", 1.0)
            test_fp_rate = test_performance.get("false_positive_rate", 1.0)

            control_accuracy = control_performance.get("accuracy", 0.0)
            test_accuracy = test_performance.get("accuracy", 0.0)

            # Test wins if it significantly reduces false positives without major accuracy loss
            if test_fp_rate < control_fp_rate * 0.9 and test_accuracy >= control_accuracy * 0.95:
                return "test"
            # Control wins if test increases false positives significantly
            elif test_fp_rate > control_fp_rate * 1.1:
                return "control"
            # Test wins if it significantly improves accuracy without increasing false positives
            elif test_accuracy > control_accuracy * 1.05 and test_fp_rate <= control_fp_rate * 1.05:
                return "test"
            else:
                return "control"  # Conservative default

        except Exception as e:
            logger.error(f"Failed to determine experiment winner: {e}")
            return "control"

    def _calculate_improvement_metrics(self, control_performance: Dict[str, float],
                                     test_performance: Dict[str, float]) -> Dict[str, float]:
        """Calculate improvement metrics between control and test groups."""
        try:
            improvements = {}

            for metric in ["accuracy", "precision", "recall", "f1_score", "false_positive_rate", "avg_response_time"]:
                control_value = control_performance.get(metric, 0.0)
                test_value = test_performance.get(metric, 0.0)

                if control_value > 0:
                    if metric == "false_positive_rate" or metric == "avg_response_time":
                        # Lower is better for these metrics
                        improvement = (control_value - test_value) / control_value
                    else:
                        # Higher is better for other metrics
                        improvement = (test_value - control_value) / control_value
                else:
                    improvement = 0.0

                improvements[f"{metric}_improvement"] = improvement

            return improvements

        except Exception as e:
            logger.error(f"Failed to calculate improvement metrics: {e}")
            return {}

    def _generate_experiment_recommendation(self, winner: str, improvement: Dict[str, float],
                                          significance: float) -> str:
        """Generate recommendation based on experiment results."""
        try:
            if significance < self.min_statistical_significance:
                return "Continue experiment - insufficient statistical significance"

            if winner == "test":
                fp_improvement = improvement.get("false_positive_rate_improvement", 0.0)
                accuracy_improvement = improvement.get("accuracy_improvement", 0.0)

                if fp_improvement > 0.1:  # >10% FP reduction
                    return "RECOMMEND: Deploy test threshold - significant false positive reduction"
                elif accuracy_improvement > 0.05:  # >5% accuracy improvement
                    return "RECOMMEND: Deploy test threshold - significant accuracy improvement"
                else:
                    return "RECOMMEND: Deploy test threshold - modest improvement with statistical significance"
            elif winner == "control":
                return "RECOMMEND: Keep current threshold - control group performed better"
            else:
                return "RECOMMEND: Keep current threshold - results inconclusive"

        except Exception as e:
            logger.error(f"Failed to generate experiment recommendation: {e}")
            return "RECOMMEND: Keep current threshold - analysis error"

    async def apply_experiment_results(self, experiment_id: str) -> bool:
        """Apply results of successful A/B test experiment."""
        try:
            analysis = await self.analyze_experiment(experiment_id)

            if analysis.get("status") != "completed":
                logger.warning(f"Cannot apply results - experiment {experiment_id} not completed")
                return False

            if analysis.get("winner") != "test":
                logger.info(f"Not applying results - control group won experiment {experiment_id}")
                return False

            with self._lock:
                experiment = self.active_experiments.get(experiment_id)
                if not experiment:
                    logger.error(f"Experiment {experiment_id} not found")
                    return False

                # Update threshold configuration
                threshold_type = experiment.threshold_type
                new_threshold = experiment.test_groups[0].threshold_value

                config = self.threshold_configs[threshold_type]
                old_threshold = config.current_value
                config.current_value = new_threshold
                config.last_updated = datetime.now(timezone.utc)

                # Mark experiment as completed
                experiment.status = ExperimentStatus.COMPLETED
                experiment.end_time = datetime.now(timezone.utc)
                experiment.winner_group_id = "test"

                # Move to history
                self.experiment_history.append(experiment)
                del self.active_experiments[experiment_id]

            logger.info("Threshold updated from experiment results",
                       experiment_id=experiment_id,
                       threshold_type=threshold_type.value,
                       old_threshold=old_threshold,
                       new_threshold=new_threshold)

            if self.audit_trail:
                self.audit_trail.log_event(
                    AuditEventType.SYSTEM_ACCESS,
                    None,
                    "threshold_calibrator",
                    "threshold_updated",
                    {
                        "experiment_id": experiment_id,
                        "threshold_type": threshold_type.value,
                        "old_threshold": old_threshold,
                        "new_threshold": new_threshold
                    },
                    risk_level=RiskLevel.MEDIUM
                )

            increment_counter("threshold_updates_applied", 1, {"type": threshold_type.value})
            set_gauge(f"threshold_{threshold_type.value}", new_threshold)

            return True

        except Exception as e:
            logger.error(f"Failed to apply experiment results: {e}")
            return False

    def get_threshold_value(self, threshold_type: ThresholdType, request_id: Optional[str] = None) -> float:
        """Get threshold value for request (considering A/B tests)."""
        try:
            # Check if request is part of an active experiment
            if request_id:
                for experiment in self.active_experiments.values():
                    if (experiment.threshold_type == threshold_type and
                        experiment.status == ExperimentStatus.RUNNING):

                        # Simple hash-based assignment to groups
                        hash_value = hash(request_id) % 100
                        if hash_value < experiment.control_group.traffic_percentage * 100:
                            return experiment.control_group.threshold_value
                        else:
                            return experiment.test_groups[0].threshold_value

            # Return current threshold if no experiment
            config = self.threshold_configs.get(threshold_type)
            return config.current_value if config else 0.5

        except Exception as e:
            logger.error(f"Failed to get threshold value: {e}")
            return 0.5

    def generate_calibration_report(self) -> Dict[str, Any]:
        """Generate comprehensive threshold calibration report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Threshold status
                threshold_status = {}
                for threshold_type, config in self.threshold_configs.items():
                    threshold_status[threshold_type.value] = {
                        "current_value": config.current_value,
                        "target_fp_rate": config.target_false_positive_rate,
                        "target_fn_rate": config.target_false_negative_rate,
                        "last_updated": config.last_updated.isoformat(),
                        "valid_range": [config.min_value, config.max_value]
                    }

                # Active experiments
                active_experiments = {}
                for exp_id, experiment in self.active_experiments.items():
                    active_experiments[exp_id] = {
                        "threshold_type": experiment.threshold_type.value,
                        "status": experiment.status.value,
                        "start_time": experiment.start_time.isoformat(),
                        "control_threshold": experiment.control_group.threshold_value,
                        "test_threshold": experiment.test_groups[0].threshold_value,
                        "control_samples": experiment.metrics.get("control", ExperimentMetrics()).total_requests,
                        "test_samples": experiment.metrics.get("test", ExperimentMetrics()).total_requests
                    }

                # Recent experiment history
                recent_experiments = []
                for experiment in self.experiment_history[-10:]:  # Last 10 experiments
                    recent_experiments.append({
                        "experiment_id": experiment.experiment_id,
                        "threshold_type": experiment.threshold_type.value,
                        "status": experiment.status.value,
                        "winner": experiment.winner_group_id,
                        "start_time": experiment.start_time.isoformat(),
                        "end_time": experiment.end_time.isoformat() if experiment.end_time else None
                    })

                return {
                    "report_generated_at": current_time.isoformat(),
                    "summary": {
                        "total_thresholds": len(self.threshold_configs),
                        "active_experiments": len(self.active_experiments),
                        "completed_experiments": len(self.experiment_history),
                        "last_threshold_update": max([config.last_updated for config in self.threshold_configs.values()]).isoformat() if self.threshold_configs else None
                    },
                    "threshold_status": threshold_status,
                    "active_experiments": active_experiments,
                    "recent_experiments": recent_experiments,
                    "recommendations": self._generate_calibration_recommendations()
                }

        except Exception as e:
            logger.error(f"Failed to generate calibration report: {e}")
            return {"error": str(e)}

    def _generate_calibration_recommendations(self) -> List[str]:
        """Generate calibration recommendations."""
        recommendations = []

        try:
            current_time = datetime.now(timezone.utc)

            # Check for outdated thresholds
            for threshold_type, config in self.threshold_configs.items():
                days_since_update = (current_time - config.last_updated).days
                if days_since_update > 30:
                    recommendations.append(f"Consider reviewing {threshold_type.value} threshold - not updated in {days_since_update} days")

            # Check for long-running experiments
            for experiment in self.active_experiments.values():
                hours_running = (current_time - experiment.start_time).total_seconds() / 3600
                if hours_running > experiment.planned_duration_hours * 1.5:
                    recommendations.append(f"Experiment {experiment.experiment_id} running longer than planned - consider analysis")

            # Check for insufficient experiment data
            for experiment in self.active_experiments.values():
                control_samples = experiment.metrics.get("control", ExperimentMetrics()).total_requests
                test_samples = experiment.metrics.get("test", ExperimentMetrics()).total_requests
                if control_samples < self.min_sample_size or test_samples < self.min_sample_size:
                    recommendations.append(f"Experiment {experiment.experiment_id} needs more data for statistical significance")

            if not recommendations:
                recommendations.append("Threshold calibration system is operating normally")

        except Exception as e:
            logger.error(f"Failed to generate calibration recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations


# Global instance
threshold_calibrator = ConservativeThresholdCalibrator()
