"""
Advanced API Inventory Management System

This module implements automated API discovery and classification to identify APIs
handling sensitive data, addressing the 47% industry gap in API inventory management.

Features:
- Automated API endpoint discovery through code analysis
- Real-time API traffic monitoring and classification
- Sensitive data detection and categorization
- Security risk assessment for each API
- Compliance mapping (GDPR, PCI-DSS, SOX)
- API dependency mapping and impact analysis
- Automated security policy enforcement
"""

import asyncio
import ast
import inspect
import json
import logging
import re
import time
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import threading
from collections import defaultdict

import aiohttp
from fastapi import Request, Response
import structlog

from ..core.config import config
from ..core.logging_config import get_logger
from ..compliance.audit_framework import AuditTrail, AuditEventType, RiskLevel
from ..monitoring.dashboard import metrics_collector, increment_counter, set_gauge

logger = get_logger(__name__)


class APIType(Enum):
    """API endpoint types."""
    EXTERNAL = "external"
    INTERNAL = "internal"
    WEBHOOK = "webhook"
    GRAPHQL = "graphql"
    REST = "rest"
    WEBSOCKET = "websocket"


class DataSensitivity(Enum):
    """Data sensitivity levels."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"
    TOP_SECRET = "top_secret"


class ComplianceFramework(Enum):
    """Compliance frameworks."""
    GDPR = "gdpr"
    PCI_DSS = "pci_dss"
    SOX = "sox"
    HIPAA = "hipaa"
    SOC2 = "soc2"
    ISO27001 = "iso27001"


class SecurityRisk(Enum):
    """Security risk levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SensitiveDataPattern:
    """Pattern for detecting sensitive data."""
    name: str
    pattern: str
    sensitivity: DataSensitivity
    compliance_frameworks: List[ComplianceFramework]
    description: str


@dataclass
class APIEndpoint:
    """API endpoint information."""
    id: str
    url: str
    method: str
    api_type: APIType
    source_file: Optional[str] = None
    function_name: Optional[str] = None
    parameters: List[str] = field(default_factory=list)
    response_fields: List[str] = field(default_factory=list)
    authentication_required: bool = False
    rate_limited: bool = False
    data_sensitivity: DataSensitivity = DataSensitivity.PUBLIC
    security_risk: SecurityRisk = SecurityRisk.LOW
    compliance_frameworks: List[ComplianceFramework] = field(default_factory=list)
    sensitive_data_types: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    last_accessed: Optional[datetime] = None
    access_count: int = 0
    error_count: int = 0
    avg_response_time: float = 0.0
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class APITrafficMetrics:
    """API traffic metrics."""
    endpoint_id: str
    timestamp: datetime
    method: str
    status_code: int
    response_time_ms: float
    request_size_bytes: int
    response_size_bytes: int
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    sensitive_data_detected: List[str] = field(default_factory=list)


class APIInventoryManager:
    """
    Advanced API inventory management system.
    
    Provides automated discovery, classification, and monitoring of APIs
    handling sensitive data with comprehensive security and compliance tracking.
    """
    
    def __init__(self, audit_trail: Optional[AuditTrail] = None):
        self.audit_trail = audit_trail
        self.endpoints: Dict[str, APIEndpoint] = {}
        self.traffic_metrics: List[APITrafficMetrics] = []
        self.sensitive_patterns = self._initialize_sensitive_patterns()
        self._lock = threading.RLock()
        self._discovery_cache: Dict[str, datetime] = {}
        self._last_scan_time: Optional[datetime] = None
        
        # Traffic monitoring
        self._traffic_buffer: List[APITrafficMetrics] = []
        self._buffer_lock = threading.Lock()
        self._max_buffer_size = 10000
        
        logger.info("APIInventoryManager initialized")
    
    def _initialize_sensitive_patterns(self) -> List[SensitiveDataPattern]:
        """Initialize patterns for detecting sensitive data."""
        return [
            # Financial data
            SensitiveDataPattern(
                name="crypto_private_key",
                pattern=r"(?i)(private[_\s]*key|priv[_\s]*key|secret[_\s]*key).*[0-9a-fA-F]{64}",
                sensitivity=DataSensitivity.TOP_SECRET,
                compliance_frameworks=[ComplianceFramework.SOX, ComplianceFramework.ISO27001],
                description="Cryptocurrency private keys"
            ),
            SensitiveDataPattern(
                name="api_key",
                pattern=r"(?i)(api[_\s]*key|access[_\s]*token|bearer[_\s]*token).*[0-9a-zA-Z]{20,}",
                sensitivity=DataSensitivity.CONFIDENTIAL,
                compliance_frameworks=[ComplianceFramework.SOC2, ComplianceFramework.ISO27001],
                description="API keys and access tokens"
            ),
            SensitiveDataPattern(
                name="wallet_address",
                pattern=r"0x[a-fA-F0-9]{40}",
                sensitivity=DataSensitivity.INTERNAL,
                compliance_frameworks=[ComplianceFramework.GDPR],
                description="Ethereum wallet addresses"
            ),
            SensitiveDataPattern(
                name="email_address",
                pattern=r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
                sensitivity=DataSensitivity.CONFIDENTIAL,
                compliance_frameworks=[ComplianceFramework.GDPR, ComplianceFramework.SOC2],
                description="Email addresses"
            ),
            SensitiveDataPattern(
                name="ip_address",
                pattern=r"\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b",
                sensitivity=DataSensitivity.INTERNAL,
                compliance_frameworks=[ComplianceFramework.GDPR],
                description="IP addresses"
            ),
            SensitiveDataPattern(
                name="financial_amount",
                pattern=r"(?i)(balance|amount|price|value|usd|eth|btc).*[\$]?[0-9,]+\.?[0-9]*",
                sensitivity=DataSensitivity.CONFIDENTIAL,
                compliance_frameworks=[ComplianceFramework.SOX, ComplianceFramework.PCI_DSS],
                description="Financial amounts and balances"
            ),
            # Personal data
            SensitiveDataPattern(
                name="user_id",
                pattern=r"(?i)(user[_\s]*id|customer[_\s]*id|account[_\s]*id).*[0-9a-zA-Z-]{8,}",
                sensitivity=DataSensitivity.CONFIDENTIAL,
                compliance_frameworks=[ComplianceFramework.GDPR, ComplianceFramework.SOC2],
                description="User and customer identifiers"
            ),
            SensitiveDataPattern(
                name="session_token",
                pattern=r"(?i)(session[_\s]*token|session[_\s]*id|csrf[_\s]*token).*[0-9a-zA-Z]{16,}",
                sensitivity=DataSensitivity.RESTRICTED,
                compliance_frameworks=[ComplianceFramework.SOC2, ComplianceFramework.ISO27001],
                description="Session tokens and CSRF tokens"
            ),
        ]

    async def discover_apis(self, scan_paths: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Discover APIs through automated code analysis.

        Args:
            scan_paths: Optional list of paths to scan. Defaults to src/ directory.

        Returns:
            Discovery results with statistics and found endpoints.
        """
        with self._lock:
            start_time = time.time()

            if scan_paths is None:
                scan_paths = ["src/"]

            discovered_endpoints = []
            scan_stats = {
                "files_scanned": 0,
                "endpoints_found": 0,
                "external_apis": 0,
                "internal_apis": 0,
                "sensitive_endpoints": 0,
                "high_risk_endpoints": 0
            }

            try:
                for scan_path in scan_paths:
                    path_obj = Path(scan_path)
                    if not path_obj.exists():
                        logger.warning(f"Scan path does not exist: {scan_path}")
                        continue

                    # Scan Python files for API endpoints
                    for py_file in path_obj.rglob("*.py"):
                        try:
                            endpoints = await self._analyze_python_file(py_file)
                            discovered_endpoints.extend(endpoints)
                            scan_stats["files_scanned"] += 1

                            if endpoints:
                                logger.debug(f"Found {len(endpoints)} endpoints in {py_file}")

                        except Exception as e:
                            logger.error(f"Failed to analyze file {py_file}: {e}")

                # Process discovered endpoints
                for endpoint in discovered_endpoints:
                    await self._process_discovered_endpoint(endpoint)
                    scan_stats["endpoints_found"] += 1

                    if endpoint.api_type == APIType.EXTERNAL:
                        scan_stats["external_apis"] += 1
                    else:
                        scan_stats["internal_apis"] += 1

                    if endpoint.data_sensitivity in [DataSensitivity.CONFIDENTIAL,
                                                   DataSensitivity.RESTRICTED,
                                                   DataSensitivity.TOP_SECRET]:
                        scan_stats["sensitive_endpoints"] += 1

                    if endpoint.security_risk in [SecurityRisk.HIGH, SecurityRisk.CRITICAL]:
                        scan_stats["high_risk_endpoints"] += 1

                # Update scan timestamp
                self._last_scan_time = datetime.now(timezone.utc)

                # Log audit event
                if self.audit_trail:
                    self.audit_trail.log_event(
                        AuditEventType.SYSTEM_ACCESS,
                        None,
                        "api_inventory",
                        "discover_apis",
                        {
                            "scan_stats": scan_stats,
                            "scan_duration_seconds": time.time() - start_time
                        },
                        risk_level=RiskLevel.LOW
                    )

                # Update metrics
                set_gauge("api_inventory_total_endpoints", len(self.endpoints))
                set_gauge("api_inventory_sensitive_endpoints", scan_stats["sensitive_endpoints"])
                set_gauge("api_inventory_high_risk_endpoints", scan_stats["high_risk_endpoints"])
                increment_counter("api_inventory_scans_completed", 1)

                logger.info("API discovery completed",
                           scan_stats=scan_stats,
                           duration_seconds=time.time() - start_time)

                return {
                    "success": True,
                    "scan_stats": scan_stats,
                    "duration_seconds": time.time() - start_time,
                    "endpoints_discovered": len(discovered_endpoints),
                    "total_endpoints": len(self.endpoints)
                }

            except Exception as e:
                logger.error(f"API discovery failed: {e}")
                increment_counter("api_inventory_scan_errors", 1)
                return {
                    "success": False,
                    "error": str(e),
                    "scan_stats": scan_stats
                }

    async def _analyze_python_file(self, file_path: Path) -> List[APIEndpoint]:
        """Analyze a Python file for API endpoints."""
        endpoints = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse AST
            tree = ast.parse(content)

            # Look for FastAPI route decorators
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    endpoint = await self._extract_fastapi_endpoint(node, file_path, content)
                    if endpoint:
                        endpoints.append(endpoint)

            # Look for external API calls
            external_endpoints = await self._extract_external_api_calls(content, file_path)
            endpoints.extend(external_endpoints)

        except Exception as e:
            logger.error(f"Failed to analyze Python file {file_path}: {e}")

        return endpoints

    async def _extract_fastapi_endpoint(self, node: ast.FunctionDef, file_path: Path, content: str) -> Optional[APIEndpoint]:
        """Extract FastAPI endpoint information from AST node."""
        try:
            # Check for FastAPI decorators
            fastapi_methods = ['get', 'post', 'put', 'delete', 'patch', 'options', 'head']

            for decorator in node.decorator_list:
                if isinstance(decorator, ast.Call) and isinstance(decorator.func, ast.Attribute):
                    if decorator.func.attr in fastapi_methods:
                        # Extract route path
                        route_path = "/"
                        if decorator.args and isinstance(decorator.args[0], ast.Constant):
                            route_path = decorator.args[0].value

                        # Create endpoint
                        endpoint_id = f"internal_{file_path.stem}_{node.name}"
                        endpoint = APIEndpoint(
                            id=endpoint_id,
                            url=route_path,
                            method=decorator.func.attr.upper(),
                            api_type=APIType.INTERNAL,
                            source_file=str(file_path),
                            function_name=node.name,
                            parameters=self._extract_function_parameters(node),
                            authentication_required=self._check_authentication_required(node, content),
                            rate_limited=self._check_rate_limited(node, content)
                        )

                        # Analyze for sensitive data
                        await self._analyze_endpoint_sensitivity(endpoint, content)

                        return endpoint

        except Exception as e:
            logger.error(f"Failed to extract FastAPI endpoint from {file_path}: {e}")

        return None

    async def _extract_external_api_calls(self, content: str, file_path: Path) -> List[APIEndpoint]:
        """Extract external API calls from file content."""
        endpoints = []

        try:
            # Common external API patterns
            api_patterns = [
                (r'https://api\.coingecko\.com/api/v3/([^\'"]+)', 'coingecko'),
                (r'https://api\.dexscreener\.com/latest/([^\'"]+)', 'dexscreener'),
                (r'https://api\.etherscan\.io/api\?([^\'"]+)', 'etherscan'),
                (r'https://public-api\.birdeye\.so/([^\'"]+)', 'birdeye'),
                (r'https://api\.llama\.fi/([^\'"]+)', 'defillama'),
                (r'https://oauth\.reddit\.com/([^\'"]+)', 'reddit'),
                (r'https://api\.twitter\.com/2/([^\'"]+)', 'twitter'),
            ]

            for pattern, service_name in api_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    endpoint_path = match.group(1) if match.groups() else match.group(0)

                    endpoint_id = f"external_{service_name}_{hash(endpoint_path) % 10000}"
                    endpoint = APIEndpoint(
                        id=endpoint_id,
                        url=match.group(0),
                        method="GET",  # Default, could be enhanced
                        api_type=APIType.EXTERNAL,
                        source_file=str(file_path),
                        dependencies=[service_name]
                    )

                    # Classify external API sensitivity
                    await self._classify_external_api_sensitivity(endpoint, service_name)
                    endpoints.append(endpoint)

        except Exception as e:
            logger.error(f"Failed to extract external API calls from {file_path}: {e}")

        return endpoints

    async def _classify_external_api_sensitivity(self, endpoint: APIEndpoint, service_name: str):
        """Classify external API sensitivity based on service type."""
        # High-sensitivity external APIs
        high_sensitivity_apis = {
            'etherscan': (DataSensitivity.CONFIDENTIAL, [ComplianceFramework.GDPR]),
            'reddit': (DataSensitivity.CONFIDENTIAL, [ComplianceFramework.GDPR, ComplianceFramework.SOC2]),
            'twitter': (DataSensitivity.CONFIDENTIAL, [ComplianceFramework.GDPR, ComplianceFramework.SOC2]),
        }

        # Medium-sensitivity external APIs
        medium_sensitivity_apis = {
            'coingecko': (DataSensitivity.INTERNAL, [ComplianceFramework.SOC2]),
            'dexscreener': (DataSensitivity.INTERNAL, [ComplianceFramework.SOC2]),
            'birdeye': (DataSensitivity.INTERNAL, [ComplianceFramework.SOC2]),
            'defillama': (DataSensitivity.INTERNAL, [ComplianceFramework.SOC2]),
        }

        if service_name in high_sensitivity_apis:
            sensitivity, frameworks = high_sensitivity_apis[service_name]
            endpoint.data_sensitivity = sensitivity
            endpoint.compliance_frameworks = frameworks
            endpoint.security_risk = SecurityRisk.HIGH
        elif service_name in medium_sensitivity_apis:
            sensitivity, frameworks = medium_sensitivity_apis[service_name]
            endpoint.data_sensitivity = sensitivity
            endpoint.compliance_frameworks = frameworks
            endpoint.security_risk = SecurityRisk.MEDIUM
        else:
            endpoint.data_sensitivity = DataSensitivity.PUBLIC
            endpoint.security_risk = SecurityRisk.LOW

    def _extract_function_parameters(self, node: ast.FunctionDef) -> List[str]:
        """Extract function parameter names."""
        parameters = []
        for arg in node.args.args:
            if arg.arg != 'self':  # Skip self parameter
                parameters.append(arg.arg)
        return parameters

    def _check_authentication_required(self, node: ast.FunctionDef, content: str) -> bool:
        """Check if endpoint requires authentication."""
        # Look for common authentication patterns
        auth_patterns = [
            r'@.*auth',
            r'Depends\(.*auth',
            r'require.*auth',
            r'token.*required',
            r'api.*key.*required'
        ]

        # Check function decorators and content around function
        func_start = getattr(node, 'lineno', 0)
        func_lines = content.split('\n')[max(0, func_start-5):func_start+10]
        func_context = '\n'.join(func_lines)

        for pattern in auth_patterns:
            if re.search(pattern, func_context, re.IGNORECASE):
                return True

        return False

    def _check_rate_limited(self, node: ast.FunctionDef, content: str) -> bool:
        """Check if endpoint is rate limited."""
        # Look for rate limiting patterns
        rate_limit_patterns = [
            r'@.*rate.*limit',
            r'rate.*limited',
            r'throttle',
            r'semaphore',
            r'acquire.*rate'
        ]

        # Check function decorators and content around function
        func_start = getattr(node, 'lineno', 0)
        func_lines = content.split('\n')[max(0, func_start-5):func_start+10]
        func_context = '\n'.join(func_lines)

        for pattern in rate_limit_patterns:
            if re.search(pattern, func_context, re.IGNORECASE):
                return True

        return False

    async def _analyze_endpoint_sensitivity(self, endpoint: APIEndpoint, content: str):
        """Analyze endpoint for sensitive data handling."""
        try:
            # Check for sensitive data patterns in the endpoint context
            sensitive_data_found = []
            highest_sensitivity = DataSensitivity.PUBLIC
            compliance_frameworks = set()

            for pattern in self.sensitive_patterns:
                if re.search(pattern.pattern, content, re.IGNORECASE):
                    sensitive_data_found.append(pattern.name)

                    # Update highest sensitivity level
                    if pattern.sensitivity.value > highest_sensitivity.value:
                        highest_sensitivity = pattern.sensitivity

                    # Add compliance frameworks
                    compliance_frameworks.update(pattern.compliance_frameworks)

            # Update endpoint with findings
            endpoint.sensitive_data_types = sensitive_data_found
            endpoint.data_sensitivity = highest_sensitivity
            endpoint.compliance_frameworks = list(compliance_frameworks)

            # Determine security risk based on sensitivity
            if highest_sensitivity == DataSensitivity.TOP_SECRET:
                endpoint.security_risk = SecurityRisk.CRITICAL
            elif highest_sensitivity == DataSensitivity.RESTRICTED:
                endpoint.security_risk = SecurityRisk.HIGH
            elif highest_sensitivity == DataSensitivity.CONFIDENTIAL:
                endpoint.security_risk = SecurityRisk.MEDIUM
            else:
                endpoint.security_risk = SecurityRisk.LOW

        except Exception as e:
            logger.error(f"Failed to analyze endpoint sensitivity: {e}")

    async def _process_discovered_endpoint(self, endpoint: APIEndpoint):
        """Process and store discovered endpoint."""
        try:
            # Check if endpoint already exists
            if endpoint.id in self.endpoints:
                existing = self.endpoints[endpoint.id]
                existing.updated_at = datetime.now(timezone.utc)

                # Update with new information if more detailed
                if len(endpoint.sensitive_data_types) > len(existing.sensitive_data_types):
                    existing.sensitive_data_types = endpoint.sensitive_data_types
                    existing.data_sensitivity = endpoint.data_sensitivity
                    existing.security_risk = endpoint.security_risk
                    existing.compliance_frameworks = endpoint.compliance_frameworks
            else:
                # Add new endpoint
                self.endpoints[endpoint.id] = endpoint

                # Log discovery
                if self.audit_trail:
                    self.audit_trail.log_event(
                        AuditEventType.SYSTEM_ACCESS,
                        None,
                        "api_inventory",
                        "endpoint_discovered",
                        {
                            "endpoint_id": endpoint.id,
                            "url": endpoint.url,
                            "method": endpoint.method,
                            "api_type": endpoint.api_type.value,
                            "data_sensitivity": endpoint.data_sensitivity.value,
                            "security_risk": endpoint.security_risk.value
                        },
                        risk_level=RiskLevel.MEDIUM if endpoint.security_risk in [SecurityRisk.HIGH, SecurityRisk.CRITICAL] else RiskLevel.LOW
                    )

        except Exception as e:
            logger.error(f"Failed to process discovered endpoint {endpoint.id}: {e}")

    async def monitor_api_traffic(self, request: Request, response: Response,
                                 response_time_ms: float, request_body: Optional[str] = None,
                                 response_body: Optional[str] = None):
        """Monitor API traffic for sensitive data and security analysis."""
        try:
            # Extract request information
            endpoint_id = self._identify_endpoint(request)

            # Analyze request and response for sensitive data
            sensitive_data_detected = []
            if request_body:
                sensitive_data_detected.extend(self._detect_sensitive_data(request_body))
            if response_body:
                sensitive_data_detected.extend(self._detect_sensitive_data(response_body))

            # Create traffic metrics
            traffic_metric = APITrafficMetrics(
                endpoint_id=endpoint_id,
                timestamp=datetime.now(timezone.utc),
                method=request.method,
                status_code=response.status_code,
                response_time_ms=response_time_ms,
                request_size_bytes=len(request_body.encode('utf-8')) if request_body else 0,
                response_size_bytes=len(response_body.encode('utf-8')) if response_body else 0,
                user_agent=request.headers.get('user-agent'),
                ip_address=request.client.host if request.client else None,
                sensitive_data_detected=list(set(sensitive_data_detected))
            )

            # Buffer traffic metrics
            with self._buffer_lock:
                self._traffic_buffer.append(traffic_metric)

                # Flush buffer if full
                if len(self._traffic_buffer) >= self._max_buffer_size:
                    await self._flush_traffic_buffer()

            # Update endpoint statistics
            await self._update_endpoint_stats(endpoint_id, traffic_metric)

            # Check for security alerts
            await self._check_traffic_security_alerts(traffic_metric)

        except Exception as e:
            logger.error(f"Failed to monitor API traffic: {e}")

    def _identify_endpoint(self, request: Request) -> str:
        """Identify which endpoint the request matches."""
        request_path = str(request.url.path)
        request_method = request.method

        # Try to match with known internal endpoints
        for endpoint_id, endpoint in self.endpoints.items():
            if (endpoint.api_type == APIType.INTERNAL and
                endpoint.method == request_method and
                self._path_matches(request_path, endpoint.url)):
                return endpoint_id

        # Create generic endpoint ID if not found
        return f"unknown_{request_method.lower()}_{hash(request_path) % 10000}"

    def _path_matches(self, request_path: str, endpoint_path: str) -> bool:
        """Check if request path matches endpoint path pattern."""
        # Simple path matching - could be enhanced with regex patterns
        if endpoint_path == request_path:
            return True

        # Handle path parameters (basic implementation)
        endpoint_parts = endpoint_path.split('/')
        request_parts = request_path.split('/')

        if len(endpoint_parts) != len(request_parts):
            return False

        for ep_part, req_part in zip(endpoint_parts, request_parts):
            if ep_part.startswith('{') and ep_part.endswith('}'):
                continue  # Path parameter
            if ep_part != req_part:
                return False

        return True

    def _detect_sensitive_data(self, content: str) -> List[str]:
        """Detect sensitive data in content using patterns."""
        detected = []

        for pattern in self.sensitive_patterns:
            if re.search(pattern.pattern, content, re.IGNORECASE):
                detected.append(pattern.name)

        return detected

    async def _update_endpoint_stats(self, endpoint_id: str, traffic_metric: APITrafficMetrics):
        """Update endpoint statistics with new traffic data."""
        try:
            if endpoint_id in self.endpoints:
                endpoint = self.endpoints[endpoint_id]

                # Update access statistics
                endpoint.access_count += 1
                endpoint.last_accessed = traffic_metric.timestamp

                # Update error count
                if traffic_metric.status_code >= 400:
                    endpoint.error_count += 1

                # Update average response time (simple moving average)
                if endpoint.avg_response_time == 0:
                    endpoint.avg_response_time = traffic_metric.response_time_ms
                else:
                    # Weighted average with more weight on recent requests
                    endpoint.avg_response_time = (
                        endpoint.avg_response_time * 0.9 +
                        traffic_metric.response_time_ms * 0.1
                    )

                endpoint.updated_at = datetime.now(timezone.utc)

                # Update metrics
                increment_counter(f"api_endpoint_requests_{endpoint_id}", 1)
                if traffic_metric.status_code >= 400:
                    increment_counter(f"api_endpoint_errors_{endpoint_id}", 1)

        except Exception as e:
            logger.error(f"Failed to update endpoint stats for {endpoint_id}: {e}")

    async def _check_traffic_security_alerts(self, traffic_metric: APITrafficMetrics):
        """Check traffic for security alerts."""
        try:
            alerts = []

            # Check for sensitive data exposure
            if traffic_metric.sensitive_data_detected:
                alerts.append({
                    "type": "sensitive_data_exposure",
                    "severity": "high",
                    "message": f"Sensitive data detected: {', '.join(traffic_metric.sensitive_data_detected)}",
                    "endpoint_id": traffic_metric.endpoint_id
                })

            # Check for high error rates
            if traffic_metric.status_code >= 500:
                alerts.append({
                    "type": "server_error",
                    "severity": "medium",
                    "message": f"Server error {traffic_metric.status_code}",
                    "endpoint_id": traffic_metric.endpoint_id
                })

            # Check for slow responses
            if traffic_metric.response_time_ms > 5000:  # 5 seconds
                alerts.append({
                    "type": "slow_response",
                    "severity": "low",
                    "message": f"Slow response: {traffic_metric.response_time_ms}ms",
                    "endpoint_id": traffic_metric.endpoint_id
                })

            # Log alerts
            for alert in alerts:
                logger.warning("API security alert", **alert)
                increment_counter("api_security_alerts", 1, {"type": alert["type"]})

                # Log to audit trail
                if self.audit_trail:
                    self.audit_trail.log_event(
                        AuditEventType.SECURITY_VIOLATION,
                        None,
                        "api_inventory",
                        "security_alert",
                        alert,
                        risk_level=RiskLevel.HIGH if alert["severity"] == "high" else RiskLevel.MEDIUM
                    )

        except Exception as e:
            logger.error(f"Failed to check traffic security alerts: {e}")

    async def _flush_traffic_buffer(self):
        """Flush traffic buffer to persistent storage."""
        try:
            with self._buffer_lock:
                if not self._traffic_buffer:
                    return

                # Move buffer to metrics list
                self.traffic_metrics.extend(self._traffic_buffer)
                buffer_size = len(self._traffic_buffer)
                self._traffic_buffer.clear()

                # Keep only recent metrics (last 24 hours)
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
                self.traffic_metrics = [
                    metric for metric in self.traffic_metrics
                    if metric.timestamp > cutoff_time
                ]

                logger.debug(f"Flushed {buffer_size} traffic metrics to storage")
                increment_counter("api_traffic_metrics_flushed", buffer_size)

        except Exception as e:
            logger.error(f"Failed to flush traffic buffer: {e}")

    def generate_inventory_report(self) -> Dict[str, Any]:
        """Generate comprehensive API inventory report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Basic statistics
                total_endpoints = len(self.endpoints)
                external_apis = sum(1 for ep in self.endpoints.values() if ep.api_type == APIType.EXTERNAL)
                internal_apis = sum(1 for ep in self.endpoints.values() if ep.api_type == APIType.INTERNAL)

                # Security statistics
                security_stats = {
                    SecurityRisk.LOW: 0,
                    SecurityRisk.MEDIUM: 0,
                    SecurityRisk.HIGH: 0,
                    SecurityRisk.CRITICAL: 0
                }

                sensitivity_stats = {
                    DataSensitivity.PUBLIC: 0,
                    DataSensitivity.INTERNAL: 0,
                    DataSensitivity.CONFIDENTIAL: 0,
                    DataSensitivity.RESTRICTED: 0,
                    DataSensitivity.TOP_SECRET: 0
                }

                compliance_stats = defaultdict(int)

                for endpoint in self.endpoints.values():
                    security_stats[endpoint.security_risk] += 1
                    sensitivity_stats[endpoint.data_sensitivity] += 1

                    for framework in endpoint.compliance_frameworks:
                        compliance_stats[framework.value] += 1

                # Traffic statistics (last 24 hours)
                recent_traffic = [
                    metric for metric in self.traffic_metrics
                    if metric.timestamp > current_time - timedelta(hours=24)
                ]

                traffic_stats = {
                    "total_requests_24h": len(recent_traffic),
                    "unique_endpoints_accessed": len(set(metric.endpoint_id for metric in recent_traffic)),
                    "avg_response_time_ms": sum(metric.response_time_ms for metric in recent_traffic) / len(recent_traffic) if recent_traffic else 0,
                    "error_rate_percent": sum(1 for metric in recent_traffic if metric.status_code >= 400) / len(recent_traffic) * 100 if recent_traffic else 0,
                    "sensitive_data_exposures": sum(len(metric.sensitive_data_detected) for metric in recent_traffic)
                }

                # Top endpoints by access
                endpoint_access_counts = {
                    ep.id: ep.access_count for ep in self.endpoints.values()
                }
                top_endpoints = sorted(endpoint_access_counts.items(), key=lambda x: x[1], reverse=True)[:10]

                # High-risk endpoints
                high_risk_endpoints = [
                    {
                        "id": ep.id,
                        "url": ep.url,
                        "method": ep.method,
                        "security_risk": ep.security_risk.value,
                        "data_sensitivity": ep.data_sensitivity.value,
                        "sensitive_data_types": ep.sensitive_data_types,
                        "compliance_frameworks": [f.value for f in ep.compliance_frameworks]
                    }
                    for ep in self.endpoints.values()
                    if ep.security_risk in [SecurityRisk.HIGH, SecurityRisk.CRITICAL]
                ]

                return {
                    "report_generated_at": current_time.isoformat(),
                    "last_scan_time": self._last_scan_time.isoformat() if self._last_scan_time else None,
                    "summary": {
                        "total_endpoints": total_endpoints,
                        "external_apis": external_apis,
                        "internal_apis": internal_apis,
                        "high_risk_endpoints": len(high_risk_endpoints),
                        "sensitive_endpoints": sum(1 for ep in self.endpoints.values()
                                                 if ep.data_sensitivity in [DataSensitivity.CONFIDENTIAL,
                                                                          DataSensitivity.RESTRICTED,
                                                                          DataSensitivity.TOP_SECRET])
                    },
                    "security_distribution": {risk.value: count for risk, count in security_stats.items()},
                    "sensitivity_distribution": {sens.value: count for sens, count in sensitivity_stats.items()},
                    "compliance_coverage": dict(compliance_stats),
                    "traffic_statistics": traffic_stats,
                    "top_endpoints": [{"endpoint_id": ep_id, "access_count": count} for ep_id, count in top_endpoints],
                    "high_risk_endpoints": high_risk_endpoints,
                    "recommendations": self._generate_security_recommendations()
                }

        except Exception as e:
            logger.error(f"Failed to generate inventory report: {e}")
            return {"error": str(e)}

    def _generate_security_recommendations(self) -> List[str]:
        """Generate security recommendations based on inventory analysis."""
        recommendations = []

        try:
            # Check for high-risk endpoints
            high_risk_count = sum(1 for ep in self.endpoints.values()
                                if ep.security_risk in [SecurityRisk.HIGH, SecurityRisk.CRITICAL])

            if high_risk_count > 0:
                recommendations.append(f"Review and secure {high_risk_count} high-risk API endpoints")

            # Check for unauthenticated sensitive endpoints
            unauth_sensitive = sum(1 for ep in self.endpoints.values()
                                 if not ep.authentication_required and
                                 ep.data_sensitivity in [DataSensitivity.CONFIDENTIAL,
                                                       DataSensitivity.RESTRICTED,
                                                       DataSensitivity.TOP_SECRET])

            if unauth_sensitive > 0:
                recommendations.append(f"Add authentication to {unauth_sensitive} sensitive endpoints")

            # Check for unmonitored external APIs
            external_count = sum(1 for ep in self.endpoints.values() if ep.api_type == APIType.EXTERNAL)
            if external_count > 10:
                recommendations.append("Consider implementing API gateway for external API management")

            # Check for compliance gaps
            gdpr_endpoints = sum(1 for ep in self.endpoints.values()
                               if ComplianceFramework.GDPR in ep.compliance_frameworks)
            if gdpr_endpoints > 0:
                recommendations.append("Ensure GDPR compliance documentation for personal data handling endpoints")

            if not recommendations:
                recommendations.append("API inventory security posture is good")

        except Exception as e:
            logger.error(f"Failed to generate security recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations

    def get_endpoint_details(self, endpoint_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific endpoint."""
        try:
            if endpoint_id not in self.endpoints:
                return None

            endpoint = self.endpoints[endpoint_id]

            # Get recent traffic for this endpoint
            recent_traffic = [
                metric for metric in self.traffic_metrics
                if metric.endpoint_id == endpoint_id and
                metric.timestamp > datetime.now(timezone.utc) - timedelta(hours=24)
            ]

            return {
                "endpoint": {
                    "id": endpoint.id,
                    "url": endpoint.url,
                    "method": endpoint.method,
                    "api_type": endpoint.api_type.value,
                    "source_file": endpoint.source_file,
                    "function_name": endpoint.function_name,
                    "parameters": endpoint.parameters,
                    "response_fields": endpoint.response_fields,
                    "authentication_required": endpoint.authentication_required,
                    "rate_limited": endpoint.rate_limited,
                    "data_sensitivity": endpoint.data_sensitivity.value,
                    "security_risk": endpoint.security_risk.value,
                    "compliance_frameworks": [f.value for f in endpoint.compliance_frameworks],
                    "sensitive_data_types": endpoint.sensitive_data_types,
                    "dependencies": endpoint.dependencies,
                    "created_at": endpoint.created_at.isoformat(),
                    "updated_at": endpoint.updated_at.isoformat()
                },
                "statistics": {
                    "total_access_count": endpoint.access_count,
                    "error_count": endpoint.error_count,
                    "avg_response_time_ms": endpoint.avg_response_time,
                    "last_accessed": endpoint.last_accessed.isoformat() if endpoint.last_accessed else None,
                    "requests_24h": len(recent_traffic),
                    "errors_24h": sum(1 for metric in recent_traffic if metric.status_code >= 400),
                    "sensitive_data_exposures_24h": sum(len(metric.sensitive_data_detected) for metric in recent_traffic)
                }
            }

        except Exception as e:
            logger.error(f"Failed to get endpoint details for {endpoint_id}: {e}")
            return None

    async def start_continuous_monitoring(self, interval_seconds: int = 300):
        """Start continuous API monitoring and discovery."""
        logger.info(f"Starting continuous API monitoring with {interval_seconds}s interval")

        while True:
            try:
                # Periodic discovery scan
                await self.discover_apis()

                # Flush traffic buffer
                await self._flush_traffic_buffer()

                # Update metrics
                set_gauge("api_inventory_monitoring_active", 1)

                await asyncio.sleep(interval_seconds)

            except Exception as e:
                logger.error(f"Error in continuous monitoring: {e}")
                await asyncio.sleep(60)  # Wait before retrying


# Global instance
api_inventory_manager = APIInventoryManager()
