"""
Advanced Rate Limiting System
Sophisticated rate limiting with sliding window, token bucket, adaptive throttling, and per-endpoint limits.
"""

import asyncio
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union
import threading

import structlog

from ..core.logging_config import get_logger
from ..monitoring import increment_counter, set_gauge, record_timer


logger = get_logger(__name__)


class RateLimitAlgorithm(Enum):
    """Rate limiting algorithms."""
    TOKEN_BUCKET = "token_bucket"
    SLIDING_WINDOW = "sliding_window"
    FIXED_WINDOW = "fixed_window"
    ADAPTIVE = "adaptive"


class UserTier(Enum):
    """User tiers for rate limiting."""
    FREE = "free"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"
    INTERNAL = "internal"


@dataclass
class RateLimitConfig:
    """Rate limit configuration."""
    requests_per_minute: int
    requests_per_hour: int
    burst_size: int
    concurrent_requests: int
    weight_factor: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "requests_per_minute": self.requests_per_minute,
            "requests_per_hour": self.requests_per_hour,
            "burst_size": self.burst_size,
            "concurrent_requests": self.concurrent_requests,
            "weight_factor": self.weight_factor
        }


@dataclass
class RateLimitResult:
    """Rate limit check result."""
    allowed: bool
    reason: Optional[str] = None
    retry_after: Optional[int] = None
    tokens_remaining: Optional[int] = None
    requests_remaining_minute: Optional[int] = None
    requests_remaining_hour: Optional[int] = None
    tier: Optional[str] = None
    system_load_factor: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "allowed": self.allowed,
            "reason": self.reason,
            "retry_after": self.retry_after,
            "tokens_remaining": self.tokens_remaining,
            "requests_remaining_minute": self.requests_remaining_minute,
            "requests_remaining_hour": self.requests_remaining_hour,
            "tier": self.tier,
            "system_load_factor": self.system_load_factor
        }


class AdvancedRateLimiter:
    """Advanced rate limiter with multiple algorithms and adaptive throttling."""
    
    def __init__(self):
        # Core data structures
        self.token_buckets: Dict[str, Dict[str, Any]] = {}
        self.sliding_windows: Dict[str, deque] = defaultdict(lambda: deque(maxlen=3600))  # 1 hour max
        self.user_tiers: Dict[str, UserTier] = {}
        self.active_requests: Dict[str, int] = defaultdict(int)
        
        # Configuration
        self.tier_configs = {
            UserTier.FREE: RateLimitConfig(
                requests_per_minute=60,
                requests_per_hour=1000,
                burst_size=10,
                concurrent_requests=5
            ),
            UserTier.PREMIUM: RateLimitConfig(
                requests_per_minute=300,
                requests_per_hour=10000,
                burst_size=50,
                concurrent_requests=20
            ),
            UserTier.ENTERPRISE: RateLimitConfig(
                requests_per_minute=1000,
                requests_per_hour=50000,
                burst_size=100,
                concurrent_requests=50
            ),
            UserTier.INTERNAL: RateLimitConfig(
                requests_per_minute=5000,
                requests_per_hour=100000,
                burst_size=500,
                concurrent_requests=100
            )
        }
        
        # Endpoint-specific configurations
        self.endpoint_configs = {
            "/api/v1/analyze": RateLimitConfig(30, 500, 5, 3, 2.0),
            "/api/v1/bulk-analyze": RateLimitConfig(10, 100, 2, 1, 5.0),
            "/api/v1/search": RateLimitConfig(100, 2000, 20, 10, 1.0),
            "/api/v1/upload": RateLimitConfig(20, 200, 5, 2, 3.0)
        }
        
        # Method-specific configurations
        self.method_configs = {
            "GET": RateLimitConfig(500, 10000, 100, 50, 1.0),
            "POST": RateLimitConfig(100, 2000, 20, 10, 2.0),
            "PUT": RateLimitConfig(50, 1000, 10, 5, 2.0),
            "DELETE": RateLimitConfig(30, 500, 5, 3, 3.0)
        }
        
        # System state
        self.system_load_factor = 1.0
        self.adaptive_throttling_enabled = True
        self.global_rate_limit = {"enabled": False, "max_rps": 1000}
        
        # Maintenance
        self._lock = threading.RLock()
        self._cleanup_interval = 300  # 5 minutes
        self._last_cleanup = time.time()
        
        logger.info("AdvancedRateLimiter initialized")
    
    def check_rate_limit(self, identifier: str, tier: Optional[UserTier] = None,
                        endpoint: Optional[str] = None, method: str = "GET",
                        ip_address: Optional[str] = None, weight: float = 1.0) -> RateLimitResult:
        """Comprehensive rate limit check."""
        with self._lock:
            current_time = time.time()
            
            # Periodic cleanup
            if current_time - self._last_cleanup > self._cleanup_interval:
                self._cleanup_old_entries()
                self._last_cleanup = current_time
            
            # Determine user tier
            user_tier = tier or self.user_tiers.get(identifier, UserTier.FREE)
            
            # Check global rate limit
            if self.global_rate_limit["enabled"]:
                global_result = self._check_global_rate_limit(current_time)
                if not global_result.allowed:
                    return global_result
            
            # Get effective configuration with adaptive throttling
            effective_config = self._get_effective_config(user_tier, endpoint, method)
            
            # Check concurrent requests
            concurrent_result = self._check_concurrent_requests(identifier, effective_config)
            if not concurrent_result.allowed:
                return concurrent_result
            
            # Token bucket algorithm
            token_result = self._check_token_bucket(identifier, effective_config, current_time, weight)
            if not token_result.allowed:
                return token_result
            
            # Sliding window algorithms
            sliding_result = self._check_sliding_window(identifier, effective_config, current_time, weight)
            if not sliding_result.allowed:
                return sliding_result
            
            # Endpoint-specific limits
            if endpoint and endpoint in self.endpoint_configs:
                endpoint_result = self._check_endpoint_limits(identifier, endpoint, current_time)
                if not endpoint_result.allowed:
                    return endpoint_result
            
            # Method-specific limits
            method_result = self._check_method_limits(identifier, method, current_time)
            if not method_result.allowed:
                return method_result
            
            # IP-based limits (if provided)
            if ip_address:
                ip_result = self._check_ip_limits(ip_address, current_time)
                if not ip_result.allowed:
                    return ip_result
            
            # All checks passed - consume resources
            self._consume_resources(identifier, user_tier, effective_config, current_time, weight)
            
            # Track active request
            self.active_requests[identifier] += 1
            
            increment_counter("rate_limit_allowed", 1, {
                "tier": user_tier.value,
                "endpoint": endpoint or "unknown",
                "method": method
            })
            
            return RateLimitResult(
                allowed=True,
                tier=user_tier.value,
                tokens_remaining=int(self.token_buckets.get(identifier, {}).get("tokens", 0)),
                requests_remaining_minute=self._get_remaining_requests(identifier, "minute", effective_config),
                requests_remaining_hour=self._get_remaining_requests(identifier, "hour", effective_config),
                system_load_factor=self.system_load_factor
            )
    
    def _check_global_rate_limit(self, current_time: float) -> RateLimitResult:
        """Check global system-wide rate limit."""
        global_key = "__global__"
        window = self.sliding_windows[global_key]
        
        # Clean window (1 second)
        cutoff_time = current_time - 1.0
        while window and window[0] < cutoff_time:
            window.popleft()
        
        if len(window) >= self.global_rate_limit["max_rps"]:
            increment_counter("rate_limit_blocked", 1, {"algorithm": "global"})
            return RateLimitResult(
                allowed=False,
                reason="global_rate_limit_exceeded",
                retry_after=1
            )
        
        window.append(current_time)
        return RateLimitResult(allowed=True)
    
    def _get_effective_config(self, tier: UserTier, endpoint: Optional[str], 
                             method: str) -> RateLimitConfig:
        """Get effective rate limit configuration with adaptive throttling."""
        base_config = self.tier_configs[tier]
        
        # Apply adaptive throttling
        if self.adaptive_throttling_enabled:
            throttle_factor = self._calculate_throttle_factor()
            
            return RateLimitConfig(
                requests_per_minute=int(base_config.requests_per_minute * throttle_factor),
                requests_per_hour=int(base_config.requests_per_hour * throttle_factor),
                burst_size=int(base_config.burst_size * throttle_factor),
                concurrent_requests=int(base_config.concurrent_requests * throttle_factor),
                weight_factor=base_config.weight_factor
            )
        
        return base_config
    
    def _calculate_throttle_factor(self) -> float:
        """Calculate throttling factor based on system load."""
        if self.system_load_factor <= 1.0:
            return 1.0
        elif self.system_load_factor <= 1.5:
            return 0.8
        elif self.system_load_factor <= 2.0:
            return 0.6
        else:
            return 0.4
    
    def _check_concurrent_requests(self, identifier: str, config: RateLimitConfig) -> RateLimitResult:
        """Check concurrent request limits."""
        active_count = self.active_requests.get(identifier, 0)
        
        if active_count >= config.concurrent_requests:
            increment_counter("rate_limit_blocked", 1, {"algorithm": "concurrent"})
            return RateLimitResult(
                allowed=False,
                reason="concurrent_limit_exceeded",
                retry_after=1
            )
        
        return RateLimitResult(allowed=True)
    
    def _check_token_bucket(self, identifier: str, config: RateLimitConfig, 
                           current_time: float, weight: float) -> RateLimitResult:
        """Enhanced token bucket algorithm."""
        if identifier not in self.token_buckets:
            self.token_buckets[identifier] = {
                "tokens": config.burst_size,
                "last_refill": current_time,
                "total_requests": 0
            }
        
        bucket = self.token_buckets[identifier]
        
        # Calculate tokens to add based on time elapsed
        time_elapsed = current_time - bucket["last_refill"]
        tokens_to_add = time_elapsed * (config.requests_per_minute / 60.0)
        
        # Refill bucket
        bucket["tokens"] = min(config.burst_size, bucket["tokens"] + tokens_to_add)
        bucket["last_refill"] = current_time
        
        # Check if request can be processed (considering weight)
        tokens_needed = weight * config.weight_factor
        
        if bucket["tokens"] >= tokens_needed:
            bucket["tokens"] -= tokens_needed
            bucket["total_requests"] += 1
            return RateLimitResult(allowed=True)
        
        # Calculate retry after time
        retry_after = (tokens_needed - bucket["tokens"]) / (config.requests_per_minute / 60.0)
        
        increment_counter("rate_limit_blocked", 1, {"algorithm": "token_bucket"})
        return RateLimitResult(
            allowed=False,
            reason="token_bucket_exhausted",
            retry_after=max(1, int(retry_after)),
            tokens_remaining=int(bucket["tokens"])
        )

    def _check_sliding_window(self, identifier: str, config: RateLimitConfig,
                             current_time: float, weight: float) -> RateLimitResult:
        """Enhanced sliding window algorithm."""
        window = self.sliding_windows[identifier]

        # Check minute window
        minute_cutoff = current_time - 60
        minute_requests = sum(entry.get("weight", 1) for entry in window
                            if isinstance(entry, dict) and entry.get("time", 0) > minute_cutoff)

        weighted_request = weight * config.weight_factor

        if minute_requests + weighted_request > config.requests_per_minute:
            increment_counter("rate_limit_blocked", 1, {"algorithm": "sliding_window_minute"})
            return RateLimitResult(
                allowed=False,
                reason="minute_limit_exceeded",
                retry_after=60,
                requests_remaining_minute=max(0, config.requests_per_minute - minute_requests)
            )

        # Check hour window
        hour_cutoff = current_time - 3600
        hour_requests = sum(entry.get("weight", 1) for entry in window
                          if isinstance(entry, dict) and entry.get("time", 0) > hour_cutoff)

        if hour_requests + weighted_request > config.requests_per_hour:
            increment_counter("rate_limit_blocked", 1, {"algorithm": "sliding_window_hour"})
            return RateLimitResult(
                allowed=False,
                reason="hour_limit_exceeded",
                retry_after=3600,
                requests_remaining_hour=max(0, config.requests_per_hour - hour_requests)
            )

        return RateLimitResult(allowed=True)

    def _check_endpoint_limits(self, identifier: str, endpoint: str,
                              current_time: float) -> RateLimitResult:
        """Check endpoint-specific rate limits."""
        endpoint_config = self.endpoint_configs[endpoint]
        endpoint_key = f"{identifier}:endpoint:{endpoint}"

        window = self.sliding_windows[endpoint_key]

        # Clean window (1 minute)
        cutoff_time = current_time - 60
        while window and window[0] < cutoff_time:
            window.popleft()

        if len(window) >= endpoint_config.requests_per_minute:
            increment_counter("rate_limit_blocked", 1, {
                "algorithm": "endpoint_specific",
                "endpoint": endpoint
            })
            return RateLimitResult(
                allowed=False,
                reason="endpoint_limit_exceeded",
                retry_after=60
            )

        window.append(current_time)
        return RateLimitResult(allowed=True)

    def _check_method_limits(self, identifier: str, method: str,
                            current_time: float) -> RateLimitResult:
        """Check method-specific rate limits."""
        if method not in self.method_configs:
            return RateLimitResult(allowed=True)

        method_config = self.method_configs[method]
        method_key = f"{identifier}:method:{method}"

        window = self.sliding_windows[method_key]

        # Clean window (1 minute)
        cutoff_time = current_time - 60
        while window and window[0] < cutoff_time:
            window.popleft()

        if len(window) >= method_config.requests_per_minute:
            increment_counter("rate_limit_blocked", 1, {
                "algorithm": "method_specific",
                "method": method
            })
            return RateLimitResult(
                allowed=False,
                reason="method_limit_exceeded",
                retry_after=60
            )

        window.append(current_time)
        return RateLimitResult(allowed=True)

    def _check_ip_limits(self, ip_address: str, current_time: float) -> RateLimitResult:
        """Check IP-based rate limits."""
        ip_key = f"ip:{ip_address}"
        window = self.sliding_windows[ip_key]

        # IP-specific limits (stricter for unknown IPs)
        ip_limit = 200  # requests per minute per IP

        # Clean window (1 minute)
        cutoff_time = current_time - 60
        while window and window[0] < cutoff_time:
            window.popleft()

        if len(window) >= ip_limit:
            increment_counter("rate_limit_blocked", 1, {
                "algorithm": "ip_specific",
                "ip": ip_address
            })
            return RateLimitResult(
                allowed=False,
                reason="ip_limit_exceeded",
                retry_after=60
            )

        window.append(current_time)
        return RateLimitResult(allowed=True)

    def _consume_resources(self, identifier: str, tier: UserTier, config: RateLimitConfig,
                          current_time: float, weight: float):
        """Consume resources after successful rate limit check."""
        # Update sliding window with weighted entry
        window = self.sliding_windows[identifier]
        window.append({
            "time": current_time,
            "weight": weight * config.weight_factor,
            "tier": tier.value
        })

    def _get_remaining_requests(self, identifier: str, window_type: str,
                               config: RateLimitConfig) -> int:
        """Get remaining requests for a time window."""
        window = self.sliding_windows[identifier]
        current_time = time.time()

        if window_type == "minute":
            cutoff_time = current_time - 60
            limit = config.requests_per_minute
        elif window_type == "hour":
            cutoff_time = current_time - 3600
            limit = config.requests_per_hour
        else:
            return 0

        used_requests = sum(entry.get("weight", 1) for entry in window
                          if isinstance(entry, dict) and entry.get("time", 0) > cutoff_time)

        return max(0, limit - used_requests)

    def _cleanup_old_entries(self):
        """Clean up old rate limiting entries."""
        current_time = time.time()

        # Clean up token buckets older than 1 hour
        expired_identifiers = []
        for identifier, bucket in self.token_buckets.items():
            if current_time - bucket["last_refill"] > 3600:
                expired_identifiers.append(identifier)

        for identifier in expired_identifiers:
            del self.token_buckets[identifier]

        # Clean up sliding windows
        cutoff_time = current_time - 3600
        for identifier, window in self.sliding_windows.items():
            while window:
                entry = window[0]
                entry_time = entry.get("time", entry) if isinstance(entry, dict) else entry
                if entry_time < cutoff_time:
                    window.popleft()
                else:
                    break

        # Clean up active requests (reset periodically)
        self.active_requests.clear()

        if expired_identifiers:
            logger.info("Rate limiter cleanup completed",
                       expired_buckets=len(expired_identifiers))

    def release_request(self, identifier: str):
        """Release an active request (call when request completes)."""
        with self._lock:
            if identifier in self.active_requests and self.active_requests[identifier] > 0:
                self.active_requests[identifier] -= 1

    def update_system_load(self, load_factor: float):
        """Update system load factor for adaptive throttling."""
        self.system_load_factor = max(0.1, min(3.0, load_factor))
        set_gauge("rate_limiter_system_load", self.system_load_factor)

        if load_factor > 1.5:
            logger.warning("High system load detected",
                         load_factor=load_factor,
                         adaptive_throttling=self.adaptive_throttling_enabled)

    def set_user_tier(self, identifier: str, tier: UserTier):
        """Set user tier for rate limiting."""
        with self._lock:
            self.user_tiers[identifier] = tier
            logger.info("User tier updated", identifier=identifier, tier=tier.value)

    def enable_global_rate_limit(self, max_rps: int):
        """Enable global rate limiting."""
        self.global_rate_limit = {"enabled": True, "max_rps": max_rps}
        logger.info("Global rate limit enabled", max_rps=max_rps)

    def disable_global_rate_limit(self):
        """Disable global rate limiting."""
        self.global_rate_limit["enabled"] = False
        logger.info("Global rate limit disabled")

    def get_rate_limit_status(self, identifier: str) -> Dict[str, Any]:
        """Get comprehensive rate limit status for identifier."""
        with self._lock:
            tier = self.user_tiers.get(identifier, UserTier.FREE)
            config = self._get_effective_config(tier, None, "GET")

            bucket = self.token_buckets.get(identifier, {})
            window = self.sliding_windows.get(identifier, deque())

            current_time = time.time()

            # Calculate current usage
            minute_requests = sum(entry.get("weight", 1) for entry in window
                                if isinstance(entry, dict) and entry.get("time", 0) > current_time - 60)
            hour_requests = sum(entry.get("weight", 1) for entry in window
                              if isinstance(entry, dict) and entry.get("time", 0) > current_time - 3600)

            return {
                "identifier": identifier,
                "tier": tier.value,
                "tokens_remaining": int(bucket.get("tokens", config.burst_size)),
                "requests_last_minute": minute_requests,
                "requests_last_hour": hour_requests,
                "requests_remaining_minute": max(0, config.requests_per_minute - minute_requests),
                "requests_remaining_hour": max(0, config.requests_per_hour - hour_requests),
                "concurrent_requests": self.active_requests.get(identifier, 0),
                "max_concurrent": config.concurrent_requests,
                "system_load_factor": self.system_load_factor,
                "adaptive_throttling": self.adaptive_throttling_enabled,
                "config": config.to_dict()
            }

    def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive rate limiter statistics."""
        with self._lock:
            active_buckets = len(self.token_buckets)
            total_windows = len(self.sliding_windows)
            total_active_requests = sum(self.active_requests.values())

            # Calculate tier distribution
            tier_distribution = defaultdict(int)
            for tier in self.user_tiers.values():
                tier_distribution[tier.value] += 1

            return {
                "active_buckets": active_buckets,
                "total_windows": total_windows,
                "total_active_requests": total_active_requests,
                "tier_distribution": dict(tier_distribution),
                "system_load_factor": self.system_load_factor,
                "adaptive_throttling_enabled": self.adaptive_throttling_enabled,
                "global_rate_limit": self.global_rate_limit,
                "tier_configs": {tier.value: config.to_dict()
                               for tier, config in self.tier_configs.items()}
            }


# Global rate limiter instance
advanced_rate_limiter = AdvancedRateLimiter()


def get_rate_limiter_status() -> Dict[str, Any]:
    """Get rate limiter system status."""
    return {
        "rate_limiter": "initialized",
        "system_stats": advanced_rate_limiter.get_system_stats(),
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
