"""
Token Whitelist Management System

This module implements configurable whitelist for established tokens (USDC, USDT, WETH, WBTC)
to reduce false positives on legitimate assets. It provides comprehensive token classification
and automatic whitelist management based on multiple criteria.

Features:
- Configurable whitelist for established tokens
- Multi-chain token support with address mapping
- Automatic whitelist updates based on market data
- Risk score adjustments for whitelisted tokens
- Whitelist validation and verification
- Integration with existing token analysis systems
"""

import asyncio
import json
import logging
import time
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import threading

import aiohttp
import structlog

from ..core.config import config
from ..core.logging_config import get_logger
from ..compliance.audit_framework import AuditTrail, AuditEventType, RiskLevel
from ..monitoring.dashboard import metrics_collector, increment_counter, set_gauge

logger = get_logger(__name__)


class WhitelistCategory(Enum):
    """Categories of whitelisted tokens."""
    STABLECOIN = "stablecoin"
    WRAPPED_NATIVE = "wrapped_native"
    BLUE_CHIP = "blue_chip"
    EXCHANGE_TOKEN = "exchange_token"
    DEFI_PROTOCOL = "defi_protocol"
    LAYER2_TOKEN = "layer2_token"
    GOVERNANCE_TOKEN = "governance_token"
    CUSTOM = "custom"


class WhitelistStatus(Enum):
    """Status of whitelist entries."""
    ACTIVE = "active"
    PENDING = "pending"
    DEPRECATED = "deprecated"
    SUSPENDED = "suspended"


class ValidationCriteria(Enum):
    """Criteria for automatic whitelist validation."""
    MARKET_CAP = "market_cap"
    TRADING_VOLUME = "trading_volume"
    LIQUIDITY = "liquidity"
    AGE = "age"
    EXCHANGE_LISTINGS = "exchange_listings"
    AUDIT_STATUS = "audit_status"
    COMMUNITY_SIZE = "community_size"


@dataclass
class WhitelistEntry:
    """Whitelist entry for a token."""
    token_address: str
    chain_id: int
    symbol: str
    name: str
    category: WhitelistCategory
    status: WhitelistStatus
    risk_score_adjustment: float  # Multiplier for risk scores (0.0 = no risk, 1.0 = normal risk)
    added_date: datetime
    last_validated: datetime
    validation_criteria: Dict[ValidationCriteria, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    auto_update: bool = True
    notes: str = ""


@dataclass
class WhitelistConfig:
    """Configuration for whitelist management."""
    auto_validation_enabled: bool = True
    validation_interval_hours: int = 24
    min_market_cap_usd: float = 1000000000  # $1B
    min_daily_volume_usd: float = 10000000   # $10M
    min_age_days: int = 365  # 1 year
    min_exchange_listings: int = 5
    risk_score_reduction: float = 0.1  # 90% risk reduction for whitelisted tokens
    max_whitelist_size: int = 1000


class TokenWhitelistManager:
    """
    Comprehensive token whitelist management system.
    
    Manages configurable whitelist for established tokens to reduce false positives
    in security analysis while maintaining robust validation criteria.
    """
    
    def __init__(self, audit_trail: Optional[AuditTrail] = None):
        self.audit_trail = audit_trail
        self.whitelist: Dict[str, WhitelistEntry] = {}
        self.config = WhitelistConfig()
        self.validation_cache: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()
        
        # Statistics
        self.stats = {
            "total_whitelisted": 0,
            "active_entries": 0,
            "last_validation": None,
            "validation_failures": 0,
            "risk_adjustments_applied": 0
        }
        
        # Initialize with well-known tokens
        self._initialize_default_whitelist()
        
        logger.info("TokenWhitelistManager initialized")
    
    def _initialize_default_whitelist(self):
        """Initialize whitelist with well-known established tokens."""
        # Define well-known tokens across major chains
        default_tokens = [
            # Ethereum Mainnet (Chain ID: 1)
            {
                "address": "******************************************",  # USDC
                "chain_id": 1,
                "symbol": "USDC",
                "name": "USD Coin",
                "category": WhitelistCategory.STABLECOIN,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # USDT
                "chain_id": 1,
                "symbol": "USDT",
                "name": "Tether USD",
                "category": WhitelistCategory.STABLECOIN,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # WETH
                "chain_id": 1,
                "symbol": "WETH",
                "name": "Wrapped Ether",
                "category": WhitelistCategory.WRAPPED_NATIVE,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # WBTC
                "chain_id": 1,
                "symbol": "WBTC",
                "name": "Wrapped BTC",
                "category": WhitelistCategory.BLUE_CHIP,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # DAI
                "chain_id": 1,
                "symbol": "DAI",
                "name": "Dai Stablecoin",
                "category": WhitelistCategory.STABLECOIN,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # UNI
                "chain_id": 1,
                "symbol": "UNI",
                "name": "Uniswap",
                "category": WhitelistCategory.DEFI_PROTOCOL,
                "risk_adjustment": 0.1
            },
            {
                "address": "******************************************",  # AAVE
                "chain_id": 1,
                "symbol": "AAVE",
                "name": "Aave Token",
                "category": WhitelistCategory.DEFI_PROTOCOL,
                "risk_adjustment": 0.1
            },
            {
                "address": "0x514910771AF9Ca656af840dff83E8264EcF986CA",  # LINK
                "chain_id": 1,
                "symbol": "LINK",
                "name": "ChainLink Token",
                "category": WhitelistCategory.BLUE_CHIP,
                "risk_adjustment": 0.1
            },
            
            # Polygon (Chain ID: 137)
            {
                "address": "******************************************",  # USDC on Polygon
                "chain_id": 137,
                "symbol": "USDC",
                "name": "USD Coin (PoS)",
                "category": WhitelistCategory.STABLECOIN,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # USDT on Polygon
                "chain_id": 137,
                "symbol": "USDT",
                "name": "Tether USD (PoS)",
                "category": WhitelistCategory.STABLECOIN,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # WETH on Polygon
                "chain_id": 137,
                "symbol": "WETH",
                "name": "Wrapped Ether",
                "category": WhitelistCategory.WRAPPED_NATIVE,
                "risk_adjustment": 0.05
            },
            
            # Binance Smart Chain (Chain ID: 56)
            {
                "address": "******************************************",  # USDC on BSC
                "chain_id": 56,
                "symbol": "USDC",
                "name": "USD Coin",
                "category": WhitelistCategory.STABLECOIN,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # USDT on BSC
                "chain_id": 56,
                "symbol": "USDT",
                "name": "Tether USD",
                "category": WhitelistCategory.STABLECOIN,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # WETH on BSC
                "chain_id": 56,
                "symbol": "ETH",
                "name": "Ethereum Token",
                "category": WhitelistCategory.BLUE_CHIP,
                "risk_adjustment": 0.05
            },
            
            # Arbitrum (Chain ID: 42161)
            {
                "address": "******************************************",  # USDC on Arbitrum
                "chain_id": 42161,
                "symbol": "USDC",
                "name": "USD Coin (Arb1)",
                "category": WhitelistCategory.STABLECOIN,
                "risk_adjustment": 0.05
            },
            {
                "address": "******************************************",  # WETH on Arbitrum
                "chain_id": 42161,
                "symbol": "WETH",
                "name": "Wrapped Ether",
                "category": WhitelistCategory.WRAPPED_NATIVE,
                "risk_adjustment": 0.05
            }
        ]
        
        # Add default tokens to whitelist
        current_time = datetime.now(timezone.utc)
        for token_data in default_tokens:
            entry_key = f"{token_data['chain_id']}:{token_data['address'].lower()}"
            
            entry = WhitelistEntry(
                token_address=token_data['address'].lower(),
                chain_id=token_data['chain_id'],
                symbol=token_data['symbol'],
                name=token_data['name'],
                category=token_data['category'],
                status=WhitelistStatus.ACTIVE,
                risk_score_adjustment=token_data['risk_adjustment'],
                added_date=current_time,
                last_validated=current_time,
                validation_criteria={
                    ValidationCriteria.MARKET_CAP: "established",
                    ValidationCriteria.EXCHANGE_LISTINGS: "major_exchanges",
                    ValidationCriteria.AGE: "mature"
                },
                metadata={
                    "source": "default_initialization",
                    "confidence": "high"
                },
                notes="Well-known established token"
            )
            
            self.whitelist[entry_key] = entry
        
        # Update statistics
        with self._lock:
            self.stats["total_whitelisted"] = len(self.whitelist)
            self.stats["active_entries"] = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.ACTIVE])
        
        logger.info(f"Initialized whitelist with {len(default_tokens)} default tokens")

    def is_whitelisted(self, token_address: str, chain_id: int) -> bool:
        """Check if a token is whitelisted."""
        try:
            entry_key = f"{chain_id}:{token_address.lower()}"
            entry = self.whitelist.get(entry_key)
            return entry is not None and entry.status == WhitelistStatus.ACTIVE
        except Exception as e:
            logger.error(f"Failed to check whitelist status: {e}")
            return False

    def get_whitelist_entry(self, token_address: str, chain_id: int) -> Optional[WhitelistEntry]:
        """Get whitelist entry for a token."""
        try:
            entry_key = f"{chain_id}:{token_address.lower()}"
            return self.whitelist.get(entry_key)
        except Exception as e:
            logger.error(f"Failed to get whitelist entry: {e}")
            return None

    def get_risk_adjustment(self, token_address: str, chain_id: int) -> float:
        """Get risk score adjustment for a whitelisted token."""
        try:
            entry = self.get_whitelist_entry(token_address, chain_id)
            if entry and entry.status == WhitelistStatus.ACTIVE:
                return entry.risk_score_adjustment
            return 1.0  # No adjustment for non-whitelisted tokens
        except Exception as e:
            logger.error(f"Failed to get risk adjustment: {e}")
            return 1.0

    async def add_token_to_whitelist(self, token_address: str, chain_id: int,
                                   symbol: str, name: str, category: WhitelistCategory,
                                   risk_adjustment: float = 0.1, notes: str = "",
                                   auto_validate: bool = True) -> bool:
        """Add a token to the whitelist."""
        try:
            entry_key = f"{chain_id}:{token_address.lower()}"

            # Check if already exists
            if entry_key in self.whitelist:
                logger.warning(f"Token {token_address} already in whitelist")
                return False

            # Validate risk adjustment
            if not (0.0 <= risk_adjustment <= 1.0):
                raise ValueError("Risk adjustment must be between 0.0 and 1.0")

            # Perform validation if requested
            validation_criteria = {}
            if auto_validate:
                validation_result = await self._validate_token_for_whitelist(token_address, chain_id)
                if not validation_result["eligible"]:
                    logger.warning(f"Token {token_address} failed whitelist validation: {validation_result['reason']}")
                    return False
                validation_criteria = validation_result["criteria"]

            # Create whitelist entry
            current_time = datetime.now(timezone.utc)
            entry = WhitelistEntry(
                token_address=token_address.lower(),
                chain_id=chain_id,
                symbol=symbol,
                name=name,
                category=category,
                status=WhitelistStatus.ACTIVE,
                risk_score_adjustment=risk_adjustment,
                added_date=current_time,
                last_validated=current_time,
                validation_criteria=validation_criteria,
                metadata={"source": "manual_addition"},
                notes=notes
            )

            # Add to whitelist
            with self._lock:
                self.whitelist[entry_key] = entry
                self.stats["total_whitelisted"] = len(self.whitelist)
                self.stats["active_entries"] = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.ACTIVE])

            # Log addition
            logger.info(f"Added token to whitelist: {symbol} ({token_address}) on chain {chain_id}")

            if self.audit_trail:
                self.audit_trail.log_event(
                    AuditEventType.SYSTEM_ACCESS,
                    None,
                    "token_whitelist",
                    "token_added",
                    {
                        "token_address": token_address,
                        "chain_id": chain_id,
                        "symbol": symbol,
                        "category": category.value,
                        "risk_adjustment": risk_adjustment
                    },
                    risk_level=RiskLevel.LOW
                )

            increment_counter("whitelist_tokens_added", 1, {"category": category.value})
            set_gauge("whitelist_total_tokens", len(self.whitelist))

            return True

        except Exception as e:
            logger.error(f"Failed to add token to whitelist: {e}")
            return False

    async def remove_token_from_whitelist(self, token_address: str, chain_id: int,
                                        reason: str = "") -> bool:
        """Remove a token from the whitelist."""
        try:
            entry_key = f"{chain_id}:{token_address.lower()}"

            with self._lock:
                entry = self.whitelist.get(entry_key)
                if not entry:
                    logger.warning(f"Token {token_address} not found in whitelist")
                    return False

                # Mark as deprecated instead of deleting
                entry.status = WhitelistStatus.DEPRECATED
                entry.metadata["removal_reason"] = reason
                entry.metadata["removed_date"] = datetime.now(timezone.utc).isoformat()

                self.stats["active_entries"] = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.ACTIVE])

            logger.info(f"Removed token from whitelist: {entry.symbol} ({token_address})")

            if self.audit_trail:
                self.audit_trail.log_event(
                    AuditEventType.SYSTEM_ACCESS,
                    None,
                    "token_whitelist",
                    "token_removed",
                    {
                        "token_address": token_address,
                        "chain_id": chain_id,
                        "symbol": entry.symbol,
                        "reason": reason
                    },
                    risk_level=RiskLevel.MEDIUM
                )

            increment_counter("whitelist_tokens_removed", 1)

            return True

        except Exception as e:
            logger.error(f"Failed to remove token from whitelist: {e}")
            return False

    async def _validate_token_for_whitelist(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Validate if a token meets whitelist criteria."""
        try:
            # Check cache first
            cache_key = f"validation:{chain_id}:{token_address.lower()}"
            cached_result = self.validation_cache.get(cache_key)
            if cached_result and (datetime.now(timezone.utc) - cached_result["timestamp"]).seconds < 3600:
                return cached_result["result"]

            validation_result = {
                "eligible": True,
                "reason": "",
                "criteria": {},
                "score": 0
            }

            # Simulate validation checks (in production, integrate with real data sources)
            criteria_checks = await self._perform_validation_checks(token_address, chain_id)

            # Market cap check
            market_cap = criteria_checks.get("market_cap", 0)
            if market_cap >= self.config.min_market_cap_usd:
                validation_result["criteria"][ValidationCriteria.MARKET_CAP] = market_cap
                validation_result["score"] += 25
            else:
                validation_result["eligible"] = False
                validation_result["reason"] = f"Market cap ${market_cap:,.0f} below minimum ${self.config.min_market_cap_usd:,.0f}"

            # Volume check
            daily_volume = criteria_checks.get("daily_volume", 0)
            if daily_volume >= self.config.min_daily_volume_usd:
                validation_result["criteria"][ValidationCriteria.TRADING_VOLUME] = daily_volume
                validation_result["score"] += 25
            else:
                validation_result["eligible"] = False
                validation_result["reason"] = f"Daily volume ${daily_volume:,.0f} below minimum ${self.config.min_daily_volume_usd:,.0f}"

            # Age check
            age_days = criteria_checks.get("age_days", 0)
            if age_days >= self.config.min_age_days:
                validation_result["criteria"][ValidationCriteria.AGE] = age_days
                validation_result["score"] += 25
            else:
                validation_result["eligible"] = False
                validation_result["reason"] = f"Token age {age_days} days below minimum {self.config.min_age_days} days"

            # Exchange listings check
            exchange_count = criteria_checks.get("exchange_listings", 0)
            if exchange_count >= self.config.min_exchange_listings:
                validation_result["criteria"][ValidationCriteria.EXCHANGE_LISTINGS] = exchange_count
                validation_result["score"] += 25
            else:
                validation_result["eligible"] = False
                validation_result["reason"] = f"Listed on {exchange_count} exchanges, minimum {self.config.min_exchange_listings}"

            # Cache result
            self.validation_cache[cache_key] = {
                "result": validation_result,
                "timestamp": datetime.now(timezone.utc)
            }

            return validation_result

        except Exception as e:
            logger.error(f"Failed to validate token for whitelist: {e}")
            return {"eligible": False, "reason": f"Validation error: {str(e)}", "criteria": {}, "score": 0}

    async def _perform_validation_checks(self, token_address: str, chain_id: int) -> Dict[str, Any]:
        """Perform actual validation checks against external data sources."""
        try:
            # In a production environment, this would integrate with:
            # - CoinGecko API for market data
            # - DeFiLlama for protocol data
            # - Exchange APIs for listing information
            # - Blockchain explorers for contract age

            # For demonstration, return simulated data
            # In practice, you'd make actual API calls here

            return {
                "market_cap": 5000000000,  # $5B
                "daily_volume": 100000000,  # $100M
                "age_days": 800,  # ~2 years
                "exchange_listings": 10,
                "liquidity": 50000000,  # $50M
                "audit_status": "audited",
                "community_size": 100000
            }

        except Exception as e:
            logger.error(f"Failed to perform validation checks: {e}")
            return {}

    async def validate_whitelist(self) -> Dict[str, Any]:
        """Validate all whitelisted tokens against current criteria."""
        try:
            validation_results = {
                "total_validated": 0,
                "passed": 0,
                "failed": 0,
                "suspended": 0,
                "failures": []
            }

            current_time = datetime.now(timezone.utc)

            for entry_key, entry in self.whitelist.items():
                if entry.status != WhitelistStatus.ACTIVE:
                    continue

                # Skip if recently validated
                if entry.auto_update and (current_time - entry.last_validated).hours < self.config.validation_interval_hours:
                    continue

                validation_results["total_validated"] += 1

                # Validate token
                result = await self._validate_token_for_whitelist(entry.token_address, entry.chain_id)

                if result["eligible"]:
                    # Update validation data
                    entry.last_validated = current_time
                    entry.validation_criteria = result["criteria"]
                    validation_results["passed"] += 1
                else:
                    # Suspend token that no longer meets criteria
                    entry.status = WhitelistStatus.SUSPENDED
                    entry.metadata["suspension_reason"] = result["reason"]
                    entry.metadata["suspended_date"] = current_time.isoformat()

                    validation_results["failed"] += 1
                    validation_results["suspended"] += 1
                    validation_results["failures"].append({
                        "token": f"{entry.symbol} ({entry.token_address})",
                        "chain_id": entry.chain_id,
                        "reason": result["reason"]
                    })

                    logger.warning(f"Suspended whitelisted token {entry.symbol}: {result['reason']}")

                    if self.audit_trail:
                        self.audit_trail.log_event(
                            AuditEventType.SECURITY_VIOLATION,
                            None,
                            "token_whitelist",
                            "token_suspended",
                            {
                                "token_address": entry.token_address,
                                "symbol": entry.symbol,
                                "reason": result["reason"]
                            },
                            risk_level=RiskLevel.MEDIUM
                        )

            # Update statistics
            with self._lock:
                self.stats["last_validation"] = current_time
                self.stats["validation_failures"] = validation_results["failed"]
                self.stats["active_entries"] = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.ACTIVE])

            logger.info("Whitelist validation completed", **validation_results)
            increment_counter("whitelist_validations_completed", 1)
            set_gauge("whitelist_active_tokens", self.stats["active_entries"])

            return validation_results

        except Exception as e:
            logger.error(f"Failed to validate whitelist: {e}")
            return {"error": str(e)}

    def apply_whitelist_risk_adjustment(self, token_address: str, chain_id: int,
                                      original_risk_score: float) -> Tuple[float, bool]:
        """Apply whitelist risk adjustment to a token's risk score."""
        try:
            entry = self.get_whitelist_entry(token_address, chain_id)

            if entry and entry.status == WhitelistStatus.ACTIVE:
                adjusted_score = original_risk_score * entry.risk_score_adjustment

                # Track adjustment
                with self._lock:
                    self.stats["risk_adjustments_applied"] += 1

                increment_counter("whitelist_risk_adjustments", 1, {
                    "category": entry.category.value,
                    "symbol": entry.symbol
                })

                logger.debug(f"Applied whitelist risk adjustment for {entry.symbol}: {original_risk_score:.3f} -> {adjusted_score:.3f}")

                return adjusted_score, True

            return original_risk_score, False

        except Exception as e:
            logger.error(f"Failed to apply whitelist risk adjustment: {e}")
            return original_risk_score, False

    def get_whitelist_by_category(self, category: WhitelistCategory) -> List[WhitelistEntry]:
        """Get all whitelisted tokens by category."""
        try:
            return [
                entry for entry in self.whitelist.values()
                if entry.category == category and entry.status == WhitelistStatus.ACTIVE
            ]
        except Exception as e:
            logger.error(f"Failed to get whitelist by category: {e}")
            return []

    def get_whitelist_by_chain(self, chain_id: int) -> List[WhitelistEntry]:
        """Get all whitelisted tokens for a specific chain."""
        try:
            return [
                entry for entry in self.whitelist.values()
                if entry.chain_id == chain_id and entry.status == WhitelistStatus.ACTIVE
            ]
        except Exception as e:
            logger.error(f"Failed to get whitelist by chain: {e}")
            return []

    def search_whitelist(self, query: str) -> List[WhitelistEntry]:
        """Search whitelist by symbol or name."""
        try:
            query_lower = query.lower()
            return [
                entry for entry in self.whitelist.values()
                if (query_lower in entry.symbol.lower() or
                    query_lower in entry.name.lower()) and
                entry.status == WhitelistStatus.ACTIVE
            ]
        except Exception as e:
            logger.error(f"Failed to search whitelist: {e}")
            return []

    def generate_whitelist_report(self) -> Dict[str, Any]:
        """Generate comprehensive whitelist report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Basic statistics
                total_entries = len(self.whitelist)
                active_entries = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.ACTIVE])
                suspended_entries = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.SUSPENDED])
                deprecated_entries = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.DEPRECATED])

                # Category distribution
                category_stats = defaultdict(int)
                chain_stats = defaultdict(int)

                for entry in self.whitelist.values():
                    if entry.status == WhitelistStatus.ACTIVE:
                        category_stats[entry.category.value] += 1
                        chain_stats[entry.chain_id] += 1

                # Risk adjustment statistics
                risk_adjustments = [entry.risk_score_adjustment for entry in self.whitelist.values()
                                  if entry.status == WhitelistStatus.ACTIVE]
                avg_risk_adjustment = sum(risk_adjustments) / len(risk_adjustments) if risk_adjustments else 0

                # Recent additions
                recent_additions = [
                    {
                        "symbol": entry.symbol,
                        "name": entry.name,
                        "chain_id": entry.chain_id,
                        "category": entry.category.value,
                        "added_date": entry.added_date.isoformat()
                    }
                    for entry in sorted(self.whitelist.values(), key=lambda x: x.added_date, reverse=True)[:10]
                    if entry.status == WhitelistStatus.ACTIVE
                ]

                # Validation status
                needs_validation = len([
                    entry for entry in self.whitelist.values()
                    if (entry.status == WhitelistStatus.ACTIVE and
                        (current_time - entry.last_validated).total_seconds() > self.config.validation_interval_hours * 3600)
                ])

                return {
                    "report_generated_at": current_time.isoformat(),
                    "summary": {
                        "total_entries": total_entries,
                        "active_entries": active_entries,
                        "suspended_entries": suspended_entries,
                        "deprecated_entries": deprecated_entries,
                        "needs_validation": needs_validation,
                        "avg_risk_adjustment": avg_risk_adjustment
                    },
                    "distribution": {
                        "by_category": dict(category_stats),
                        "by_chain": dict(chain_stats)
                    },
                    "recent_additions": recent_additions,
                    "configuration": {
                        "auto_validation_enabled": self.config.auto_validation_enabled,
                        "validation_interval_hours": self.config.validation_interval_hours,
                        "min_market_cap_usd": self.config.min_market_cap_usd,
                        "min_daily_volume_usd": self.config.min_daily_volume_usd,
                        "risk_score_reduction": self.config.risk_score_reduction
                    },
                    "statistics": self.stats.copy(),
                    "recommendations": self._generate_whitelist_recommendations()
                }

        except Exception as e:
            logger.error(f"Failed to generate whitelist report: {e}")
            return {"error": str(e)}

    def _generate_whitelist_recommendations(self) -> List[str]:
        """Generate whitelist management recommendations."""
        recommendations = []

        try:
            current_time = datetime.now(timezone.utc)

            # Check for tokens needing validation
            needs_validation = [
                entry for entry in self.whitelist.values()
                if (entry.status == WhitelistStatus.ACTIVE and
                    (current_time - entry.last_validated).total_seconds() > self.config.validation_interval_hours * 3600)
            ]

            if len(needs_validation) > 10:
                recommendations.append(f"{len(needs_validation)} tokens need validation - consider running validation")

            # Check for suspended tokens
            suspended_count = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.SUSPENDED])
            if suspended_count > 0:
                recommendations.append(f"{suspended_count} tokens are suspended - review and update or remove")

            # Check whitelist size
            active_count = len([e for e in self.whitelist.values() if e.status == WhitelistStatus.ACTIVE])
            if active_count > self.config.max_whitelist_size * 0.9:
                recommendations.append("Whitelist approaching size limit - consider removing deprecated entries")

            # Check for missing major tokens
            major_symbols = {"USDC", "USDT", "WETH", "WBTC", "DAI", "UNI", "LINK"}
            whitelisted_symbols = {entry.symbol for entry in self.whitelist.values() if entry.status == WhitelistStatus.ACTIVE}
            missing_major = major_symbols - whitelisted_symbols

            if missing_major:
                recommendations.append(f"Consider adding major tokens: {', '.join(missing_major)}")

            if not recommendations:
                recommendations.append("Whitelist management is operating optimally")

        except Exception as e:
            logger.error(f"Failed to generate whitelist recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations

    async def start_automatic_validation(self):
        """Start automatic whitelist validation loop."""
        logger.info("Starting automatic whitelist validation")

        while True:
            try:
                if self.config.auto_validation_enabled:
                    await self.validate_whitelist()

                # Wait for next validation cycle
                await asyncio.sleep(self.config.validation_interval_hours * 3600)

            except Exception as e:
                logger.error(f"Error in automatic validation: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retry


# Global instance
token_whitelist_manager = TokenWhitelistManager()
