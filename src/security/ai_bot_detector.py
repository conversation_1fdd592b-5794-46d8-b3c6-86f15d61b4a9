"""
AI-Powered Bot Detection Enhancement System

This module implements advanced AI-based pattern recognition to detect and mitigate
bot attacks, addressing the 137% spike in AI-powered bot fraud attacks in 2025.

Features:
- Machine learning-based behavioral analysis
- Advanced fingerprinting and device profiling
- Real-time anomaly detection with adaptive thresholds
- Multi-dimensional bot classification (scraper, fraud, DDoS, etc.)
- Sophisticated evasion technique detection
- Automated response and mitigation strategies
"""

import asyncio
import hashlib
import json
import logging
import math
import re
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import threading
import statistics

import structlog
from fastapi import Request, Response

from ..core.config import config
from ..core.logging_config import get_logger
from ..compliance.audit_framework import AuditTrail, AuditEventType, RiskLevel
from ..monitoring.dashboard import metrics_collector, increment_counter, set_gauge
from .threshold_calibration import threshold_calibrator, ThresholdType

logger = get_logger(__name__)


class BotType(Enum):
    """Types of bot behavior detected."""
    LEGITIMATE = "legitimate"
    SCRAPER = "scraper"
    FRAUD = "fraud"
    DDOS = "ddos"
    SPAM = "spam"
    CREDENTIAL_STUFFING = "credential_stuffing"
    API_ABUSE = "api_abuse"
    PRICE_MANIPULATION = "price_manipulation"
    UNKNOWN = "unknown"


class BotConfidence(Enum):
    """Confidence levels for bot detection."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class EvasionTechnique(Enum):
    """Bot evasion techniques detected."""
    USER_AGENT_ROTATION = "user_agent_rotation"
    IP_ROTATION = "ip_rotation"
    TIMING_RANDOMIZATION = "timing_randomization"
    HEADER_SPOOFING = "header_spoofing"
    CAPTCHA_SOLVING = "captcha_solving"
    BROWSER_AUTOMATION = "browser_automation"
    RESIDENTIAL_PROXY = "residential_proxy"
    HUMAN_MIMICRY = "human_mimicry"


@dataclass
class RequestFingerprint:
    """Comprehensive request fingerprint for bot detection."""
    ip_address: str
    user_agent: str
    headers_hash: str
    tls_fingerprint: Optional[str]
    timing_pattern: List[float]
    request_sequence: List[str]
    payload_patterns: List[str]
    behavioral_score: float = 0.0
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class BotDetectionResult:
    """Result of bot detection analysis."""
    is_bot: bool
    bot_type: BotType
    confidence: BotConfidence
    risk_score: float
    evasion_techniques: List[EvasionTechnique]
    behavioral_anomalies: List[str]
    fingerprint: RequestFingerprint
    mitigation_actions: List[str]
    details: Dict[str, Any]


@dataclass
class BotAlert:
    """Bot detection security alert."""
    alert_id: str
    ip_address: str
    user_agent: str
    bot_type: BotType
    confidence: BotConfidence
    risk_score: float
    evasion_techniques: List[EvasionTechnique]
    timestamp: datetime
    details: Dict[str, Any]
    mitigation_applied: List[str]


class AIBotDetector:
    """
    Advanced AI-powered bot detection system.
    
    Uses machine learning and behavioral analysis to detect sophisticated
    bot attacks with high accuracy and low false positive rates.
    """
    
    def __init__(self, audit_trail: Optional[AuditTrail] = None):
        self.audit_trail = audit_trail
        self.request_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.fingerprint_cache: Dict[str, RequestFingerprint] = {}
        self.bot_patterns: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.alerts: List[BotAlert] = []
        self._lock = threading.RLock()
        
        # ML model parameters
        self.behavioral_features = self._initialize_behavioral_features()
        self.anomaly_thresholds = self._initialize_anomaly_thresholds()
        self.evasion_detectors = self._initialize_evasion_detectors()
        
        # Learning parameters
        self.learning_window_hours = 24
        self.min_requests_for_analysis = 10
        self.false_positive_threshold = 0.05
        
        # Known bot signatures
        self.known_bot_signatures = self._initialize_bot_signatures()
        
        logger.info("AIBotDetector initialized")
    
    def _initialize_behavioral_features(self) -> Dict[str, Dict[str, Any]]:
        """Initialize behavioral feature extractors."""
        return {
            "timing_patterns": {
                "request_intervals": {"weight": 0.3, "threshold": 0.7},
                "burst_patterns": {"weight": 0.2, "threshold": 0.8},
                "regularity_score": {"weight": 0.25, "threshold": 0.75}
            },
            "navigation_patterns": {
                "path_sequence_entropy": {"weight": 0.2, "threshold": 0.6},
                "depth_first_traversal": {"weight": 0.15, "threshold": 0.8},
                "breadth_first_traversal": {"weight": 0.15, "threshold": 0.8}
            },
            "interaction_patterns": {
                "mouse_movement_simulation": {"weight": 0.1, "threshold": 0.9},
                "keyboard_timing": {"weight": 0.1, "threshold": 0.85},
                "scroll_behavior": {"weight": 0.05, "threshold": 0.9}
            },
            "content_patterns": {
                "payload_similarity": {"weight": 0.2, "threshold": 0.8},
                "parameter_enumeration": {"weight": 0.25, "threshold": 0.7},
                "data_extraction_patterns": {"weight": 0.3, "threshold": 0.75}
            }
        }
    
    def _initialize_anomaly_thresholds(self) -> Dict[str, float]:
        """Initialize anomaly detection thresholds."""
        return {
            "request_frequency": 0.8,
            "timing_regularity": 0.75,
            "header_consistency": 0.7,
            "behavioral_deviation": 0.8,
            "evasion_score": 0.6
        }
    
    def _initialize_evasion_detectors(self) -> Dict[EvasionTechnique, Dict[str, Any]]:
        """Initialize evasion technique detectors."""
        return {
            EvasionTechnique.USER_AGENT_ROTATION: {
                "pattern": r"Mozilla/\d+\.\d+.*",
                "variance_threshold": 0.8,
                "detection_window": 3600  # 1 hour
            },
            EvasionTechnique.IP_ROTATION: {
                "subnet_variance_threshold": 0.7,
                "geolocation_variance_threshold": 0.6,
                "detection_window": 1800  # 30 minutes
            },
            EvasionTechnique.TIMING_RANDOMIZATION: {
                "jitter_threshold": 0.3,
                "pattern_break_threshold": 0.5,
                "detection_window": 900  # 15 minutes
            },
            EvasionTechnique.HEADER_SPOOFING: {
                "inconsistency_threshold": 0.6,
                "entropy_threshold": 0.8,
                "detection_window": 1800
            },
            EvasionTechnique.BROWSER_AUTOMATION: {
                "automation_indicators": [
                    "webdriver", "selenium", "phantomjs", "headless",
                    "automation", "bot", "crawler", "spider"
                ],
                "detection_threshold": 0.7
            },
            EvasionTechnique.HUMAN_MIMICRY: {
                "mimicry_patterns": [
                    "perfect_timing", "identical_sequences", "repeated_patterns"
                ],
                "detection_threshold": 0.8
            }
        }
    
    def _initialize_bot_signatures(self) -> Dict[str, Dict[str, Any]]:
        """Initialize known bot signatures and patterns."""
        return {
            "crypto_price_bots": {
                "patterns": [
                    r"/api/.*price.*",
                    r"/api/.*market.*",
                    r"/api/.*ticker.*"
                ],
                "frequency_threshold": 100,  # requests per minute
                "bot_type": BotType.SCRAPER
            },
            "arbitrage_bots": {
                "patterns": [
                    r"/api/.*exchange.*",
                    r"/api/.*orderbook.*",
                    r"/api/.*liquidity.*"
                ],
                "frequency_threshold": 200,
                "bot_type": BotType.PRICE_MANIPULATION
            },
            "data_scrapers": {
                "patterns": [
                    r"/api/.*list.*",
                    r"/api/.*search.*",
                    r"/api/.*discover.*"
                ],
                "frequency_threshold": 50,
                "bot_type": BotType.SCRAPER
            },
            "fraud_bots": {
                "patterns": [
                    r"/api/.*analyze.*",
                    r"/api/.*validate.*",
                    r"/api/.*verify.*"
                ],
                "frequency_threshold": 30,
                "bot_type": BotType.FRAUD,
                "payload_indicators": [
                    "bulk_requests", "parameter_fuzzing", "injection_attempts"
                ]
            }
        }
    
    async def analyze_request(self, request: Request, response: Response,
                            request_data: Optional[Dict[str, Any]] = None,
                            response_data: Optional[Dict[str, Any]] = None) -> BotDetectionResult:
        """
        Analyze request for bot behavior using AI-powered detection.
        
        Args:
            request: FastAPI request object
            response: FastAPI response object
            request_data: Parsed request data
            response_data: Parsed response data
            
        Returns:
            BotDetectionResult with detection analysis
        """
        try:
            # Extract request fingerprint
            fingerprint = await self._extract_request_fingerprint(request, request_data)
            
            # Analyze behavioral patterns
            behavioral_score = await self._analyze_behavioral_patterns(fingerprint)
            
            # Detect evasion techniques
            evasion_techniques = await self._detect_evasion_techniques(fingerprint)
            
            # Check against known bot signatures
            signature_match = await self._check_bot_signatures(request, request_data)
            
            # Calculate overall bot probability
            bot_probability = await self._calculate_bot_probability(
                fingerprint, behavioral_score, evasion_techniques, signature_match
            )

            # Get dynamic threshold for bot detection
            request_id = f"{fingerprint.ip_address}_{fingerprint.timestamp.timestamp()}"
            bot_threshold = threshold_calibrator.get_threshold_value(ThresholdType.BOT_DETECTION, request_id)

            # Determine bot type and confidence
            bot_type, confidence = await self._classify_bot_behavior(
                fingerprint, signature_match, evasion_techniques
            )
            
            # Generate detection result using dynamic threshold
            result = BotDetectionResult(
                is_bot=bot_probability > bot_threshold,
                bot_type=bot_type,
                confidence=confidence,
                risk_score=bot_probability,
                evasion_techniques=evasion_techniques,
                behavioral_anomalies=await self._identify_behavioral_anomalies(fingerprint),
                fingerprint=fingerprint,
                mitigation_actions=await self._recommend_mitigation_actions(bot_probability, bot_type),
                details={
                    "behavioral_score": behavioral_score,
                    "signature_matches": signature_match,
                    "bot_threshold_used": bot_threshold,
                    "analysis_timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

            # Record experiment result if part of A/B test
            await self._record_threshold_experiment_result(request_id, result, bot_probability, bot_threshold)
            
            # Store analysis results
            await self._store_analysis_results(result)
            
            # Generate alert if bot detected
            if result.is_bot and result.confidence in [BotConfidence.HIGH, BotConfidence.CRITICAL]:
                await self._generate_bot_alert(result)
            
            # Update metrics
            increment_counter("bot_detection_analyses", 1)
            if result.is_bot:
                increment_counter("bots_detected", 1, {"type": result.bot_type.value})
                set_gauge("bot_detection_risk_score", result.risk_score)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze request for bot behavior: {e}")
            increment_counter("bot_detection_errors", 1)
            
            # Return safe default result
            return BotDetectionResult(
                is_bot=False,
                bot_type=BotType.UNKNOWN,
                confidence=BotConfidence.LOW,
                risk_score=0.0,
                evasion_techniques=[],
                behavioral_anomalies=[],
                fingerprint=RequestFingerprint(
                    ip_address=request.client.host if request.client else "unknown",
                    user_agent=request.headers.get("user-agent", "unknown"),
                    headers_hash="unknown",
                    tls_fingerprint=None,
                    timing_pattern=[],
                    request_sequence=[],
                    payload_patterns=[]
                ),
                mitigation_actions=[],
                details={"error": str(e)}
            )

    async def _extract_request_fingerprint(self, request: Request,
                                         request_data: Optional[Dict[str, Any]]) -> RequestFingerprint:
        """Extract comprehensive request fingerprint for analysis."""
        try:
            ip_address = request.client.host if request.client else "unknown"
            user_agent = request.headers.get("user-agent", "")

            # Create headers hash (excluding dynamic headers)
            static_headers = {
                k: v for k, v in request.headers.items()
                if k.lower() not in ["date", "timestamp", "x-request-id", "x-trace-id"]
            }
            headers_hash = hashlib.md5(json.dumps(static_headers, sort_keys=True).encode()).hexdigest()

            # Extract timing patterns from request history
            timing_pattern = self._extract_timing_pattern(ip_address)

            # Extract request sequence patterns
            request_sequence = self._extract_request_sequence(ip_address, str(request.url.path))

            # Extract payload patterns
            payload_patterns = self._extract_payload_patterns(request_data) if request_data else []

            # Record request timestamp
            current_time = time.time()
            with self._lock:
                self.request_history[ip_address].append(current_time)

            return RequestFingerprint(
                ip_address=ip_address,
                user_agent=user_agent,
                headers_hash=headers_hash,
                tls_fingerprint=self._extract_tls_fingerprint(request),
                timing_pattern=timing_pattern,
                request_sequence=request_sequence,
                payload_patterns=payload_patterns,
                behavioral_score=0.0  # Will be calculated later
            )

        except Exception as e:
            logger.error(f"Failed to extract request fingerprint: {e}")
            return RequestFingerprint(
                ip_address="unknown",
                user_agent="unknown",
                headers_hash="unknown",
                tls_fingerprint=None,
                timing_pattern=[],
                request_sequence=[],
                payload_patterns=[]
            )

    def _extract_timing_pattern(self, ip_address: str) -> List[float]:
        """Extract timing patterns from request history."""
        try:
            with self._lock:
                history = list(self.request_history[ip_address])

            if len(history) < 2:
                return []

            # Calculate intervals between requests
            intervals = []
            for i in range(1, len(history)):
                interval = history[i] - history[i-1]
                intervals.append(interval)

            return intervals[-20:]  # Keep last 20 intervals

        except Exception as e:
            logger.error(f"Failed to extract timing pattern: {e}")
            return []

    def _extract_request_sequence(self, ip_address: str, current_path: str) -> List[str]:
        """Extract request sequence patterns."""
        try:
            # Get recent request paths for this IP
            patterns = self.bot_patterns[ip_address]
            path_sequence = patterns.get("path_sequence", [])

            # Add current path
            path_sequence.append(current_path)

            # Keep only recent paths
            if len(path_sequence) > 50:
                path_sequence = path_sequence[-50:]

            # Update patterns
            patterns["path_sequence"] = path_sequence

            return path_sequence

        except Exception as e:
            logger.error(f"Failed to extract request sequence: {e}")
            return []

    def _extract_payload_patterns(self, request_data: Dict[str, Any]) -> List[str]:
        """Extract patterns from request payload."""
        try:
            patterns = []

            if not request_data:
                return patterns

            # Check for parameter enumeration
            if isinstance(request_data, dict):
                param_count = len(request_data)
                if param_count > 20:
                    patterns.append("excessive_parameters")

                # Check for injection patterns
                for key, value in request_data.items():
                    if isinstance(value, str):
                        if re.search(r"['\";]|union|select|drop|insert", value, re.IGNORECASE):
                            patterns.append("injection_attempt")
                        if len(value) > 1000:
                            patterns.append("oversized_payload")

            # Check for bulk operations
            if isinstance(request_data, list) and len(request_data) > 100:
                patterns.append("bulk_operation")

            return patterns

        except Exception as e:
            logger.error(f"Failed to extract payload patterns: {e}")
            return []

    def _extract_tls_fingerprint(self, request: Request) -> Optional[str]:
        """Extract TLS fingerprint if available."""
        try:
            # In a production environment, you would extract actual TLS fingerprint
            # For now, we'll use a simplified approach based on headers
            tls_headers = {
                "x-forwarded-proto": request.headers.get("x-forwarded-proto"),
                "x-forwarded-ssl": request.headers.get("x-forwarded-ssl"),
                "x-ssl-cipher": request.headers.get("x-ssl-cipher")
            }

            if any(tls_headers.values()):
                return hashlib.md5(json.dumps(tls_headers, sort_keys=True).encode()).hexdigest()

            return None

        except Exception as e:
            logger.error(f"Failed to extract TLS fingerprint: {e}")
            return None

    async def _analyze_behavioral_patterns(self, fingerprint: RequestFingerprint) -> float:
        """Analyze behavioral patterns using ML-based approach."""
        try:
            behavioral_scores = []

            # Analyze timing patterns
            timing_score = self._analyze_timing_patterns(fingerprint.timing_pattern)
            behavioral_scores.append(timing_score * 0.4)  # High weight for timing

            # Analyze navigation patterns
            navigation_score = self._analyze_navigation_patterns(fingerprint.request_sequence)
            behavioral_scores.append(navigation_score * 0.3)

            # Analyze content patterns
            content_score = self._analyze_content_patterns(fingerprint.payload_patterns)
            behavioral_scores.append(content_score * 0.3)

            # Calculate weighted average
            if behavioral_scores:
                return sum(behavioral_scores)
            else:
                return 0.0

        except Exception as e:
            logger.error(f"Failed to analyze behavioral patterns: {e}")
            return 0.0

    def _analyze_timing_patterns(self, timing_pattern: List[float]) -> float:
        """Analyze timing patterns for bot-like behavior."""
        try:
            if len(timing_pattern) < 3:
                return 0.0

            # Calculate timing regularity
            if len(timing_pattern) > 1:
                variance = statistics.variance(timing_pattern)
                mean_interval = statistics.mean(timing_pattern)

                # High regularity (low variance) indicates bot behavior
                if mean_interval > 0:
                    regularity_score = 1.0 - min(1.0, variance / (mean_interval ** 2))
                else:
                    regularity_score = 0.0
            else:
                regularity_score = 0.0

            # Check for burst patterns
            burst_score = 0.0
            if len(timing_pattern) >= 5:
                # Look for rapid bursts followed by pauses
                short_intervals = sum(1 for interval in timing_pattern if interval < 1.0)
                burst_ratio = short_intervals / len(timing_pattern)
                if burst_ratio > 0.7:  # More than 70% rapid requests
                    burst_score = 0.8

            # Combine scores
            return max(regularity_score, burst_score)

        except Exception as e:
            logger.error(f"Failed to analyze timing patterns: {e}")
            return 0.0

    def _analyze_navigation_patterns(self, request_sequence: List[str]) -> float:
        """Analyze navigation patterns for bot-like behavior."""
        try:
            if len(request_sequence) < 5:
                return 0.0

            # Calculate path entropy
            path_counts = defaultdict(int)
            for path in request_sequence:
                path_counts[path] += 1

            total_requests = len(request_sequence)
            entropy = 0.0
            for count in path_counts.values():
                probability = count / total_requests
                if probability > 0:
                    entropy -= probability * math.log2(probability)

            # Normalize entropy (lower entropy = more bot-like)
            max_entropy = math.log2(len(path_counts)) if len(path_counts) > 1 else 1
            normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0

            # Bot-like behavior has low entropy (repetitive patterns)
            bot_score = 1.0 - normalized_entropy

            # Check for systematic traversal patterns
            systematic_score = self._detect_systematic_traversal(request_sequence)

            return max(bot_score, systematic_score)

        except Exception as e:
            logger.error(f"Failed to analyze navigation patterns: {e}")
            return 0.0

    def _detect_systematic_traversal(self, request_sequence: List[str]) -> float:
        """Detect systematic traversal patterns."""
        try:
            if len(request_sequence) < 10:
                return 0.0

            # Look for sequential patterns (e.g., /api/token/1, /api/token/2, etc.)
            sequential_patterns = 0
            for i in range(len(request_sequence) - 1):
                current = request_sequence[i]
                next_req = request_sequence[i + 1]

                # Extract numeric parts
                current_nums = re.findall(r'\d+', current)
                next_nums = re.findall(r'\d+', next_req)

                if current_nums and next_nums:
                    # Check if numbers are sequential
                    try:
                        if int(next_nums[-1]) == int(current_nums[-1]) + 1:
                            sequential_patterns += 1
                    except (ValueError, IndexError):
                        pass

            # Calculate sequential ratio
            sequential_ratio = sequential_patterns / (len(request_sequence) - 1)

            # High sequential ratio indicates bot behavior
            return min(1.0, sequential_ratio * 2)  # Scale up for detection

        except Exception as e:
            logger.error(f"Failed to detect systematic traversal: {e}")
            return 0.0

    def _analyze_content_patterns(self, payload_patterns: List[str]) -> float:
        """Analyze content patterns for bot-like behavior."""
        try:
            if not payload_patterns:
                return 0.0

            bot_indicators = {
                "excessive_parameters": 0.8,
                "injection_attempt": 0.9,
                "oversized_payload": 0.6,
                "bulk_operation": 0.7,
                "parameter_enumeration": 0.8
            }

            max_score = 0.0
            for pattern in payload_patterns:
                if pattern in bot_indicators:
                    max_score = max(max_score, bot_indicators[pattern])

            return max_score

        except Exception as e:
            logger.error(f"Failed to analyze content patterns: {e}")
            return 0.0

    async def _detect_evasion_techniques(self, fingerprint: RequestFingerprint) -> List[EvasionTechnique]:
        """Detect bot evasion techniques."""
        detected_techniques = []

        try:
            ip_address = fingerprint.ip_address

            # Check for user agent rotation
            if await self._detect_user_agent_rotation(ip_address, fingerprint.user_agent):
                detected_techniques.append(EvasionTechnique.USER_AGENT_ROTATION)

            # Check for timing randomization
            if self._detect_timing_randomization(fingerprint.timing_pattern):
                detected_techniques.append(EvasionTechnique.TIMING_RANDOMIZATION)

            # Check for header spoofing
            if self._detect_header_spoofing(fingerprint):
                detected_techniques.append(EvasionTechnique.HEADER_SPOOFING)

            # Check for browser automation indicators
            if self._detect_browser_automation(fingerprint.user_agent):
                detected_techniques.append(EvasionTechnique.BROWSER_AUTOMATION)

            # Check for human mimicry patterns
            if self._detect_human_mimicry(fingerprint):
                detected_techniques.append(EvasionTechnique.HUMAN_MIMICRY)

        except Exception as e:
            logger.error(f"Failed to detect evasion techniques: {e}")

        return detected_techniques

    async def _detect_user_agent_rotation(self, ip_address: str, user_agent: str) -> bool:
        """Detect user agent rotation patterns."""
        try:
            patterns = self.bot_patterns[ip_address]
            user_agents = patterns.setdefault("user_agents", set())
            user_agents.add(user_agent)

            # If too many different user agents from same IP, likely rotation
            return len(user_agents) > 5

        except Exception as e:
            logger.error(f"Failed to detect user agent rotation: {e}")
            return False

    def _detect_timing_randomization(self, timing_pattern: List[float]) -> bool:
        """Detect artificial timing randomization."""
        try:
            if len(timing_pattern) < 10:
                return False

            # Check for artificial jitter patterns
            variance = statistics.variance(timing_pattern)
            mean_interval = statistics.mean(timing_pattern)

            if mean_interval > 0:
                coefficient_of_variation = math.sqrt(variance) / mean_interval
                # Artificial randomization often has specific CV ranges
                return 0.3 < coefficient_of_variation < 0.7

            return False

        except Exception as e:
            logger.error(f"Failed to detect timing randomization: {e}")
            return False

    def _detect_header_spoofing(self, fingerprint: RequestFingerprint) -> bool:
        """Detect header spoofing patterns."""
        try:
            # Check for inconsistent header combinations
            user_agent = fingerprint.user_agent.lower()

            # Common spoofing patterns
            spoofing_indicators = [
                "headless" in user_agent,
                "phantom" in user_agent,
                "selenium" in user_agent,
                len(user_agent) > 500,  # Unusually long user agent
                user_agent.count("mozilla") > 1  # Duplicate Mozilla strings
            ]

            return any(spoofing_indicators)

        except Exception as e:
            logger.error(f"Failed to detect header spoofing: {e}")
            return False

    def _detect_browser_automation(self, user_agent: str) -> bool:
        """Detect browser automation tools."""
        try:
            automation_indicators = [
                "webdriver", "selenium", "phantomjs", "headless",
                "automation", "bot", "crawler", "spider", "scraper"
            ]

            user_agent_lower = user_agent.lower()
            return any(indicator in user_agent_lower for indicator in automation_indicators)

        except Exception as e:
            logger.error(f"Failed to detect browser automation: {e}")
            return False

    def _detect_human_mimicry(self, fingerprint: RequestFingerprint) -> bool:
        """Detect attempts to mimic human behavior."""
        try:
            # Look for too-perfect human-like patterns
            timing_pattern = fingerprint.timing_pattern

            if len(timing_pattern) < 5:
                return False

            # Check for suspiciously consistent "random" intervals
            if len(set(timing_pattern)) == len(timing_pattern):  # All unique intervals
                # Check if intervals follow mathematical patterns
                sorted_intervals = sorted(timing_pattern)
                differences = [sorted_intervals[i+1] - sorted_intervals[i] for i in range(len(sorted_intervals)-1)]

                # If differences are too regular, it's likely artificial
                if len(set(differences)) <= 2:
                    return True

            return False

        except Exception as e:
            logger.error(f"Failed to detect human mimicry: {e}")
            return False

    async def _check_bot_signatures(self, request: Request,
                                  request_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Check against known bot signatures."""
        try:
            matches = {}
            request_path = str(request.url.path)

            for signature_name, signature_config in self.known_bot_signatures.items():
                # Check path patterns
                for pattern in signature_config["patterns"]:
                    if re.search(pattern, request_path, re.IGNORECASE):
                        matches[signature_name] = {
                            "pattern_matched": pattern,
                            "bot_type": signature_config["bot_type"],
                            "confidence": 0.8
                        }
                        break

            return matches

        except Exception as e:
            logger.error(f"Failed to check bot signatures: {e}")
            return {}

    async def _calculate_bot_probability(self, fingerprint: RequestFingerprint,
                                       behavioral_score: float,
                                       evasion_techniques: List[EvasionTechnique],
                                       signature_matches: Dict[str, Any]) -> float:
        """Calculate overall bot probability using ensemble approach."""
        try:
            scores = []

            # Behavioral analysis score
            scores.append(behavioral_score * 0.4)

            # Evasion techniques score
            evasion_score = min(1.0, len(evasion_techniques) * 0.3)
            scores.append(evasion_score * 0.3)

            # Signature matching score
            signature_score = 0.8 if signature_matches else 0.0
            scores.append(signature_score * 0.3)

            # Calculate weighted average
            return sum(scores)

        except Exception as e:
            logger.error(f"Failed to calculate bot probability: {e}")
            return 0.0

    async def _classify_bot_behavior(self, fingerprint: RequestFingerprint,
                                   signature_matches: Dict[str, Any],
                                   evasion_techniques: List[EvasionTechnique]) -> Tuple[BotType, BotConfidence]:
        """Classify bot type and confidence level."""
        try:
            # Check signature matches first
            if signature_matches:
                for match_info in signature_matches.values():
                    return match_info["bot_type"], BotConfidence.HIGH

            # Classify based on evasion techniques
            if EvasionTechnique.BROWSER_AUTOMATION in evasion_techniques:
                return BotType.SCRAPER, BotConfidence.HIGH

            if len(evasion_techniques) >= 3:
                return BotType.FRAUD, BotConfidence.HIGH

            if len(evasion_techniques) >= 1:
                return BotType.SCRAPER, BotConfidence.MEDIUM

            # Default classification
            return BotType.UNKNOWN, BotConfidence.LOW

        except Exception as e:
            logger.error(f"Failed to classify bot behavior: {e}")
            return BotType.UNKNOWN, BotConfidence.LOW

    async def _identify_behavioral_anomalies(self, fingerprint: RequestFingerprint) -> List[str]:
        """Identify specific behavioral anomalies."""
        anomalies = []

        try:
            # Check timing anomalies
            if len(fingerprint.timing_pattern) > 5:
                variance = statistics.variance(fingerprint.timing_pattern)
                if variance < 0.1:
                    anomalies.append("extremely_regular_timing")
                elif variance > 100:
                    anomalies.append("highly_irregular_timing")

            # Check request sequence anomalies
            if len(fingerprint.request_sequence) > 10:
                unique_paths = len(set(fingerprint.request_sequence))
                if unique_paths / len(fingerprint.request_sequence) < 0.3:
                    anomalies.append("repetitive_path_access")

            # Check payload anomalies
            if "injection_attempt" in fingerprint.payload_patterns:
                anomalies.append("malicious_payload_detected")

            if "bulk_operation" in fingerprint.payload_patterns:
                anomalies.append("bulk_data_operation")

        except Exception as e:
            logger.error(f"Failed to identify behavioral anomalies: {e}")

        return anomalies

    async def _recommend_mitigation_actions(self, bot_probability: float,
                                          bot_type: BotType) -> List[str]:
        """Recommend mitigation actions based on bot detection."""
        actions = []

        try:
            if bot_probability > 0.9:
                actions.append("block_ip_immediately")
                actions.append("require_captcha")
                actions.append("alert_security_team")
            elif bot_probability > 0.7:
                actions.append("rate_limit_aggressive")
                actions.append("require_additional_verification")
                actions.append("monitor_closely")
            elif bot_probability > 0.5:
                actions.append("rate_limit_moderate")
                actions.append("log_detailed_activity")

            # Bot-type specific actions
            if bot_type == BotType.FRAUD:
                actions.append("freeze_account_temporarily")
                actions.append("require_manual_review")
            elif bot_type == BotType.DDOS:
                actions.append("activate_ddos_protection")
                actions.append("scale_infrastructure")
            elif bot_type == BotType.SCRAPER:
                actions.append("serve_limited_data")
                actions.append("implement_honeypot")

        except Exception as e:
            logger.error(f"Failed to recommend mitigation actions: {e}")

        return actions

    async def _store_analysis_results(self, result: BotDetectionResult):
        """Store bot detection analysis results."""
        try:
            # Update fingerprint cache
            self.fingerprint_cache[result.fingerprint.ip_address] = result.fingerprint

            # Update bot patterns
            ip_address = result.fingerprint.ip_address
            patterns = self.bot_patterns[ip_address]
            patterns["last_analysis"] = result
            patterns["bot_probability_history"] = patterns.get("bot_probability_history", [])
            patterns["bot_probability_history"].append(result.risk_score)

            # Keep only recent history
            if len(patterns["bot_probability_history"]) > 100:
                patterns["bot_probability_history"] = patterns["bot_probability_history"][-100:]

        except Exception as e:
            logger.error(f"Failed to store analysis results: {e}")

    async def _generate_bot_alert(self, result: BotDetectionResult):
        """Generate bot detection alert."""
        try:
            alert = BotAlert(
                alert_id=hashlib.md5(f"{result.fingerprint.ip_address}_{result.fingerprint.timestamp}".encode()).hexdigest()[:16],
                ip_address=result.fingerprint.ip_address,
                user_agent=result.fingerprint.user_agent,
                bot_type=result.bot_type,
                confidence=result.confidence,
                risk_score=result.risk_score,
                evasion_techniques=result.evasion_techniques,
                timestamp=result.fingerprint.timestamp,
                details=result.details,
                mitigation_applied=result.mitigation_actions
            )

            # Store alert
            with self._lock:
                self.alerts.append(alert)

                # Keep only recent alerts
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
                self.alerts = [a for a in self.alerts if a.timestamp > cutoff_time]

            # Log alert
            logger.warning("Bot detected",
                          alert_id=alert.alert_id,
                          ip_address=alert.ip_address,
                          bot_type=alert.bot_type.value,
                          confidence=alert.confidence.value,
                          risk_score=alert.risk_score)

            # Log to audit trail
            if self.audit_trail:
                risk_level = RiskLevel.HIGH if alert.confidence in [BotConfidence.HIGH, BotConfidence.CRITICAL] else RiskLevel.MEDIUM
                self.audit_trail.log_event(
                    AuditEventType.SECURITY_VIOLATION,
                    None,
                    "ai_bot_detector",
                    "bot_detected",
                    {
                        "alert_id": alert.alert_id,
                        "ip_address": alert.ip_address,
                        "bot_type": alert.bot_type.value,
                        "confidence": alert.confidence.value,
                        "risk_score": alert.risk_score,
                        "evasion_techniques": [t.value for t in alert.evasion_techniques]
                    },
                    risk_level=risk_level
                )

        except Exception as e:
            logger.error(f"Failed to generate bot alert: {e}")

    def generate_bot_report(self) -> Dict[str, Any]:
        """Generate comprehensive bot detection report."""
        try:
            with self._lock:
                current_time = datetime.now(timezone.utc)

                # Basic statistics
                total_alerts = len(self.alerts)
                total_ips_monitored = len(self.bot_patterns)

                # Bot type distribution
                bot_type_stats = defaultdict(int)
                confidence_stats = defaultdict(int)
                evasion_stats = defaultdict(int)

                for alert in self.alerts:
                    bot_type_stats[alert.bot_type.value] += 1
                    confidence_stats[alert.confidence.value] += 1
                    for technique in alert.evasion_techniques:
                        evasion_stats[technique.value] += 1

                # Top offending IPs
                ip_alert_counts = defaultdict(int)
                for alert in self.alerts:
                    ip_alert_counts[alert.ip_address] += 1

                top_ips = sorted(ip_alert_counts.items(), key=lambda x: x[1], reverse=True)[:10]

                return {
                    "report_generated_at": current_time.isoformat(),
                    "summary": {
                        "total_bot_alerts_24h": total_alerts,
                        "unique_ips_monitored": total_ips_monitored,
                        "high_confidence_detections": confidence_stats.get("high", 0) + confidence_stats.get("critical", 0),
                        "most_common_bot_type": max(bot_type_stats.items(), key=lambda x: x[1])[0] if bot_type_stats else "none"
                    },
                    "detection_distribution": {
                        "by_bot_type": dict(bot_type_stats),
                        "by_confidence": dict(confidence_stats),
                        "by_evasion_technique": dict(evasion_stats)
                    },
                    "top_offending_ips": [{"ip": ip, "alert_count": count} for ip, count in top_ips],
                    "security_recommendations": self._generate_bot_security_recommendations(bot_type_stats, evasion_stats)
                }

        except Exception as e:
            logger.error(f"Failed to generate bot report: {e}")
            return {"error": str(e)}

    def _generate_bot_security_recommendations(self, bot_type_stats: Dict[str, int],
                                             evasion_stats: Dict[str, int]) -> List[str]:
        """Generate security recommendations based on bot analysis."""
        recommendations = []

        try:
            # Check for high bot activity
            total_bots = sum(bot_type_stats.values())
            if total_bots > 100:
                recommendations.append("High bot activity detected - implement advanced rate limiting")

            # Bot-type specific recommendations
            if bot_type_stats.get("fraud", 0) > 10:
                recommendations.append("CRITICAL: Multiple fraud bots detected - review authentication mechanisms")

            if bot_type_stats.get("ddos", 0) > 5:
                recommendations.append("DDoS bot activity detected - activate DDoS protection")

            if bot_type_stats.get("scraper", 0) > 50:
                recommendations.append("High scraper activity - implement API access controls")

            # Evasion technique recommendations
            if evasion_stats.get("user_agent_rotation", 0) > 20:
                recommendations.append("User agent rotation detected - implement device fingerprinting")

            if evasion_stats.get("browser_automation", 0) > 10:
                recommendations.append("Browser automation detected - implement CAPTCHA challenges")

            if not recommendations:
                recommendations.append("Bot detection system is functioning normally")

        except Exception as e:
            logger.error(f"Failed to generate bot security recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")

        return recommendations

    async def _record_threshold_experiment_result(self, request_id: str, result: BotDetectionResult,
                                                 bot_probability: float, threshold_used: float):
        """Record result for threshold calibration experiments."""
        try:
            # Determine if this was a true/false positive/negative
            # This is a simplified implementation - in production, you'd need ground truth data

            # For demonstration, we'll use heuristics to estimate true labels
            is_likely_bot = (
                len(result.evasion_techniques) > 2 or
                result.risk_score > 0.9 or
                "automation" in result.fingerprint.user_agent.lower()
            )

            # Determine experiment group based on threshold used
            default_threshold = 0.7  # Default bot detection threshold
            group_id = "test" if abs(threshold_used - default_threshold) > 0.01 else "control"

            # Record result for any active experiments
            for experiment in threshold_calibrator.active_experiments.values():
                if (experiment.threshold_type == ThresholdType.BOT_DETECTION and
                    experiment.status.value == "running"):

                    is_true_positive = result.is_bot and is_likely_bot
                    is_false_positive = result.is_bot and not is_likely_bot

                    await threshold_calibrator.record_experiment_result(
                        experiment.experiment_id,
                        group_id,
                        is_true_positive,
                        is_false_positive,
                        0.0  # Response time would be measured elsewhere
                    )
                    break

        except Exception as e:
            logger.error(f"Failed to record threshold experiment result: {e}")


# Global instance
ai_bot_detector = AIBotDetector()
