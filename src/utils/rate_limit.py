"""
Production-ready rate limiting utilities with exponential backoff.

Implements rate limiting for various APIs with proper semaphore-based
concurrency control and exponential backoff strategies.
"""

import asyncio
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, Callable, Awaitable
from dataclasses import dataclass
from enum import Enum
import logging
from functools import wraps

import structlog
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
)
import aiohttp

logger = structlog.get_logger(__name__)


class RateLimitStrategy(Enum):
    """Rate limiting strategies."""
    TOKEN_BUCKET = "token_bucket"
    SLIDING_WINDOW = "sliding_window"
    FIXED_WINDOW = "fixed_window"


@dataclass
class RateLimitConfig:
    """Rate limit configuration."""
    requests_per_second: float
    burst_size: int
    strategy: RateLimitStrategy = RateLimitStrategy.TOKEN_BUCKET
    backoff_factor: float = 2.0
    max_backoff: float = 60.0
    jitter: bool = True


class RateLimiter:
    """
    Generic rate limiter with multiple strategies.
    
    Features:
    - Token bucket algorithm (default)
    - Sliding window rate limiting
    - Exponential backoff with jitter
    - Async/await support
    - Thread-safe operation
    """
    
    def __init__(self, config: RateLimitConfig):
        self.config = config
        self.semaphore = asyncio.Semaphore(config.burst_size)
        self._tokens = config.burst_size
        self._last_refill = time.time()
        self._requests = []  # For sliding window
        self._lock = asyncio.Lock()
        
    async def acquire(self) -> None:
        """Acquire rate limit permission."""
        async with self._lock:
            if self.config.strategy == RateLimitStrategy.TOKEN_BUCKET:
                await self._token_bucket_acquire()
            elif self.config.strategy == RateLimitStrategy.SLIDING_WINDOW:
                await self._sliding_window_acquire()
            else:  # FIXED_WINDOW
                await self._fixed_window_acquire()
    
    async def _token_bucket_acquire(self) -> None:
        """Token bucket rate limiting."""
        now = time.time()
        time_passed = now - self._last_refill
        
        # Refill tokens
        tokens_to_add = time_passed * self.config.requests_per_second
        self._tokens = min(
            self.config.burst_size,
            self._tokens + tokens_to_add
        )
        self._last_refill = now
        
        if self._tokens >= 1:
            self._tokens -= 1
        else:
            # Calculate wait time
            wait_time = (1 - self._tokens) / self.config.requests_per_second
            logger.debug("Rate limit hit, waiting", wait_time=wait_time)
            await asyncio.sleep(wait_time)
            self._tokens = 0
    
    async def _sliding_window_acquire(self) -> None:
        """Sliding window rate limiting."""
        now = time.time()
        window_start = now - 1.0  # 1 second window
        
        # Remove old requests
        self._requests = [req_time for req_time in self._requests if req_time > window_start]
        
        if len(self._requests) >= self.config.requests_per_second:
            # Calculate wait time until oldest request expires
            wait_time = self._requests[0] - window_start + 0.001  # Small buffer
            logger.debug("Sliding window rate limit hit", wait_time=wait_time)
            await asyncio.sleep(wait_time)
        
        self._requests.append(now)
    
    async def _fixed_window_acquire(self) -> None:
        """Fixed window rate limiting."""
        now = time.time()
        window_start = int(now)  # 1-second windows
        
        # Reset counter for new window
        if not hasattr(self, '_window_start') or self._window_start != window_start:
            self._window_start = window_start
            self._window_count = 0
        
        if self._window_count >= self.config.requests_per_second:
            wait_time = window_start + 1 - now
            logger.debug("Fixed window rate limit hit", wait_time=wait_time)
            await asyncio.sleep(wait_time)
            self._window_start = int(time.time())
            self._window_count = 0
        
        self._window_count += 1


class EtherscanRateLimiter(RateLimiter):
    """
    Etherscan-specific rate limiter.
    
    Free tier: 5 requests/second
    Pro tier: 100 requests/second
    
    Reference: https://docs.etherscan.io/api-endpoints/stats
    """
    
    def __init__(self, api_key: Optional[str] = None):
        # Determine rate limit based on API key presence
        if api_key and api_key != "your_etherscan_api_key_here":
            # Assume pro tier for valid API keys
            requests_per_second = 100
            burst_size = 200
        else:
            # Free tier limits
            requests_per_second = 5
            burst_size = 10
        
        config = RateLimitConfig(
            requests_per_second=requests_per_second,
            burst_size=burst_size,
            strategy=RateLimitStrategy.TOKEN_BUCKET,
            backoff_factor=2.0,
            max_backoff=30.0
        )
        super().__init__(config)
        self.api_key = api_key
        
        logger.info(
            "Etherscan rate limiter initialized",
            tier="pro" if requests_per_second > 5 else "free",
            rps=requests_per_second
        )


class APIRateLimiter:
    """
    Multi-API rate limiter manager.
    
    Manages rate limiters for different APIs with their specific limits.
    """
    
    def __init__(self):
        self.limiters: Dict[str, RateLimiter] = {}
        self._setup_default_limiters()
    
    def _setup_default_limiters(self) -> None:
        """Setup default rate limiters for common APIs."""
        
        # CoinGecko: 10-50 calls/minute depending on plan
        # https://www.coingecko.com/en/api/pricing
        self.limiters["coingecko"] = RateLimiter(RateLimitConfig(
            requests_per_second=0.5,  # 30 requests/minute
            burst_size=5,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))
        
        # DexScreener: No official limits, be conservative
        self.limiters["dexscreener"] = RateLimiter(RateLimitConfig(
            requests_per_second=2.0,
            burst_size=10,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))
        
        # DeFiLlama: No official limits, be conservative
        self.limiters["defillama"] = RateLimiter(RateLimitConfig(
            requests_per_second=1.0,
            burst_size=5,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))
        
        # Fear & Greed Index: Very conservative
        self.limiters["fear_greed"] = RateLimiter(RateLimitConfig(
            requests_per_second=0.1,  # 6 requests/minute
            burst_size=2,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))
        
        # Google Trends (via PyTrends): Conservative
        self.limiters["google_trends"] = RateLimiter(RateLimitConfig(
            requests_per_second=0.2,  # 12 requests/minute
            burst_size=3,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))

        # Honeypot.is API: Conservative for free tier
        self.limiters["honeypot_is"] = RateLimiter(RateLimitConfig(
            requests_per_second=1.0,  # 60 requests/minute
            burst_size=2,  # Small burst to test rate limiting
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))

        # GoPlus Labs API: Conservative for free tier
        self.limiters["goplus"] = RateLimiter(RateLimitConfig(
            requests_per_second=2.0,  # 120 requests/minute
            burst_size=10,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))

        # Birdeye API: Conservative for free tier
        self.limiters["birdeye"] = RateLimiter(RateLimitConfig(
            requests_per_second=1.0,  # 60 requests/minute
            burst_size=5,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))

        # OpenRouter API: Conservative for free tier
        self.limiters["openrouter"] = RateLimiter(RateLimitConfig(
            requests_per_second=0.5,  # 30 requests/minute
            burst_size=3,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))

        # Etherscan API: Based on tier
        etherscan_key = os.getenv("ETHERSCAN_API_KEY")
        if etherscan_key and etherscan_key != "your_etherscan_api_key_here":
            # Pro tier
            self.limiters["etherscan"] = RateLimiter(RateLimitConfig(
                requests_per_second=100,
                burst_size=200,
                strategy=RateLimitStrategy.TOKEN_BUCKET
            ))
        else:
            # Free tier
            self.limiters["etherscan"] = RateLimiter(RateLimitConfig(
                requests_per_second=5,
                burst_size=10,
                strategy=RateLimitStrategy.TOKEN_BUCKET
            ))

        # Infura API: Based on tier
        self.limiters["infura"] = RateLimiter(RateLimitConfig(
            requests_per_second=10,  # Conservative for free tier
            burst_size=20,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))

        # Test API for testing purposes
        self.limiters["test_api"] = RateLimiter(RateLimitConfig(
            requests_per_second=2.0,
            burst_size=3,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        ))
    
    def add_limiter(self, name: str, config: RateLimitConfig) -> None:
        """Add a custom rate limiter."""
        self.limiters[name] = RateLimiter(config)
        logger.info("Added rate limiter", name=name, config=config)
    
    async def acquire(self, api_name: str) -> None:
        """Acquire rate limit for specific API."""
        if api_name not in self.limiters:
            logger.warning("No rate limiter for API", api=api_name)
            return
        
        await self.limiters[api_name].acquire()
    
    def get_limiter(self, api_name: str) -> Optional[RateLimiter]:
        """Get rate limiter for specific API."""
        return self.limiters.get(api_name)


def rate_limited(
    api_name: str,
    max_retries: int = 3,
    backoff_factor: float = 2.0
) -> Callable:
    """
    Decorator for rate-limited API calls with exponential backoff.
    
    Args:
        api_name: Name of the API for rate limiting
        max_retries: Maximum number of retry attempts
        backoff_factor: Exponential backoff multiplier
    
    Example:
        @rate_limited("etherscan", max_retries=3)
        async def fetch_token_info(address: str):
            # API call implementation
            pass
    """
    def decorator(func: Callable[..., Awaitable[Any]]) -> Callable[..., Awaitable[Any]]:
        @wraps(func)
        @retry(
            stop=stop_after_attempt(max_retries),
            wait=wait_exponential(multiplier=1, min=1, max=60),
            retry=retry_if_exception_type((
                aiohttp.ClientError,
                asyncio.TimeoutError,
                ConnectionError
            )),
            before_sleep=before_sleep_log(logger, logging.INFO)
        )
        async def wrapper(*args, **kwargs) -> Any:
            # Get or create rate limiter
            if not hasattr(wrapper, '_rate_limiter_manager'):
                wrapper._rate_limiter_manager = APIRateLimiter()
            
            # Acquire rate limit
            await wrapper._rate_limiter_manager.acquire(api_name)
            
            try:
                result = await func(*args, **kwargs)
                logger.debug("API call successful", api=api_name, func=func.__name__)
                return result
            except Exception as e:
                logger.warning(
                    "API call failed",
                    api=api_name,
                    func=func.__name__,
                    error=str(e)
                )
                raise
        
        return wrapper
    return decorator


# Global rate limiter manager instance
_global_rate_limiter = APIRateLimiter()


async def get_rate_limiter(api_name: str) -> RateLimiter:
    """Get global rate limiter for API."""
    return _global_rate_limiter.get_limiter(api_name)


async def acquire_rate_limit(api_name: str) -> None:
    """Acquire rate limit from global manager."""
    await _global_rate_limiter.acquire(api_name)
