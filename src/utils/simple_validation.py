"""
Simplified data validation without Great Expectations complexity.

This module provides essential data validation for API responses,
agent outputs, and pipeline data flow validation using basic checks.
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import logging
import json
import re
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Container for validation results."""
    success: bool
    suite_name: str
    data_points: int
    passed_checks: int
    failed_checks: int
    success_rate: float
    errors: List[str]
    warnings: List[str]
    details: Dict[str, Any]


class SimpleDataValidator:
    """Simple data validator using basic checks."""
    
    def validate_api_response(
        self,
        data: Union[Dict, List],
        api_name: str,
        endpoint: str
    ) -> ValidationResult:
        """
        Validate API response data structure and content.
        
        Args:
            data: API response data
            api_name: Name of the API (e.g., 'coingecko', 'etherscan')
            endpoint: API endpoint name
            
        Returns:
            ValidationResult with validation details
        """
        suite_name = f"{api_name}_{endpoint}_validation"
        errors = []
        warnings = []
        passed_checks = 0
        total_checks = 0
        
        try:
            # Convert data to DataFrame for validation
            if isinstance(data, dict):
                if api_name == "coingecko" and "prices" in data:
                    # Handle CoinGecko market chart format
                    df = self._convert_coingecko_market_chart(data)
                elif api_name == "dexscreener" and "pairs" in data:
                    # Handle DexScreener pairs format
                    df = pd.DataFrame(data["pairs"])
                else:
                    # Generic dict to DataFrame conversion
                    df = pd.json_normalize(data)
            elif isinstance(data, list):
                df = pd.DataFrame(data)
            else:
                raise ValueError(f"Unsupported data type: {type(data)}")
            
            if df.empty:
                return ValidationResult(
                    success=False,
                    suite_name=suite_name,
                    data_points=0,
                    passed_checks=0,
                    failed_checks=1,
                    success_rate=0.0,
                    errors=["Empty dataset"],
                    warnings=[],
                    details={}
                )
            
            # Run API-specific validations
            if api_name == "coingecko":
                passed, total, check_errors = self._validate_coingecko_data(df, endpoint)
            elif api_name == "dexscreener":
                passed, total, check_errors = self._validate_dexscreener_data(df, endpoint)
            elif api_name == "etherscan":
                passed, total, check_errors = self._validate_etherscan_data(df, endpoint)
            else:
                passed, total, check_errors = self._validate_generic_data(df)
            
            passed_checks += passed
            total_checks += total
            errors.extend(check_errors)
            
            success_rate = passed_checks / total_checks if total_checks > 0 else 0.0
            
            return ValidationResult(
                success=success_rate >= 0.8,  # 80% success threshold
                suite_name=suite_name,
                data_points=len(df),
                passed_checks=passed_checks,
                failed_checks=total_checks - passed_checks,
                success_rate=success_rate,
                errors=errors,
                warnings=warnings,
                details={"data_shape": df.shape, "columns": list(df.columns)}
            )
            
        except Exception as e:
            logger.error(f"Validation failed for {api_name}/{endpoint}: {e}")
            return ValidationResult(
                success=False,
                suite_name=suite_name,
                data_points=0,
                passed_checks=0,
                failed_checks=1,
                success_rate=0.0,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                details={}
            )
    
    def validate_agent_output(
        self,
        agent_output: Dict[str, Any],
        agent_name: str,
        expected_schema: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate AutoGen agent output against expected schema.
        
        Args:
            agent_output: Output from AutoGen agent
            agent_name: Name of the agent
            expected_schema: Expected output schema
            
        Returns:
            ValidationResult with validation details
        """
        suite_name = f"{agent_name}_output_validation"
        errors = []
        passed_checks = 0
        total_checks = 0
        
        try:
            # Check required fields
            for field_name, field_config in expected_schema.items():
                total_checks += 1
                
                if field_config.get("required", False):
                    if field_name in agent_output and agent_output[field_name] is not None:
                        passed_checks += 1
                    else:
                        errors.append(f"Missing required field: {field_name}")
                else:
                    passed_checks += 1  # Optional fields always pass if missing
                
                # Type checking
                if field_name in agent_output and agent_output[field_name] is not None:
                    total_checks += 1
                    expected_type = field_config.get("type")
                    actual_value = agent_output[field_name]
                    
                    if expected_type == "float" and isinstance(actual_value, (int, float)):
                        passed_checks += 1
                    elif expected_type == "str" and isinstance(actual_value, str):
                        passed_checks += 1
                    elif expected_type == "int" and isinstance(actual_value, int):
                        passed_checks += 1
                    elif expected_type == "bool" and isinstance(actual_value, bool):
                        passed_checks += 1
                    else:
                        errors.append(f"Type mismatch for {field_name}: expected {expected_type}, got {type(actual_value).__name__}")
                
                # Range checking
                if (field_name in agent_output and 
                    agent_output[field_name] is not None and
                    isinstance(agent_output[field_name], (int, float))):
                    
                    value = agent_output[field_name]
                    
                    if "min_value" in field_config:
                        total_checks += 1
                        if value >= field_config["min_value"]:
                            passed_checks += 1
                        else:
                            errors.append(f"{field_name} below minimum: {value} < {field_config['min_value']}")
                    
                    if "max_value" in field_config:
                        total_checks += 1
                        if value <= field_config["max_value"]:
                            passed_checks += 1
                        else:
                            errors.append(f"{field_name} above maximum: {value} > {field_config['max_value']}")
            
            success_rate = passed_checks / total_checks if total_checks > 0 else 0.0
            
            return ValidationResult(
                success=success_rate >= 0.8,
                suite_name=suite_name,
                data_points=1,
                passed_checks=passed_checks,
                failed_checks=total_checks - passed_checks,
                success_rate=success_rate,
                errors=errors,
                warnings=[],
                details={"agent_output_keys": list(agent_output.keys())}
            )
            
        except Exception as e:
            logger.error(f"Agent output validation failed for {agent_name}: {e}")
            return ValidationResult(
                success=False,
                suite_name=suite_name,
                data_points=0,
                passed_checks=0,
                failed_checks=1,
                success_rate=0.0,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                details={}
            )
    
    def _convert_coingecko_market_chart(self, data: Dict) -> pd.DataFrame:
        """Convert CoinGecko market chart data to DataFrame."""
        prices = data.get("prices", [])
        volumes = data.get("total_volumes", [])
        
        df_data = []
        for i, (timestamp_ms, price) in enumerate(prices):
            volume = volumes[i][1] if i < len(volumes) else 0
            df_data.append({
                "timestamp": timestamp_ms,
                "price": price,
                "volume": volume
            })
        
        return pd.DataFrame(df_data)
    
    def _validate_coingecko_data(self, df: pd.DataFrame, endpoint: str) -> tuple:
        """Validate CoinGecko-specific data."""
        passed = 0
        total = 0
        errors = []
        
        if "market_chart" in endpoint:
            # Check required columns
            required_cols = ["timestamp", "price"]
            for col in required_cols:
                total += 1
                if col in df.columns:
                    passed += 1
                else:
                    errors.append(f"Missing column: {col}")
            
            # Check price values
            if "price" in df.columns:
                total += 1
                if df["price"].notna().all() and (df["price"] >= 0).all():
                    passed += 1
                else:
                    errors.append("Invalid price values (null or negative)")
        
        return passed, total, errors
    
    def _validate_dexscreener_data(self, df: pd.DataFrame, endpoint: str) -> tuple:
        """Validate DexScreener-specific data."""
        passed = 0
        total = 0
        errors = []
        
        # Check required columns
        required_cols = ["chainId", "pairAddress"]
        for col in required_cols:
            total += 1
            if col in df.columns:
                passed += 1
            else:
                errors.append(f"Missing column: {col}")
        
        # Check chain ID values
        if "chainId" in df.columns:
            total += 1
            if df["chainId"].notna().all():
                passed += 1
            else:
                errors.append("Null chainId values found")
        
        return passed, total, errors
    
    def _validate_etherscan_data(self, df: pd.DataFrame, endpoint: str) -> tuple:
        """Validate Etherscan-specific data."""
        passed = 0
        total = 0
        errors = []
        
        # Basic row count check
        total += 1
        if len(df) > 0:
            passed += 1
        else:
            errors.append("Empty Etherscan response")
        
        return passed, total, errors
    
    def _validate_generic_data(self, df: pd.DataFrame) -> tuple:
        """Validate generic data."""
        passed = 0
        total = 0
        errors = []
        
        # Basic checks
        total += 1
        if len(df) > 0:
            passed += 1
        else:
            errors.append("Empty dataset")
        
        total += 1
        if len(df.columns) > 0:
            passed += 1
        else:
            errors.append("No columns found")
        
        return passed, total, errors


# Convenience functions for common validations
def validate_token_address(address: str) -> bool:
    """Validate Ethereum token address format."""
    if not address or not isinstance(address, str):
        return False
    
    # Ethereum address pattern
    eth_pattern = r"^0x[a-fA-F0-9]{40}$"
    return bool(re.match(eth_pattern, address))


def validate_price_data(price_data: List[Dict]) -> ValidationResult:
    """Validate price data structure and values."""
    validator = SimpleDataValidator()
    return validator.validate_api_response(price_data, "price_data", "validation")


def validate_technical_indicators(indicators: Dict[str, Any]) -> ValidationResult:
    """Validate technical analysis indicators."""
    expected_schema = {
        "rsi_14": {"type": "float", "min_value": 0, "max_value": 100},
        "macd_line": {"type": "float"},
        "bb_upper": {"type": "float", "min_value": 0},
        "bb_lower": {"type": "float", "min_value": 0},
        "overall_signal": {"type": "str", "required": True}
    }
    
    validator = SimpleDataValidator()
    return validator.validate_agent_output(indicators, "technical_analysis", expected_schema)
