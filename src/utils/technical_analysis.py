"""
Technical Analysis utilities using TA-Lib.

This module provides comprehensive technical analysis indicators
for cryptocurrency token analysis.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    logging.warning("TA-Lib not available. Technical analysis will be limited.")

logger = logging.getLogger(__name__)


@dataclass
class TechnicalIndicators:
    """Container for technical analysis indicators."""
    
    # Trend Indicators
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    
    # Momentum Indicators
    rsi_14: Optional[float] = None
    macd_line: Optional[float] = None
    macd_signal: Optional[float] = None
    macd_histogram: Optional[float] = None
    
    # Volatility Indicators
    bb_upper: Optional[float] = None
    bb_middle: Optional[float] = None
    bb_lower: Optional[float] = None
    bb_width: Optional[float] = None
    
    # Volume Indicators
    volume_sma: Optional[float] = None
    
    # Additional Indicators
    stoch_k: Optional[float] = None
    stoch_d: Optional[float] = None
    atr: Optional[float] = None
    
    # Analysis Summary
    trend_signal: Optional[str] = None  # "bullish", "bearish", "neutral"
    momentum_signal: Optional[str] = None
    volatility_signal: Optional[str] = None
    overall_signal: Optional[str] = None
    confidence: Optional[float] = None


class TechnicalAnalyzer:
    """Comprehensive technical analysis using TA-Lib."""
    
    def __init__(self):
        if not TALIB_AVAILABLE:
            raise ImportError("TA-Lib is required for technical analysis")
    
    def analyze_ohlcv(
        self,
        ohlcv_data: List[Dict[str, Any]],
        current_price: float
    ) -> TechnicalIndicators:
        """
        Perform comprehensive technical analysis on OHLCV data.
        
        Args:
            ohlcv_data: List of OHLCV dictionaries with keys:
                       ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            current_price: Current token price
            
        Returns:
            TechnicalIndicators object with all calculated indicators
        """
        if len(ohlcv_data) < 50:
            logger.warning(f"Insufficient data for technical analysis: {len(ohlcv_data)} points")
            return TechnicalIndicators()
        
        # Convert to numpy arrays
        df = pd.DataFrame(ohlcv_data)
        
        # Ensure numeric types
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remove any NaN values
        df = df.dropna()
        
        if len(df) < 50:
            logger.warning("Insufficient clean data after processing")
            return TechnicalIndicators()
        
        # Extract arrays
        opens = df['open'].values
        highs = df['high'].values
        lows = df['low'].values
        closes = df['close'].values
        volumes = df['volume'].values
        
        try:
            indicators = TechnicalIndicators()
            
            # Calculate trend indicators
            indicators.sma_20 = self._safe_indicator(talib.SMA, closes, timeperiod=20)
            indicators.sma_50 = self._safe_indicator(talib.SMA, closes, timeperiod=50)
            indicators.ema_12 = self._safe_indicator(talib.EMA, closes, timeperiod=12)
            indicators.ema_26 = self._safe_indicator(talib.EMA, closes, timeperiod=26)
            
            # Calculate momentum indicators
            indicators.rsi_14 = self._safe_indicator(talib.RSI, closes, timeperiod=14)
            
            # MACD
            macd_line, macd_signal, macd_hist = talib.MACD(
                closes, fastperiod=12, slowperiod=26, signalperiod=9
            )
            indicators.macd_line = self._get_last_valid(macd_line)
            indicators.macd_signal = self._get_last_valid(macd_signal)
            indicators.macd_histogram = self._get_last_valid(macd_hist)
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = talib.BBANDS(
                closes, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0
            )
            indicators.bb_upper = self._get_last_valid(bb_upper)
            indicators.bb_middle = self._get_last_valid(bb_middle)
            indicators.bb_lower = self._get_last_valid(bb_lower)
            
            if indicators.bb_upper and indicators.bb_lower:
                indicators.bb_width = (indicators.bb_upper - indicators.bb_lower) / indicators.bb_middle
            
            # Stochastic
            stoch_k, stoch_d = talib.STOCH(
                highs, lows, closes, 
                fastk_period=14, slowk_period=3, slowd_period=3
            )
            indicators.stoch_k = self._get_last_valid(stoch_k)
            indicators.stoch_d = self._get_last_valid(stoch_d)
            
            # Average True Range
            indicators.atr = self._safe_indicator(talib.ATR, highs, lows, closes, timeperiod=14)
            
            # Volume indicators
            indicators.volume_sma = self._safe_indicator(talib.SMA, volumes, timeperiod=20)
            
            # Generate signals
            self._generate_signals(indicators, current_price)
            
            logger.info("Technical analysis completed successfully")
            return indicators
            
        except Exception as e:
            logger.error(f"Technical analysis failed: {e}")
            return TechnicalIndicators()
    
    def _safe_indicator(self, func, *args, **kwargs) -> Optional[float]:
        """Safely calculate indicator and return last valid value."""
        try:
            result = func(*args, **kwargs)
            return self._get_last_valid(result)
        except Exception as e:
            logger.warning(f"Indicator calculation failed: {e}")
            return None
    
    def _get_last_valid(self, array: np.ndarray) -> Optional[float]:
        """Get the last non-NaN value from array."""
        if array is None or len(array) == 0:
            return None
        
        # Find last non-NaN value
        valid_indices = ~np.isnan(array)
        if not np.any(valid_indices):
            return None
        
        last_valid_idx = np.where(valid_indices)[0][-1]
        return float(array[last_valid_idx])
    
    def _generate_signals(self, indicators: TechnicalIndicators, current_price: float):
        """Generate trading signals based on indicators."""
        signals = []
        confidence_scores = []
        
        # Trend Analysis
        trend_signals = []
        if indicators.sma_20 and indicators.sma_50:
            if indicators.sma_20 > indicators.sma_50:
                trend_signals.append("bullish")
            else:
                trend_signals.append("bearish")
        
        if indicators.ema_12 and indicators.ema_26:
            if indicators.ema_12 > indicators.ema_26:
                trend_signals.append("bullish")
            else:
                trend_signals.append("bearish")
        
        # Determine trend signal
        if len(trend_signals) > 0:
            bullish_count = trend_signals.count("bullish")
            if bullish_count > len(trend_signals) / 2:
                indicators.trend_signal = "bullish"
            elif bullish_count < len(trend_signals) / 2:
                indicators.trend_signal = "bearish"
            else:
                indicators.trend_signal = "neutral"
        
        # Momentum Analysis
        momentum_signals = []
        if indicators.rsi_14:
            if indicators.rsi_14 > 70:
                momentum_signals.append("overbought")
            elif indicators.rsi_14 < 30:
                momentum_signals.append("oversold")
            else:
                momentum_signals.append("neutral")
        
        if indicators.macd_line and indicators.macd_signal:
            if indicators.macd_line > indicators.macd_signal:
                momentum_signals.append("bullish")
            else:
                momentum_signals.append("bearish")
        
        # Determine momentum signal
        if "overbought" in momentum_signals:
            indicators.momentum_signal = "overbought"
        elif "oversold" in momentum_signals:
            indicators.momentum_signal = "oversold"
        elif momentum_signals.count("bullish") > momentum_signals.count("bearish"):
            indicators.momentum_signal = "bullish"
        elif momentum_signals.count("bearish") > momentum_signals.count("bullish"):
            indicators.momentum_signal = "bearish"
        else:
            indicators.momentum_signal = "neutral"
        
        # Volatility Analysis
        if indicators.bb_upper and indicators.bb_lower:
            bb_position = (current_price - indicators.bb_lower) / (indicators.bb_upper - indicators.bb_lower)
            if bb_position > 0.8:
                indicators.volatility_signal = "high_upper"
            elif bb_position < 0.2:
                indicators.volatility_signal = "high_lower"
            else:
                indicators.volatility_signal = "normal"
        
        # Overall Signal
        all_signals = [indicators.trend_signal, indicators.momentum_signal]
        bullish_signals = [s for s in all_signals if s == "bullish"]
        bearish_signals = [s for s in all_signals if s == "bearish"]
        
        if len(bullish_signals) > len(bearish_signals):
            indicators.overall_signal = "bullish"
            indicators.confidence = len(bullish_signals) / len([s for s in all_signals if s])
        elif len(bearish_signals) > len(bullish_signals):
            indicators.overall_signal = "bearish"
            indicators.confidence = len(bearish_signals) / len([s for s in all_signals if s])
        else:
            indicators.overall_signal = "neutral"
            indicators.confidence = 0.5
    
    def get_ohlcv_from_price_data(
        self,
        price_history: List[Dict[str, Any]],
        interval_minutes: int = 60
    ) -> List[Dict[str, Any]]:
        """
        Convert price history to OHLCV format for technical analysis.
        
        Args:
            price_history: List of price data points
            interval_minutes: Interval for OHLCV candles in minutes
            
        Returns:
            List of OHLCV dictionaries
        """
        if not price_history:
            return []
        
        try:
            df = pd.DataFrame(price_history)
            df['timestamp'] = pd.to_datetime(df['timestamp'], format='ISO8601')
            df = df.sort_values('timestamp')
            
            # Resample to create OHLCV candles
            df.set_index('timestamp', inplace=True)
            
            ohlcv = df['price'].resample(f'{interval_minutes}min').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last'
            }).dropna()
            
            # Add volume (use price as proxy if volume not available)
            if 'volume' in df.columns:
                volume = df['volume'].resample(f'{interval_minutes}min').sum()
            else:
                volume = df['price'].resample(f'{interval_minutes}min').count()
            
            ohlcv['volume'] = volume
            
            # Convert back to list of dictionaries
            result = []
            for timestamp, row in ohlcv.iterrows():
                result.append({
                    'timestamp': timestamp.isoformat(),
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': float(row['volume'])
                })
            
            return result
            
        except Exception as e:
            logger.error(f"OHLCV conversion failed: {e}")
            return []


def analyze_token_technicals(
    price_history: List[Dict[str, Any]],
    current_price: float
) -> Dict[str, Any]:
    """
    Convenience function for token technical analysis.
    
    Args:
        price_history: Historical price data
        current_price: Current token price
        
    Returns:
        Dictionary with technical analysis results
    """
    if not TALIB_AVAILABLE:
        return {
            "error": "TA-Lib not available",
            "indicators": {},
            "signals": {}
        }
    
    analyzer = TechnicalAnalyzer()
    
    # Convert price history to OHLCV
    ohlcv_data = analyzer.get_ohlcv_from_price_data(price_history)
    
    if len(ohlcv_data) < 50:
        return {
            "error": "Insufficient data for technical analysis",
            "data_points": len(ohlcv_data),
            "required": 50
        }
    
    # Perform analysis
    indicators = analyzer.analyze_ohlcv(ohlcv_data, current_price)
    
    # Convert to dictionary for JSON serialization
    return {
        "success": True,
        "current_price": current_price,
        "data_points": len(ohlcv_data),
        "indicators": {
            "trend": {
                "sma_20": indicators.sma_20,
                "sma_50": indicators.sma_50,
                "ema_12": indicators.ema_12,
                "ema_26": indicators.ema_26
            },
            "momentum": {
                "rsi_14": indicators.rsi_14,
                "macd_line": indicators.macd_line,
                "macd_signal": indicators.macd_signal,
                "macd_histogram": indicators.macd_histogram,
                "stoch_k": indicators.stoch_k,
                "stoch_d": indicators.stoch_d
            },
            "volatility": {
                "bb_upper": indicators.bb_upper,
                "bb_middle": indicators.bb_middle,
                "bb_lower": indicators.bb_lower,
                "bb_width": indicators.bb_width,
                "atr": indicators.atr
            },
            "volume": {
                "volume_sma": indicators.volume_sma
            }
        },
        "signals": {
            "trend": indicators.trend_signal,
            "momentum": indicators.momentum_signal,
            "volatility": indicators.volatility_signal,
            "overall": indicators.overall_signal,
            "confidence": indicators.confidence
        }
    }
