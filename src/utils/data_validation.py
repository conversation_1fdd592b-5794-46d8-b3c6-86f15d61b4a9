"""
Data validation utilities using Great Expectations.

This module provides comprehensive data validation for API responses,
agent outputs, and pipeline data flow validation.
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import logging
import json
import re
from datetime import datetime

try:
    import great_expectations as gx
    GX_AVAILABLE = True
except ImportError:
    GX_AVAILABLE = False
    logging.warning("Great Expectations not available. Data validation will be limited.")

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Container for validation results."""
    success: bool
    suite_name: str
    data_points: int
    passed_expectations: int
    failed_expectations: int
    success_rate: float
    errors: List[str]
    warnings: List[str]
    details: Dict[str, Any]


class DataValidator:
    """Comprehensive data validator using Great Expectations."""
    
    def __init__(self, context_root_dir: Optional[str] = None):
        if not GX_AVAILABLE:
            raise ImportError("Great Expectations is required for data validation")
        
        self.context_root_dir = context_root_dir or "tests/great_expectations"
        self.context = None
        self._initialize_context()
    
    def _initialize_context(self):
        """Initialize Great Expectations context."""
        try:
            # Create a simple in-memory context for testing
            self.context = gx.get_context()
            logger.info("Great Expectations context initialized")
        except Exception as e:
            logger.error(f"Failed to initialize GX context: {e}")
            raise
    
    def validate_api_response(
        self,
        data: Union[Dict, List],
        api_name: str,
        endpoint: str
    ) -> ValidationResult:
        """
        Validate API response data structure and content.
        
        Args:
            data: API response data
            api_name: Name of the API (e.g., 'coingecko', 'etherscan')
            endpoint: API endpoint name
            
        Returns:
            ValidationResult with validation details
        """
        suite_name = f"{api_name}_{endpoint}_validation"
        
        try:
            # Convert data to DataFrame for validation
            if isinstance(data, dict):
                if api_name == "coingecko" and "prices" in data:
                    # Handle CoinGecko market chart format
                    df = self._convert_coingecko_market_chart(data)
                elif api_name == "dexscreener" and "pairs" in data:
                    # Handle DexScreener pairs format
                    df = pd.DataFrame(data["pairs"])
                else:
                    # Generic dict to DataFrame conversion
                    df = pd.json_normalize(data)
            elif isinstance(data, list):
                df = pd.DataFrame(data)
            else:
                raise ValueError(f"Unsupported data type: {type(data)}")
            
            if df.empty:
                return ValidationResult(
                    success=False,
                    suite_name=suite_name,
                    data_points=0,
                    passed_expectations=0,
                    failed_expectations=1,
                    success_rate=0.0,
                    errors=["Empty dataset"],
                    warnings=[],
                    details={}
                )
            
            # Create or get expectation suite
            suite = self._get_or_create_suite(suite_name, api_name, endpoint)
            
            # Create batch request using modern GX API
            batch_request = gx.core.batch.RuntimeBatchRequest(
                datasource_name="runtime_datasource",
                data_connector_name="default_runtime_data_connector",
                data_asset_name=f"{api_name}_{endpoint}",
                runtime_parameters={"batch_data": df},
                batch_identifiers={
                    "api_name": api_name,
                    "endpoint": endpoint,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # Get validator and run validation
            validator = self.context.get_validator(
                batch_request=batch_request,
                expectation_suite=suite
            )
            
            results = validator.validate()
            
            # Process results
            passed = sum(1 for r in results.results if r.success)
            failed = sum(1 for r in results.results if not r.success)
            total = len(results.results)
            
            errors = []
            warnings = []
            
            for result in results.results:
                if not result.success:
                    expectation_type = result.expectation_config.expectation_type
                    errors.append(f"Failed: {expectation_type}")
                    
                    # Add specific error details
                    if hasattr(result, 'result') and result.result:
                        if 'unexpected_count' in result.result:
                            errors.append(f"  Unexpected values: {result.result['unexpected_count']}")
                        if 'partial_unexpected_list' in result.result:
                            errors.append(f"  Examples: {result.result['partial_unexpected_list'][:3]}")
            
            return ValidationResult(
                success=results.success,
                suite_name=suite_name,
                data_points=len(df),
                passed_expectations=passed,
                failed_expectations=failed,
                success_rate=passed / total if total > 0 else 0.0,
                errors=errors,
                warnings=warnings,
                details={
                    "statistics": results.statistics,
                    "meta": results.meta
                }
            )
            
        except Exception as e:
            logger.error(f"Validation failed for {api_name}/{endpoint}: {e}")
            return ValidationResult(
                success=False,
                suite_name=suite_name,
                data_points=0,
                passed_expectations=0,
                failed_expectations=1,
                success_rate=0.0,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                details={}
            )
    
    def validate_agent_output(
        self,
        agent_output: Dict[str, Any],
        agent_name: str,
        expected_schema: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate AutoGen agent output against expected schema.
        
        Args:
            agent_output: Output from AutoGen agent
            agent_name: Name of the agent
            expected_schema: Expected output schema
            
        Returns:
            ValidationResult with validation details
        """
        suite_name = f"{agent_name}_output_validation"
        
        try:
            # Convert agent output to DataFrame
            df = pd.json_normalize(agent_output)
            
            # Create or get expectation suite
            suite = self._get_or_create_agent_suite(suite_name, agent_name, expected_schema)
            
            # Create batch request
            batch_request = RuntimeBatchRequest(
                datasource_name="runtime_datasource",
                data_connector_name="default_runtime_data_connector",
                data_asset_name=f"{agent_name}_output",
                runtime_parameters={"batch_data": df},
                batch_identifiers={
                    "agent_name": agent_name,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            # Get validator and run validation
            validator = self.context.get_validator(
                batch_request=batch_request,
                expectation_suite=suite
            )
            
            results = validator.validate()
            
            # Process results
            passed = sum(1 for r in results.results if r.success)
            failed = sum(1 for r in results.results if not r.success)
            total = len(results.results)
            
            errors = []
            for result in results.results:
                if not result.success:
                    errors.append(f"Failed: {result.expectation_config.expectation_type}")
            
            return ValidationResult(
                success=results.success,
                suite_name=suite_name,
                data_points=len(df),
                passed_expectations=passed,
                failed_expectations=failed,
                success_rate=passed / total if total > 0 else 0.0,
                errors=errors,
                warnings=[],
                details={"statistics": results.statistics}
            )
            
        except Exception as e:
            logger.error(f"Agent output validation failed for {agent_name}: {e}")
            return ValidationResult(
                success=False,
                suite_name=suite_name,
                data_points=0,
                passed_expectations=0,
                failed_expectations=1,
                success_rate=0.0,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                details={}
            )
    
    def _convert_coingecko_market_chart(self, data: Dict) -> pd.DataFrame:
        """Convert CoinGecko market chart data to DataFrame."""
        prices = data.get("prices", [])
        volumes = data.get("total_volumes", [])
        
        df_data = []
        for i, (timestamp_ms, price) in enumerate(prices):
            volume = volumes[i][1] if i < len(volumes) else 0
            df_data.append({
                "timestamp": timestamp_ms,
                "price": price,
                "volume": volume
            })
        
        return pd.DataFrame(df_data)
    
    def _get_or_create_suite(
        self,
        suite_name: str,
        api_name: str,
        endpoint: str
    ) -> gx.ExpectationSuite:
        """Get existing or create new expectation suite for API validation."""
        try:
            return self.context.get_expectation_suite(suite_name)
        except:
            # Create new suite
            suite = self.context.create_expectation_suite(suite_name)
            
            # Add common API validation expectations
            if api_name == "coingecko":
                self._add_coingecko_expectations(suite, endpoint)
            elif api_name == "dexscreener":
                self._add_dexscreener_expectations(suite, endpoint)
            elif api_name == "etherscan":
                self._add_etherscan_expectations(suite, endpoint)
            
            return suite
    
    def _add_coingecko_expectations(self, suite: gx.ExpectationSuite, endpoint: str):
        """Add CoinGecko-specific validation expectations."""
        if "market_chart" in endpoint:
            suite.add_expectation(
                gx.expectations.ExpectColumnToExist(column="timestamp")
            )
            suite.add_expectation(
                gx.expectations.ExpectColumnToExist(column="price")
            )
            suite.add_expectation(
                gx.expectations.ExpectColumnValuesToNotBeNull(column="price")
            )
            suite.add_expectation(
                gx.expectations.ExpectColumnValuesToBeOfType(column="price", type_="float")
            )
            suite.add_expectation(
                gx.expectations.ExpectColumnValuesToBeBetween(
                    column="price", min_value=0, max_value=1000000
                )
            )
    
    def _add_dexscreener_expectations(self, suite: gx.ExpectationSuite, endpoint: str):
        """Add DexScreener-specific validation expectations."""
        if "pairs" in endpoint or "search" in endpoint:
            suite.add_expectation(
                gx.expectations.ExpectColumnToExist(column="chainId")
            )
            suite.add_expectation(
                gx.expectations.ExpectColumnToExist(column="pairAddress")
            )
            suite.add_expectation(
                gx.expectations.ExpectColumnValuesToNotBeNull(column="chainId")
            )
    
    def _add_etherscan_expectations(self, suite: gx.ExpectationSuite, endpoint: str):
        """Add Etherscan-specific validation expectations."""
        suite.add_expectation(
            gx.expectations.ExpectTableRowCountToBeBetween(min_value=1, max_value=10000)
        )
    
    def _get_or_create_agent_suite(
        self,
        suite_name: str,
        agent_name: str,
        expected_schema: Dict[str, Any]
    ) -> gx.ExpectationSuite:
        """Get existing or create new expectation suite for agent validation."""
        try:
            return self.context.get_expectation_suite(suite_name)
        except:
            # Create new suite based on expected schema
            suite = self.context.create_expectation_suite(suite_name)
            
            # Add expectations based on schema
            for field_name, field_config in expected_schema.items():
                if field_config.get("required", False):
                    suite.add_expectation(
                        gx.expectations.ExpectColumnToExist(column=field_name)
                    )
                    suite.add_expectation(
                        gx.expectations.ExpectColumnValuesToNotBeNull(column=field_name)
                    )
                
                if "type" in field_config:
                    suite.add_expectation(
                        gx.expectations.ExpectColumnValuesToBeOfType(
                            column=field_name, type_=field_config["type"]
                        )
                    )
                
                if "min_value" in field_config and "max_value" in field_config:
                    suite.add_expectation(
                        gx.expectations.ExpectColumnValuesToBeBetween(
                            column=field_name,
                            min_value=field_config["min_value"],
                            max_value=field_config["max_value"]
                        )
                    )
            
            return suite


# Convenience functions for common validations
def validate_token_address(address: str) -> bool:
    """Validate Ethereum token address format."""
    if not address or not isinstance(address, str):
        return False
    
    # Ethereum address pattern
    eth_pattern = r"^0x[a-fA-F0-9]{40}$"
    return bool(re.match(eth_pattern, address))


def validate_price_data(price_data: List[Dict]) -> ValidationResult:
    """Validate price data structure and values."""
    if not GX_AVAILABLE:
        return ValidationResult(
            success=False,
            suite_name="price_validation",
            data_points=0,
            passed_expectations=0,
            failed_expectations=1,
            success_rate=0.0,
            errors=["Great Expectations not available"],
            warnings=[],
            details={}
        )
    
    validator = DataValidator()
    return validator.validate_api_response(price_data, "price_data", "validation")


def validate_technical_indicators(indicators: Dict[str, Any]) -> ValidationResult:
    """Validate technical analysis indicators."""
    expected_schema = {
        "rsi_14": {"type": "float", "min_value": 0, "max_value": 100},
        "macd_line": {"type": "float"},
        "bb_upper": {"type": "float", "min_value": 0},
        "bb_lower": {"type": "float", "min_value": 0},
        "overall_signal": {"type": "str", "required": True}
    }
    
    if not GX_AVAILABLE:
        return ValidationResult(
            success=False,
            suite_name="technical_indicators",
            data_points=0,
            passed_expectations=0,
            failed_expectations=1,
            success_rate=0.0,
            errors=["Great Expectations not available"],
            warnings=[],
            details={}
        )
    
    validator = DataValidator()
    return validator.validate_agent_output(indicators, "technical_analysis", expected_schema)
