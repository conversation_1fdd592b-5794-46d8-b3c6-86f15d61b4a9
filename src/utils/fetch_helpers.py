"""
Production-ready fetch helper functions for external APIs.

All functions implement:
- Exponential backoff with jitter
- Proper error handling and logging
- Type-safe return values
- Rate limiting integration
- Comprehensive documentation
"""

import asyncio
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import json

import aiohttp
import structlog
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

from .rate_limit import rate_limited, EtherscanRateLimiter

logger = structlog.get_logger(__name__)


@dataclass
class APIResponse:
    """Standardized API response wrapper."""
    success: bool
    data: Any
    error: Optional[str] = None
    status_code: Optional[int] = None
    rate_limited: bool = False
    cached: bool = False


@rate_limited("coingecko", max_retries=3)
async def fetch_coingecko_data(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    session: Optional[aiohttp.ClientSession] = None
) -> APIResponse:
    """
    Fetch data from CoinGecko API.
    
    Args:
        endpoint: API endpoint (e.g., 'simple/price')
        params: Query parameters
        session: Optional aiohttp session
    
    Returns:
        APIResponse with data or error information
    
    Reference: https://www.coingecko.com/en/api/documentation
    """
    base_url = "https://api.coingecko.com/api/v3"
    url = f"{base_url}/{endpoint.lstrip('/')}"
    
    # Add API key if available
    api_key = os.getenv("COINGECKO_API_KEY")
    headers = {"Accept": "application/json"}
    if api_key and api_key != "your_coingecko_api_key_here":
        headers["x-cg-demo-api-key"] = api_key
    
    params = params or {}
    
    try:
        if session:
            async with session.get(url, params=params, headers=headers) as response:
                return await _process_response(response, "coingecko")
        else:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    return await _process_response(response, "coingecko")
                    
    except Exception as e:
        logger.error("CoinGecko API error", endpoint=endpoint, error=str(e))
        return APIResponse(success=False, error=str(e))


@rate_limited("dexscreener", max_retries=3)
async def fetch_dexscreener_data(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    session: Optional[aiohttp.ClientSession] = None
) -> APIResponse:
    """
    Fetch data from DexScreener API.
    
    Args:
        endpoint: API endpoint (e.g., 'dex/tokens/trending')
        params: Query parameters
        session: Optional aiohttp session
    
    Returns:
        APIResponse with data or error information
    
    Reference: https://docs.dexscreener.com/api/reference
    """
    base_url = "https://api.dexscreener.com/latest"
    url = f"{base_url}/{endpoint.lstrip('/')}"
    
    headers = {
        "Accept": "application/json",
        "User-Agent": "TokenAnalyzer/1.0"
    }
    
    params = params or {}
    
    try:
        if session:
            async with session.get(url, params=params, headers=headers) as response:
                return await _process_response(response, "dexscreener")
        else:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    return await _process_response(response, "dexscreener")
                    
    except Exception as e:
        logger.error("DexScreener API error", endpoint=endpoint, error=str(e))
        return APIResponse(success=False, error=str(e))


@rate_limited("defillama", max_retries=3)
async def fetch_defillama_data(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    session: Optional[aiohttp.ClientSession] = None
) -> APIResponse:
    """
    Fetch data from DeFiLlama API.
    
    Args:
        endpoint: API endpoint (e.g., 'protocols')
        params: Query parameters
        session: Optional aiohttp session
    
    Returns:
        APIResponse with data or error information
    
    Reference: https://defillama.com/docs/api
    """
    base_url = "https://api.llama.fi"
    url = f"{base_url}/{endpoint.lstrip('/')}"
    
    headers = {
        "Accept": "application/json",
        "User-Agent": "TokenAnalyzer/1.0"
    }
    
    params = params or {}
    
    try:
        if session:
            async with session.get(url, params=params, headers=headers) as response:
                return await _process_response(response, "defillama")
        else:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    return await _process_response(response, "defillama")
                    
    except Exception as e:
        logger.error("DeFiLlama API error", endpoint=endpoint, error=str(e))
        return APIResponse(success=False, error=str(e))


async def discover_newest_tokens(
    min_liquidity_usd: float = 10000,
    max_age_hours: int = 24,
    limit: int = 50
) -> APIResponse:
    """
    Discover newest tokens using DexScreener API with proper filtering.

    Args:
        min_liquidity_usd: Minimum liquidity in USD to filter quality tokens
        max_age_hours: Maximum age in hours for "new" tokens
        limit: Maximum number of tokens to return

    Returns:
        APIResponse with filtered newest tokens
    """
    import time

    try:
        # Get current timestamp for age filtering
        current_time = time.time() * 1000  # Convert to milliseconds
        max_age_ms = max_age_hours * 60 * 60 * 1000
        min_created_at = current_time - max_age_ms

        # Search for recent pairs across multiple chains
        chains = ["ethereum", "solana", "base", "arbitrum"]
        all_new_tokens = []

        # Use DexScreener token profiles API for newest tokens
        profiles_url = "https://api.dexscreener.com/token-profiles/latest/v1"

        async with aiohttp.ClientSession() as session:
            # Get latest token profiles
            async with session.get(profiles_url) as response:
                if response.status == 200:
                    profiles = await response.json()

                    # For each profile, get the actual pair data
                    for profile in profiles[:100]:  # Limit to first 100 profiles
                        token_address = profile.get("tokenAddress")
                        chain_id = profile.get("chainId")

                        if token_address and chain_id:
                            # Get pair data for this token
                            search_url = f"https://api.dexscreener.com/latest/dex/search"
                            async with session.get(search_url, params={"q": token_address}) as pair_response:
                                if pair_response.status == 200:
                                    pair_data = await pair_response.json()
                                    pairs = pair_data.get("pairs", [])

                                    for pair in pairs:
                                        pair_created_at = pair.get("pairCreatedAt")
                                        liquidity_usd = pair.get("liquidity", {}).get("usd", 0)

                                        # Filter by age and liquidity
                                        if (pair_created_at and
                                            pair_created_at >= min_created_at and
                                            liquidity_usd >= min_liquidity_usd):

                                            token_info = {
                                                "address": pair.get("baseToken", {}).get("address"),
                                                "symbol": pair.get("baseToken", {}).get("symbol"),
                                                "name": pair.get("baseToken", {}).get("name"),
                                                "chain_id": pair.get("chainId"),
                                                "price_usd": pair.get("priceUsd"),
                                                "liquidity_usd": liquidity_usd,
                                                "volume_24h": pair.get("volume", {}).get("h24", 0),
                                                "pair_created_at": pair_created_at,
                                                "age_hours": (current_time - pair_created_at) / (1000 * 60 * 60),
                                                "dex_id": pair.get("dexId"),
                                                "pair_address": pair.get("pairAddress"),
                                                "profile_url": profile.get("url", ""),
                                                "description": profile.get("description", "")
                                            }

                                            all_new_tokens.append(token_info)

        # Sort by creation time (newest first) and limit results
        all_new_tokens.sort(key=lambda x: x["pair_created_at"], reverse=True)
        newest_tokens = all_new_tokens[:limit]

        return APIResponse(
            success=True,
            data={
                "tokens": newest_tokens,
                "total_found": len(newest_tokens),
                "filters": {
                    "min_liquidity_usd": min_liquidity_usd,
                    "max_age_hours": max_age_hours,
                    "chains_searched": chains
                }
            }
        )

    except Exception as e:
        logger.error("Token discovery error", error=str(e))
        return APIResponse(
            success=False,
            error=f"Token discovery failed: {str(e)}"
        )


# Global Etherscan rate limiter
_etherscan_limiter = None


async def _get_etherscan_limiter() -> EtherscanRateLimiter:
    """Get or create Etherscan rate limiter."""
    global _etherscan_limiter
    if _etherscan_limiter is None:
        api_key = os.getenv("ETHERSCAN_API_KEY")
        _etherscan_limiter = EtherscanRateLimiter(api_key)
    return _etherscan_limiter


async def fetch_etherscan_data(
    module: str,
    action: str,
    params: Optional[Dict[str, Any]] = None,
    session: Optional[aiohttp.ClientSession] = None
) -> APIResponse:
    """
    Fetch data from Etherscan API with proper rate limiting.
    
    Args:
        module: API module (e.g., 'account', 'contract')
        action: API action (e.g., 'balance', 'getsourcecode')
        params: Additional parameters
        session: Optional aiohttp session
    
    Returns:
        APIResponse with data or error information
    
    Reference: https://docs.etherscan.io/api-endpoints/accounts
    Rate Limit: 5 requests/second (free), 100 requests/second (pro)
    """
    # Apply rate limiting
    limiter = await _get_etherscan_limiter()
    await limiter.acquire()
    
    base_url = "https://api.etherscan.io/api"
    
    # Build parameters
    api_params = {
        "module": module,
        "action": action,
        "apikey": os.getenv("ETHERSCAN_API_KEY", "YourApiKeyToken")
    }
    
    if params:
        api_params.update(params)
    
    headers = {
        "Accept": "application/json",
        "User-Agent": "TokenAnalyzer/1.0"
    }
    
    try:
        if session:
            async with session.get(base_url, params=api_params, headers=headers) as response:
                return await _process_etherscan_response(response)
        else:
            async with aiohttp.ClientSession() as session:
                async with session.get(base_url, params=api_params, headers=headers) as response:
                    return await _process_etherscan_response(response)
                    
    except Exception as e:
        logger.error("Etherscan API error", module=module, action=action, error=str(e))
        return APIResponse(success=False, error=str(e))


@rate_limited("fear_greed", max_retries=2)
async def fetch_fear_greed_index(
    limit: int = 1,
    session: Optional[aiohttp.ClientSession] = None
) -> APIResponse:
    """
    Fetch Fear & Greed Index data.
    
    Args:
        limit: Number of results to return (default: 1)
        session: Optional aiohttp session
    
    Returns:
        APIResponse with fear & greed data
    
    Reference: https://alternative.me/crypto/fear-and-greed-index/
    """
    url = "https://api.alternative.me/fng/"
    params = {"limit": limit, "format": "json"}
    
    headers = {
        "Accept": "application/json",
        "User-Agent": "TokenAnalyzer/1.0"
    }
    
    try:
        if session:
            async with session.get(url, params=params, headers=headers) as response:
                return await _process_response(response, "fear_greed")
        else:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, headers=headers) as response:
                    return await _process_response(response, "fear_greed")
                    
    except Exception as e:
        logger.error("Fear & Greed API error", error=str(e))
        return APIResponse(success=False, error=str(e))


@rate_limited("google_trends", max_retries=2)
async def fetch_google_trends(
    keywords: List[str],
    timeframe: str = "today 7-d",
    geo: str = "US"
) -> APIResponse:
    """
    Fetch Google Trends data using PyTrends.
    
    Args:
        keywords: List of keywords to search
        timeframe: Time frame for trends (default: "today 7-d")
        geo: Geographic location (default: "US")
    
    Returns:
        APIResponse with trends data
    
    Note: This uses PyTrends library for Google Trends access
    """
    try:
        from pytrends.request import TrendReq
        
        # Initialize PyTrends
        pytrends = TrendReq(hl='en-US', tz=360)
        
        # Build payload
        pytrends.build_payload(keywords, cat=0, timeframe=timeframe, geo=geo, gprop='')
        
        # Get interest over time
        interest_over_time = pytrends.interest_over_time()
        
        # Get related queries
        related_queries = pytrends.related_queries()
        
        # Get related topics
        related_topics = pytrends.related_topics()
        
        data = {
            "keywords": keywords,
            "timeframe": timeframe,
            "geo": geo,
            "interest_over_time": interest_over_time.to_dict() if not interest_over_time.empty else {},
            "related_queries": related_queries,
            "related_topics": related_topics,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info("Google Trends data fetched", keywords=keywords, timeframe=timeframe)
        return APIResponse(success=True, data=data)
        
    except ImportError:
        error_msg = "PyTrends library not installed. Install with: pip install pytrends"
        logger.error(error_msg)
        return APIResponse(success=False, error=error_msg)
    except Exception as e:
        logger.error("Google Trends error", keywords=keywords, error=str(e))
        return APIResponse(success=False, error=str(e))


async def _process_response(response: aiohttp.ClientResponse, api_name: str) -> APIResponse:
    """Process HTTP response and return standardized APIResponse."""
    try:
        # Check for rate limiting
        if response.status == 429:
            logger.warning("Rate limited", api=api_name, status=response.status)
            return APIResponse(
                success=False,
                error="Rate limited",
                status_code=response.status,
                rate_limited=True
            )
        
        # Check for other HTTP errors
        if response.status >= 400:
            error_text = await response.text()
            logger.warning(
                "HTTP error",
                api=api_name,
                status=response.status,
                error=error_text[:200]
            )
            return APIResponse(
                success=False,
                error=f"HTTP {response.status}: {error_text[:200]}",
                status_code=response.status
            )
        
        # Parse JSON response
        data = await response.json()
        
        logger.debug("API call successful", api=api_name, status=response.status)
        return APIResponse(
            success=True,
            data=data,
            status_code=response.status
        )
        
    except json.JSONDecodeError as e:
        logger.error("JSON decode error", api=api_name, error=str(e))
        return APIResponse(success=False, error=f"Invalid JSON response: {str(e)}")
    except Exception as e:
        logger.error("Response processing error", api=api_name, error=str(e))
        return APIResponse(success=False, error=str(e))


async def _process_etherscan_response(response: aiohttp.ClientResponse) -> APIResponse:
    """Process Etherscan-specific response format."""
    try:
        if response.status == 429:
            return APIResponse(
                success=False,
                error="Rate limited",
                status_code=response.status,
                rate_limited=True
            )
        
        if response.status >= 400:
            error_text = await response.text()
            return APIResponse(
                success=False,
                error=f"HTTP {response.status}: {error_text[:200]}",
                status_code=response.status
            )
        
        data = await response.json()
        
        # Etherscan returns status in response body
        if data.get("status") == "0":
            error_msg = data.get("message", "Unknown error")
            logger.warning("Etherscan API error", error=error_msg)
            return APIResponse(success=False, error=error_msg, data=data)
        
        logger.debug("Etherscan API call successful", status=response.status)
        return APIResponse(
            success=True,
            data=data.get("result", data),
            status_code=response.status
        )
        
    except Exception as e:
        logger.error("Etherscan response processing error", error=str(e))
        return APIResponse(success=False, error=str(e))
