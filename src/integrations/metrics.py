"""
Metrics collection and monitoring for the Token Analyzer system.
Provides comprehensive performance monitoring, alerting, and observability.
"""

import asyncio
import time
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque
import json

import structlog

from ..core.config import get_settings
from ..core.database import DatabaseManager


logger = structlog.get_logger(__name__)


@dataclass
class MetricPoint:
    """Individual metric data point."""
    timestamp: datetime
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class AgentMetrics:
    """Agent-specific metrics."""
    name: str
    execution_count: int = 0
    success_count: int = 0
    failure_count: int = 0
    total_execution_time_ms: float = 0
    average_execution_time_ms: float = 0
    last_execution: Optional[datetime] = None
    error_rate: float = 0.0
    availability: float = 1.0


class MetricsCollector:
    """
    Collects, stores, and exposes metrics for the token analyzer system.
    
    Features:
    - Agent performance metrics
    - System health metrics
    - Custom business metrics
    - Time-series data storage
    - Alert threshold monitoring
    """
    
    def __init__(self, retention_hours: int = 24):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
        self.retention_hours = retention_hours
        
        # Metrics storage
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.agent_metrics: Dict[str, AgentMetrics] = {}
        self.system_metrics: Dict[str, Any] = {}
        
        # Counters and gauges
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        
        # Alert thresholds
        self.alert_thresholds = {
            "agent_error_rate": 0.1,  # 10% error rate
            "agent_avg_execution_time": 5000,  # 5 seconds
            "memory_usage_percent": 85,  # 85% memory usage
            "cache_hit_rate": 0.5  # 50% cache hit rate
        }
        
        # Database manager for persistent storage
        self.db_manager: Optional[DatabaseManager] = None
        
        # Background tasks
        self.cleanup_task: Optional[asyncio.Task] = None
        self.flush_task: Optional[asyncio.Task] = None
        
        self.running = False
    
    async def start(self) -> None:
        """Start the metrics collector."""
        try:
            self.logger.info("Starting MetricsCollector")
            
            # Initialize database connection
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            
            # Start background tasks
            self.running = True
            self.cleanup_task = asyncio.create_task(self._cleanup_old_metrics())
            self.flush_task = asyncio.create_task(self._flush_metrics_to_db())
            
            self.logger.info("MetricsCollector started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start MetricsCollector: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the metrics collector."""
        try:
            self.logger.info("Stopping MetricsCollector")
            
            self.running = False
            
            # Cancel background tasks
            if self.cleanup_task:
                self.cleanup_task.cancel()
                try:
                    await self.cleanup_task
                except asyncio.CancelledError:
                    pass
            
            if self.flush_task:
                self.flush_task.cancel()
                try:
                    await self.flush_task
                except asyncio.CancelledError:
                    pass
            
            # Final flush to database
            await self._flush_metrics_to_db_once()
            
            # Close database connection
            if self.db_manager:
                await self.db_manager.close()
            
            self.logger.info("MetricsCollector stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping MetricsCollector: {e}")
    
    # ==================== METRIC RECORDING ====================
    
    async def record_agent_execution(
        self,
        agent_name: str,
        method_name: str,
        execution_time_ms: float,
        success: bool,
        error: Optional[str] = None
    ) -> None:
        """Record agent execution metrics."""
        try:
            # Update agent metrics
            if agent_name not in self.agent_metrics:
                self.agent_metrics[agent_name] = AgentMetrics(name=agent_name)
            
            metrics = self.agent_metrics[agent_name]
            metrics.execution_count += 1
            metrics.total_execution_time_ms += execution_time_ms
            metrics.average_execution_time_ms = (
                metrics.total_execution_time_ms / metrics.execution_count
            )
            metrics.last_execution = datetime.utcnow()
            
            if success:
                metrics.success_count += 1
            else:
                metrics.failure_count += 1
            
            metrics.error_rate = metrics.failure_count / metrics.execution_count
            
            # Record time-series metrics
            timestamp = datetime.utcnow()
            labels = {
                "agent": agent_name,
                "method": method_name,
                "success": str(success)
            }
            
            self.metrics["agent_execution_time"].append(
                MetricPoint(timestamp, execution_time_ms, labels)
            )
            
            self.metrics["agent_execution_count"].append(
                MetricPoint(timestamp, 1, labels)
            )
            
            # Update counters
            self.counters[f"agent_{agent_name}_executions"] += 1
            self.counters[f"agent_{agent_name}_{'successes' if success else 'failures'}"] += 1
            
            # Check alerts
            await self._check_agent_alerts(agent_name, metrics)
            
        except Exception as e:
            self.logger.error(f"Error recording agent execution metrics: {e}")
    
    async def record_cache_operation(
        self,
        operation: str,
        hit: bool,
        execution_time_ms: float
    ) -> None:
        """Record cache operation metrics."""
        try:
            timestamp = datetime.utcnow()
            labels = {
                "operation": operation,
                "hit": str(hit)
            }
            
            self.metrics["cache_operation_time"].append(
                MetricPoint(timestamp, execution_time_ms, labels)
            )
            
            self.metrics["cache_operations"].append(
                MetricPoint(timestamp, 1, labels)
            )
            
            # Update counters
            self.counters[f"cache_{operation}_operations"] += 1
            self.counters[f"cache_{'hits' if hit else 'misses'}"] += 1
            
        except Exception as e:
            self.logger.error(f"Error recording cache operation metrics: {e}")
    
    async def record_database_operation(
        self,
        operation: str,
        table: str,
        execution_time_ms: float,
        rows_affected: int = 0
    ) -> None:
        """Record database operation metrics."""
        try:
            timestamp = datetime.utcnow()
            labels = {
                "operation": operation,
                "table": table
            }
            
            self.metrics["db_operation_time"].append(
                MetricPoint(timestamp, execution_time_ms, labels)
            )
            
            self.metrics["db_operations"].append(
                MetricPoint(timestamp, 1, labels)
            )
            
            if rows_affected > 0:
                self.metrics["db_rows_affected"].append(
                    MetricPoint(timestamp, rows_affected, labels)
                )
            
            # Update counters
            self.counters[f"db_{operation}_operations"] += 1
            
        except Exception as e:
            self.logger.error(f"Error recording database operation metrics: {e}")
    
    async def record_api_request(
        self,
        endpoint: str,
        method: str,
        status_code: int,
        execution_time_ms: float
    ) -> None:
        """Record API request metrics."""
        try:
            timestamp = datetime.utcnow()
            labels = {
                "endpoint": endpoint,
                "method": method,
                "status_code": str(status_code)
            }
            
            self.metrics["api_request_time"].append(
                MetricPoint(timestamp, execution_time_ms, labels)
            )
            
            self.metrics["api_requests"].append(
                MetricPoint(timestamp, 1, labels)
            )
            
            # Update counters
            self.counters[f"api_requests"] += 1
            self.counters[f"api_requests_{status_code}"] += 1
            
        except Exception as e:
            self.logger.error(f"Error recording API request metrics: {e}")
    
    async def record_custom_metric(
        self,
        name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """Record custom metric."""
        try:
            timestamp = datetime.utcnow()
            metric_labels = labels or {}
            
            self.metrics[name].append(
                MetricPoint(timestamp, value, metric_labels)
            )
            
        except Exception as e:
            self.logger.error(f"Error recording custom metric {name}: {e}")
    
    def record_token_discovered(self, chain: str, symbol: str) -> None:
        """Record token discovery event."""
        try:
            timestamp = datetime.utcnow()
            
            # Update discovery metrics
            self.metrics["tokens_discovered"].append(
                MetricPoint(timestamp, 1.0, {"chain": chain, "symbol": symbol})
            )
            
            # Update chain-specific metrics
            if chain not in self.metrics:
                self.metrics[f"tokens_discovered_{chain}"] = []
            
            self.metrics[f"tokens_discovered_{chain}"].append(
                MetricPoint(timestamp, 1.0, {"symbol": symbol})
            )
            
        except Exception as e:
            self.logger.error(f"Error recording token discovery for {symbol}: {e}")
    
    def record_validation_result(self, symbol: str, is_valid: bool, risk_score: float) -> None:
        """Record token validation result."""
        try:
            timestamp = datetime.utcnow()
            
            # Record validation outcome
            self.metrics["validations_performed"].append(
                MetricPoint(timestamp, 1.0, {
                    "symbol": symbol, 
                    "result": "valid" if is_valid else "invalid"
                })
            )
            
            # Record risk score
            if is_valid:
                self.metrics["validation_risk_scores"].append(
                    MetricPoint(timestamp, risk_score, {"symbol": symbol})
                )
            
        except Exception as e:
            self.logger.error(f"Error recording validation result for {symbol}: {e}")
    
    def record_analysis_completion(self, symbol: str, analysis_type: str, duration_ms: float) -> None:
        """Record analysis completion event."""
        try:
            timestamp = datetime.utcnow()
            
            self.metrics["analyses_completed"].append(
                MetricPoint(timestamp, 1.0, {
                    "symbol": symbol, 
                    "type": analysis_type
                })
            )
            
            self.metrics["analysis_duration_ms"].append(
                MetricPoint(timestamp, duration_ms, {
                    "symbol": symbol, 
                    "type": analysis_type
                })
            )
            
        except Exception as e:
            self.logger.error(f"Error recording analysis completion for {symbol}: {e}")

    async def record_discovery_source(
        self,
        source: str,
        tokens_found: int,
        success: bool,
        error: str = None
    ) -> None:
        """Record discovery source execution metrics."""
        try:
            timestamp = datetime.now(timezone.utc)

            # Record discovery metrics
            self.metrics["discovery_sources"].append({
                "timestamp": timestamp,
                "source": source,
                "tokens_found": tokens_found,
                "success": success,
                "error": error
            })

            # Update counters
            self.counters[f"discovery_{source}_calls"] += 1
            if success:
                self.counters[f"discovery_{source}_success"] += 1
                self.counters[f"discovery_{source}_tokens"] += tokens_found
            else:
                self.counters[f"discovery_{source}_failures"] += 1

        except Exception as e:
            self.logger.error(f"Error recording discovery source metrics for {source}: {e}")
    
    # ==================== METRIC RETRIEVAL ====================
    
    def get_agent_metrics(self) -> Dict[str, AgentMetrics]:
        """Get all agent metrics."""
        return self.agent_metrics.copy()
    
    def get_agent_metric(self, agent_name: str) -> Optional[AgentMetrics]:
        """Get metrics for specific agent."""
        return self.agent_metrics.get(agent_name)
    
    def get_counters(self) -> Dict[str, float]:
        """Get all counter values."""
        return self.counters.copy()
    
    def get_gauges(self) -> Dict[str, float]:
        """Get all gauge values."""
        return self.gauges.copy()
    
    def get_time_series_metrics(
        self,
        metric_name: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[MetricPoint]:
        """Get time-series metrics within time range."""
        metrics = self.metrics.get(metric_name, deque())
        
        if not start_time and not end_time:
            return list(metrics)
        
        filtered_metrics = []
        for metric in metrics:
            if start_time and metric.timestamp < start_time:
                continue
            if end_time and metric.timestamp > end_time:
                continue
            filtered_metrics.append(metric)
        
        return filtered_metrics
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health metrics."""
        try:
            total_executions = sum(m.execution_count for m in self.agent_metrics.values())
            total_failures = sum(m.failure_count for m in self.agent_metrics.values())
            overall_error_rate = total_failures / total_executions if total_executions > 0 else 0.0

            return {
                "total_executions": total_executions,
                "total_failures": total_failures,
                "error_rate": overall_error_rate,
                "active_agents": len(self.agent_metrics),
                "counters": dict(self.counters),
                "timestamp": datetime.now(timezone.utc)
            }

        except Exception as e:
            self.logger.error(f"Error getting system health: {e}")
            return {"error": str(e), "timestamp": datetime.now(timezone.utc)}

    # ==================== BACKGROUND TASKS ====================

    async def _cleanup_old_metrics(self) -> None:
        """Background task to clean up old metrics."""
        while self.running:
            try:
                cutoff_time = datetime.utcnow() - timedelta(hours=self.retention_hours)

                # Clean up time-series metrics
                for metric_name, metric_deque in self.metrics.items():
                    # Remove old metrics
                    while metric_deque and metric_deque[0].timestamp < cutoff_time:
                        metric_deque.popleft()

                # Sleep for 1 hour before next cleanup
                await asyncio.sleep(3600)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def _flush_metrics_to_db(self) -> None:
        """Background task to flush metrics to database."""
        while self.running:
            try:
                await self._flush_metrics_to_db_once()
                # Flush every 5 minutes
                await asyncio.sleep(300)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in flush task: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def _flush_metrics_to_db_once(self) -> None:
        """Flush current metrics to database once."""
        if not self.db_manager:
            return

        try:
            # Prepare metrics data for database storage
            metrics_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "agent_metrics": {
                    name: {
                        "execution_count": metrics.execution_count,
                        "success_count": metrics.success_count,
                        "failure_count": metrics.failure_count,
                        "average_execution_time_ms": metrics.average_execution_time_ms,
                        "error_rate": metrics.error_rate,
                        "last_execution": metrics.last_execution.isoformat() if metrics.last_execution else None
                    }
                    for name, metrics in self.agent_metrics.items()
                },
                "counters": dict(self.counters),
                "gauges": dict(self.gauges)
            }

            # Store in database (simplified - would need proper table structure)
            # For now, just log the flush operation
            self.logger.debug(f"Flushed {len(self.agent_metrics)} agent metrics to database")

        except Exception as e:
            self.logger.error(f"Error flushing metrics to database: {e}")

    async def _check_agent_alerts(self, agent_name: str, metrics: AgentMetrics) -> None:
        """Check if agent metrics exceed alert thresholds."""
        try:
            alerts = []

            # Check error rate
            if metrics.error_rate > self.alert_thresholds["agent_error_rate"]:
                alerts.append(f"High error rate: {metrics.error_rate:.2%}")

            # Check average execution time
            if metrics.average_execution_time_ms > self.alert_thresholds["agent_avg_execution_time"]:
                alerts.append(f"High execution time: {metrics.average_execution_time_ms:.0f}ms")

            # Log alerts
            if alerts:
                self.logger.warning(f"Agent {agent_name} alerts: {', '.join(alerts)}")

        except Exception as e:
            self.logger.error(f"Error checking agent alerts: {e}")
