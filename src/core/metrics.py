"""
Metrics collector for the token analyzer system.
"""

import asyncio
import time
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


class MetricsCollector:
    """Simple metrics collector for testing."""
    
    def __init__(self):
        self.initialized = False
        self.metrics = {}
        self.start_time = time.time()
    
    async def initialize(self) -> None:
        """Initialize metrics collection."""
        logger.info("Initializing metrics collector")
        self.initialized = True
        self.start_time = time.time()
    
    async def shutdown(self) -> None:
        """Shutdown metrics collection."""
        logger.info("Shutting down metrics collector")
        self.initialized = False
    
    def record_metric(self, name: str, value: Any, tags: Dict[str, str] = None) -> None:
        """Record a metric."""
        if not self.initialized:
            return
        
        self.metrics[name] = {
            "value": value,
            "timestamp": time.time(),
            "tags": tags or {}
        }
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get all metrics."""
        return {
            "uptime": time.time() - self.start_time,
            "metrics": self.metrics
        }
