"""
High-performance caching system using Redis.
Provides intelligent caching strategies for different data types.
"""

import asyncio
import json
import pickle
from datetime import datetime, timezone
from functools import wraps
from typing import Any, TypeVar

import redis.asyncio as redis
import structlog
from pydantic import BaseModel

from .config import config

logger = structlog.get_logger(__name__)

T = TypeVar("T")


class CacheStats(BaseModel):
    """Cache statistics model."""

    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    errors: int = 0

    @property
    def hit_rate(self) -> float:
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class CacheConfig(BaseModel):
    """Cache configuration for different data types."""

    ttl: int  # Time to live in seconds
    serializer: str = "json"  # json, pickle, or raw
    compression: bool = False
    namespace: str = "default"


class CacheManager:
    """
    Redis-based cache manager with intelligent strategies.

    Features:
    - Multiple serialization formats
    - Namespace support
    - TTL management
    - Circuit breaker pattern
    - Statistics tracking
    - Batch operations
    """

    def __init__(self):
        self.redis_client: redis.Redis | None = None
        self.stats = CacheStats()
        self._circuit_breaker_failures = 0
        self._circuit_breaker_threshold = 5
        self._circuit_breaker_timeout = 60
        self._circuit_breaker_last_failure = None

        # Optimized cache configurations for production performance
        self.cache_configs = {
            # Token data - 5 minutes (frequent updates for price changes)
            "tokens": CacheConfig(
                ttl=300, namespace="tokens"
            ),
            # Market data - 1 minute (very frequent updates)
            "market_data": CacheConfig(
                ttl=60, namespace="market"
            ),
            # Chain data - 5 minutes (moderate update frequency)
            "chain_data": CacheConfig(
                ttl=300, namespace="chain"
            ),
            # Analysis results - 1 hour (expensive to compute, can be cached longer)
            "analysis": CacheConfig(ttl=3600, namespace="analysis"),
            # Trends - 15 minutes (balance between freshness and performance)
            "trends": CacheConfig(ttl=900, namespace="trends"),
            # Session data - 1 hour
            "session": CacheConfig(
                ttl=3600, namespace="session", serializer="pickle"
            ),
            # Discovery results - 10 minutes (new tokens appear frequently)
            "discovery": CacheConfig(ttl=600, namespace="discovery"),
            # API responses - 2 minutes (balance API rate limits with freshness)
            "api_responses": CacheConfig(ttl=120, namespace="api"),
            # Security analysis - 30 minutes (security data changes less frequently)
            "security": CacheConfig(ttl=1800, namespace="security"),
        }

    async def initialize(self) -> None:
        """Initialize Redis connection."""
        try:
            redis_config = config.get_redis_config()

            self.redis_client = redis.from_url(
                redis_config["url"],
                password=redis_config["password"],
                db=redis_config["db"],
                max_connections=redis_config["max_connections"],
                encoding=redis_config["encoding"],
                decode_responses=redis_config["decode_responses"],
                socket_timeout=10,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
            )

            # Test connection
            await self.redis_client.ping()

            logger.info("Cache manager initialized successfully")

        except Exception as e:
            logger.error("Failed to initialize cache manager", error=str(e))
            raise

    async def shutdown(self) -> None:
        """Shutdown the cache manager."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            logger.info("Cache manager shutdown complete")
        except Exception as e:
            logger.error("Error during cache shutdown", error=str(e))

    async def close(self) -> None:
        """Close Redis connection."""
        if self.redis_client:
            try:
                await self.redis_client.close()
                self.redis_client = None
                logger.info("Cache manager closed")
            except Exception as e:
                logger.error("Error closing cache manager", error=str(e))

    def _is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open."""
        if self._circuit_breaker_failures < self._circuit_breaker_threshold:
            return False

        if self._circuit_breaker_last_failure is None:
            return False

        time_since_failure = (
            datetime.now(timezone.utc) - self._circuit_breaker_last_failure
        ).total_seconds()
        return time_since_failure < self._circuit_breaker_timeout

    def _handle_cache_error(self, operation: str, error: Exception) -> None:
        """Handle cache operation errors."""
        self.stats.errors += 1
        self._circuit_breaker_failures += 1
        self._circuit_breaker_last_failure = datetime.now(timezone.utc)

        logger.warning(
            "Cache operation failed",
            operation=operation,
            error=str(error),
            failures=self._circuit_breaker_failures,
        )

    def _reset_circuit_breaker(self) -> None:
        """Reset circuit breaker on successful operation."""
        if self._circuit_breaker_failures > 0:
            self._circuit_breaker_failures = 0
            self._circuit_breaker_last_failure = None
            logger.info("Circuit breaker reset")

    def _get_cache_config(self, data_type: str) -> CacheConfig:
        """Get cache configuration for data type."""
        return self.cache_configs.get(data_type, self.cache_configs["tokens"])

    def _make_key(self, key: str, namespace: str = "default") -> str:
        """Create namespaced cache key."""
        return f"{config.system.app_name}:{namespace}:{key}"

    def _serialize_value(self, value: Any, serializer: str) -> bytes:
        """Serialize value based on serializer type."""
        if serializer == "json":
            return json.dumps(value, default=str).encode("utf-8")
        elif serializer == "pickle":
            return pickle.dumps(value)
        elif serializer == "raw":
            return value if isinstance(value, bytes) else str(value).encode("utf-8")
        else:
            raise ValueError(f"Unknown serializer: {serializer}")

    def _deserialize_value(self, value, serializer: str) -> Any:
        """Deserialize value based on serializer type."""
        if serializer == "json":
            # Handle both string and bytes input
            if isinstance(value, bytes):
                return json.loads(value.decode("utf-8"))
            else:
                return json.loads(value)
        elif serializer == "pickle":
            # Pickle requires bytes
            if isinstance(value, str):
                value = value.encode("utf-8")
            return pickle.loads(value)
        elif serializer == "raw":
            return value
        else:
            raise ValueError(f"Unknown serializer: {serializer}")

    # ==================== CORE CACHE OPERATIONS ====================

    async def get(
        self, key: str, data_type: str = "default", default: Any = None
    ) -> Any:
        """Get value from cache."""
        if self._is_circuit_breaker_open():
            self.stats.misses += 1
            return default

        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)

            value = await self.redis_client.get(cache_key)

            if value is None:
                self.stats.misses += 1
                return default

            self.stats.hits += 1
            self._reset_circuit_breaker()

            return self._deserialize_value(value, cache_config.serializer)

        except Exception as e:
            self._handle_cache_error("get", e)
            return default

    async def set(
        self, key: str, value: Any, data_type: str = "default", ttl: int | None = None
    ) -> bool:
        """Set value in cache."""
        if self._is_circuit_breaker_open():
            return False

        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            cache_ttl = ttl or cache_config.ttl

            serialized_value = self._serialize_value(value, cache_config.serializer)

            result = await self.redis_client.setex(
                cache_key, cache_ttl, serialized_value
            )

            self.stats.sets += 1
            self._reset_circuit_breaker()

            return result

        except Exception as e:
            self._handle_cache_error("set", e)
            return False

    async def delete(self, key: str, data_type: str = "default") -> bool:
        """Delete value from cache."""
        if self._is_circuit_breaker_open():
            return False

        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)

            result = await self.redis_client.delete(cache_key)

            self.stats.deletes += 1
            self._reset_circuit_breaker()

            return bool(result)

        except Exception as e:
            self._handle_cache_error("delete", e)
            return False

    async def exists(self, key: str, data_type: str = "default") -> bool:
        """Check if key exists in cache."""
        if self._is_circuit_breaker_open():
            return False

        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)

            result = await self.redis_client.exists(cache_key)
            self._reset_circuit_breaker()

            return bool(result)

        except Exception as e:
            self._handle_cache_error("exists", e)
            return False

    # ==================== BATCH OPERATIONS ====================

    async def get_many(
        self, keys: list[str], data_type: str = "default"
    ) -> dict[str, Any]:
        """Get multiple values from cache."""
        if self._is_circuit_breaker_open():
            self.stats.misses += len(keys)
            return {}

        try:
            cache_config = self._get_cache_config(data_type)
            cache_keys = [self._make_key(key, cache_config.namespace) for key in keys]

            values = await self.redis_client.mget(cache_keys)

            result = {}
            for _i, (original_key, value) in enumerate(zip(keys, values, strict=False)):
                if value is not None:
                    try:
                        result[original_key] = self._deserialize_value(
                            value, cache_config.serializer
                        )
                        self.stats.hits += 1
                    except Exception as e:
                        logger.warning(
                            f"Failed to deserialize cached value for {original_key}: {e}"
                        )
                        self.stats.misses += 1
                else:
                    self.stats.misses += 1

            self._reset_circuit_breaker()
            return result

        except Exception as e:
            self._handle_cache_error("get_many", e)
            self.stats.misses += len(keys)
            return {}

    async def set_many(
        self, items: dict[str, Any], data_type: str = "default", ttl: int | None = None
    ) -> bool:
        """Set multiple values in cache."""
        if self._is_circuit_breaker_open():
            return False

        try:
            cache_config = self._get_cache_config(data_type)
            cache_ttl = ttl or cache_config.ttl

            # Use pipeline for efficiency
            async with self.redis_client.pipeline() as pipe:
                for key, value in items.items():
                    cache_key = self._make_key(key, cache_config.namespace)
                    serialized_value = self._serialize_value(
                        value, cache_config.serializer
                    )
                    pipe.setex(cache_key, cache_ttl, serialized_value)

                await pipe.execute()

            self.stats.sets += len(items)
            self._reset_circuit_breaker()

            return True

        except Exception as e:
            self._handle_cache_error("set_many", e)
            return False

    # ==================== ADVANCED OPERATIONS ====================

    async def increment(
        self, key: str, amount: int = 1, data_type: str = "default"
    ) -> int:
        """Increment a numeric value."""
        if self._is_circuit_breaker_open():
            return 0

        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)

            result = await self.redis_client.incr(cache_key, amount)

            # Set TTL if this is a new key
            await self.redis_client.expire(cache_key, cache_config.ttl)

            self._reset_circuit_breaker()
            return result

        except Exception as e:
            self._handle_cache_error("increment", e)
            return 0

    async def add_to_set(
        self, key: str, values: Any | list[Any], data_type: str = "default"
    ) -> int:
        """Add values to a set."""
        if self._is_circuit_breaker_open():
            return 0

        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)

            if not isinstance(values, list):
                values = [values]

            # Serialize values
            serialized_values = [
                self._serialize_value(value, cache_config.serializer)
                for value in values
            ]

            result = await self.redis_client.sadd(cache_key, *serialized_values)

            # Set TTL
            await self.redis_client.expire(cache_key, cache_config.ttl)

            self._reset_circuit_breaker()
            return result

        except Exception as e:
            self._handle_cache_error("add_to_set", e)
            return 0

    async def get_set_members(self, key: str, data_type: str = "default") -> list[Any]:
        """Get all members of a set."""
        if self._is_circuit_breaker_open():
            return []

        try:
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)

            members = await self.redis_client.smembers(cache_key)

            result = [
                self._deserialize_value(member, cache_config.serializer)
                for member in members
            ]

            self._reset_circuit_breaker()
            return result

        except Exception as e:
            self._handle_cache_error("get_set_members", e)
            return []

    # ==================== SMART CACHE INVALIDATION ====================

    async def invalidate_by_pattern(self, pattern: str, namespace: str = "default") -> int:
        """
        Invalidate cache entries matching a pattern.

        Args:
            pattern: Redis pattern (e.g., "token:*", "market:ETH:*")
            namespace: Cache namespace

        Returns:
            Number of keys invalidated
        """
        if self._is_circuit_breaker_open():
            return 0

        try:
            full_pattern = f"{namespace}:{pattern}"
            keys = await self.redis_client.keys(full_pattern)

            if keys:
                deleted = await self.redis_client.delete(*keys)
                self.stats.deletes += deleted
                logger.info("Invalidated cache entries", pattern=full_pattern, count=deleted)
                return deleted

            return 0

        except Exception as e:
            self._handle_cache_error("invalidate_by_pattern", e)
            return 0

    async def invalidate_stale_data(self, data_type: str, max_age_seconds: int) -> int:
        """
        Invalidate cache entries older than max_age_seconds based on data freshness.

        Args:
            data_type: Type of data to check
            max_age_seconds: Maximum age in seconds

        Returns:
            Number of keys invalidated
        """
        if self._is_circuit_breaker_open():
            return 0

        try:
            cache_config = self._get_cache_config(data_type)
            pattern = f"{cache_config.namespace}:*"
            keys = await self.redis_client.keys(pattern)

            invalidated = 0

            for key in keys:
                # Get key creation time (if available)
                key_info = await self.redis_client.object("idletime", key)
                if key_info is not None:
                    # Calculate age based on idle time
                    age_seconds = key_info
                    if age_seconds > max_age_seconds:
                        await self.redis_client.delete(key)
                        invalidated += 1

            if invalidated > 0:
                self.stats.deletes += invalidated
                logger.info("Invalidated stale cache entries",
                          data_type=data_type,
                          max_age=max_age_seconds,
                          count=invalidated)

            return invalidated

        except Exception as e:
            self._handle_cache_error("invalidate_stale_data", e)
            return 0

    async def refresh_cache_entry(self, key: str, fetch_func, data_type: str = "default") -> Any:
        """
        Force refresh a cache entry by fetching new data.

        Args:
            key: Cache key
            fetch_func: Function to fetch fresh data
            data_type: Type of data

        Returns:
            Fresh data
        """
        try:
            # Delete existing entry
            cache_config = self._get_cache_config(data_type)
            cache_key = self._make_key(key, cache_config.namespace)
            await self.redis_client.delete(cache_key)

            # Fetch and cache fresh data
            fresh_data = await fetch_func()
            await self.set(key, fresh_data, data_type)

            logger.debug("Refreshed cache entry", key=key, data_type=data_type)
            return fresh_data

        except Exception as e:
            logger.error("Failed to refresh cache entry", key=key, error=str(e))
            # Try to fetch without caching
            try:
                return await fetch_func()
            except Exception:
                return None

    # ==================== CACHE WARMING ====================

    async def warm_cache(self, warming_functions: dict[str, callable]) -> dict[str, int]:
        """
        Pre-populate cache with frequently accessed data.

        Args:
            warming_functions: Dict of {data_type: async_function} to fetch data

        Returns:
            Dict of {data_type: count} of warmed entries
        """
        results = {}

        for data_type, fetch_func in warming_functions.items():
            try:
                logger.info("Starting cache warming", data_type=data_type)

                # Fetch data and populate cache
                data = await fetch_func()

                if isinstance(data, dict):
                    # Multiple items to cache
                    await self.set_many(data, data_type)
                    results[data_type] = len(data)
                elif isinstance(data, list):
                    # List of items - create keys based on index or item properties
                    cache_items = {}
                    for i, item in enumerate(data):
                        if isinstance(item, dict) and 'address' in item:
                            key = item['address']
                        elif isinstance(item, dict) and 'symbol' in item:
                            key = item['symbol']
                        else:
                            key = str(i)
                        cache_items[key] = item

                    await self.set_many(cache_items, data_type)
                    results[data_type] = len(cache_items)
                else:
                    # Single item
                    await self.set("default", data, data_type)
                    results[data_type] = 1

                logger.info("Cache warming completed",
                          data_type=data_type,
                          count=results[data_type])

            except Exception as e:
                logger.error("Cache warming failed",
                           data_type=data_type,
                           error=str(e))
                results[data_type] = 0

        return results

    async def warm_top_tokens(self, db_manager, limit: int = 100) -> int:
        """
        Warm cache with top tokens by market cap or volume.

        Args:
            db_manager: Database manager instance
            limit: Number of top tokens to cache

        Returns:
            Number of tokens cached
        """
        try:
            # Fetch top tokens from database
            query = """
                SELECT address, symbol, name, chain, price_usd, market_cap_usd, volume_24h_usd
                FROM tokens
                WHERE market_cap_usd > 0
                ORDER BY market_cap_usd DESC
                LIMIT ?
            """

            results = await db_manager.execute_query(query, (limit,))

            if results:
                # Convert to cache format
                cache_items = {}
                for row in results:
                    token_data = {
                        "address": row[0],
                        "symbol": row[1],
                        "name": row[2],
                        "chain": row[3],
                        "price_usd": row[4],
                        "market_cap_usd": row[5],
                        "volume_24h_usd": row[6]
                    }
                    cache_items[row[0]] = token_data  # Use address as key

                # Cache the tokens
                await self.set_many(cache_items, "tokens")

                logger.info("Warmed cache with top tokens", count=len(cache_items))
                return len(cache_items)

            return 0

        except Exception as e:
            logger.error("Failed to warm top tokens cache", error=str(e))
            return 0

    # ==================== CACHE MONITORING ====================

    def get_stats(self) -> CacheStats:
        """Get current cache statistics."""
        return self.stats

    def reset_stats(self) -> None:
        """Reset cache statistics."""
        self.stats = CacheStats()
        logger.info("Cache statistics reset")

    async def get_cache_info(self) -> dict[str, Any]:
        """Get comprehensive cache information."""
        try:
            # Get Redis info
            redis_info = await self.redis_client.info()

            # Get memory usage
            memory_info = {
                "used_memory": redis_info.get("used_memory", 0),
                "used_memory_human": redis_info.get("used_memory_human", "0B"),
                "used_memory_peak": redis_info.get("used_memory_peak", 0),
                "used_memory_peak_human": redis_info.get("used_memory_peak_human", "0B"),
            }

            # Get key counts by namespace
            namespace_counts = {}
            for data_type, config in self.cache_configs.items():
                pattern = f"{config.namespace}:*"
                keys = await self.redis_client.keys(pattern)
                namespace_counts[data_type] = len(keys)

            return {
                "stats": self.stats.model_dump(),
                "memory": memory_info,
                "namespace_counts": namespace_counts,
                "circuit_breaker": {
                    "failures": self._circuit_breaker_failures,
                    "is_open": self._is_circuit_breaker_open(),
                    "last_failure": self._circuit_breaker_last_failure.isoformat() if self._circuit_breaker_last_failure else None
                }
            }

        except Exception as e:
            logger.error("Failed to get cache info", error=str(e))
            return {
                "stats": self.stats.model_dump(),
                "error": str(e)
            }

    # ==================== CACHE PATTERNS ====================

    async def get_or_set(
        self, key: str, fetch_func, data_type: str = "default", ttl: int | None = None
    ) -> Any:
        """Get value from cache or fetch and cache it."""
        # Try to get from cache first
        value = await self.get(key, data_type)

        if value is not None:
            return value

        # Fetch the value
        try:
            value = (
                await fetch_func()
                if asyncio.iscoroutinefunction(fetch_func)
                else fetch_func()
            )

            # Cache the result
            await self.set(key, value, data_type, ttl)

            return value

        except Exception as e:
            logger.error("Failed to fetch value for cache", key=key, error=str(e))
            raise

    def cached(
        self,
        key_template: str = None,
        data_type: str = "default",
        ttl: int | None = None,
    ):
        """Decorator for caching function results."""

        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Generate cache key
                if key_template:
                    cache_key = key_template.format(*args, **kwargs)
                else:
                    cache_key = (
                        f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
                    )

                return await self.get_or_set(
                    cache_key, lambda: func(*args, **kwargs), data_type, ttl
                )

            return wrapper

        return decorator

    # ==================== MAINTENANCE OPERATIONS ====================

    async def clear_namespace(self, namespace: str) -> int:
        """Clear all keys in a namespace."""
        if self._is_circuit_breaker_open():
            return 0

        try:
            pattern = self._make_key("*", namespace)
            keys = await self.redis_client.keys(pattern)

            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info(f"Cleared {deleted} keys from namespace {namespace}")
                return deleted

            return 0

        except Exception as e:
            self._handle_cache_error("clear_namespace", e)
            return 0

    async def get_cache_info(self) -> dict[str, Any]:
        """Get cache information and statistics."""
        try:
            info = await self.redis_client.info()

            return {
                "stats": self.stats.model_dump(),
                "redis_info": {
                    "used_memory": info.get("used_memory_human", "unknown"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0),
                    "keyspace_hits": info.get("keyspace_hits", 0),
                    "keyspace_misses": info.get("keyspace_misses", 0),
                },
                "circuit_breaker": {
                    "failures": self._circuit_breaker_failures,
                    "is_open": self._is_circuit_breaker_open(),
                    "last_failure": self._circuit_breaker_last_failure.isoformat()
                    if self._circuit_breaker_last_failure
                    else None,
                },
            }

        except Exception as e:
            logger.error("Failed to get cache info", error=str(e))
            return {"error": str(e)}

    async def health_check(self) -> bool:
        """Perform cache health check."""
        try:
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error("Cache health check failed", error=str(e))
            return False
