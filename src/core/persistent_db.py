"""
Persistent Database Architecture with Time-Series, Document, and Caching

This module implements a comprehensive database architecture:
- Time-Series DB: InfluxDB for price/volume data and metrics
- Document DB: MongoDB for analysis reports and unstructured data
- Relational DB: PostgreSQL for structured data and relationships
- Cache Layer: Redis with intelligent caching strategies
- Data Lake: Parquet files for historical data and analytics
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
import json
import pandas as pd
from pathlib import Path

# Time-series database
try:
    from influxdb_client import InfluxDBClient, Point
    from influxdb_client.client.write_api import SYNCHRONOUS
    INFLUXDB_AVAILABLE = True
except ImportError:
    INFLUXDB_AVAILABLE = False

# Document database
try:
    from motor.motor_asyncio import AsyncIOMotorClient
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False

# Relational database
try:
    import asyncpg
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

from .cache import CacheManager
from .database import DatabaseManager
from .config import config


logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """Database types for different data patterns."""
    TIME_SERIES = "time_series"      # Price data, metrics, events
    DOCUMENT = "document"            # Analysis reports, unstructured data
    RELATIONAL = "relational"        # Structured data, relationships
    CACHE = "cache"                  # Fast access, temporary data
    DATA_LAKE = "data_lake"          # Historical data, analytics


@dataclass
class TimeSeriesPoint:
    """Time-series data point."""
    measurement: str
    tags: Dict[str, str]
    fields: Dict[str, Union[float, int, str, bool]]
    timestamp: datetime


@dataclass
class DocumentRecord:
    """Document database record."""
    collection: str
    document_id: str
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


class PersistentDatabaseManager:
    """
    Comprehensive database manager with multiple storage backends.
    
    Features:
    - Time-series data for prices, volumes, and metrics
    - Document storage for analysis reports and unstructured data
    - Relational data for structured information and relationships
    - Intelligent caching with multiple strategies
    - Data lake for historical analytics
    - Automatic data partitioning and archiving
    - Cross-database queries and joins
    """

    def __init__(self):
        # Core components
        self.cache_manager = CacheManager()
        self.duckdb_manager = DatabaseManager()
        
        # Time-series database (InfluxDB)
        self.influx_client: Optional[InfluxDBClient] = None
        self.influx_write_api = None
        self.influx_query_api = None
        
        # Document database (MongoDB)
        self.mongo_client: Optional[AsyncIOMotorClient] = None
        self.mongo_db = None
        
        # Relational database (PostgreSQL)
        self.postgres_pool: Optional[asyncpg.Pool] = None
        
        # Data lake (Parquet files)
        self.data_lake_path = Path("data/lake")
        self.data_lake_path.mkdir(parents=True, exist_ok=True)
        
        # Database availability flags
        self.influxdb_enabled = INFLUXDB_AVAILABLE
        self.mongodb_enabled = MONGODB_AVAILABLE
        self.postgresql_enabled = POSTGRESQL_AVAILABLE

    async def initialize(self):
        """Initialize all database connections."""
        logger.info("Initializing persistent database architecture...")
        
        try:
            # Initialize cache manager
            await self.cache_manager.initialize()
            
            # Initialize DuckDB
            await self.duckdb_manager.initialize()
            
            # Initialize time-series database
            if self.influxdb_enabled:
                await self._initialize_influxdb()
            
            # Initialize document database
            if self.mongodb_enabled:
                await self._initialize_mongodb()
            
            # Initialize relational database
            if self.postgresql_enabled:
                await self._initialize_postgresql()
            
            # Create schemas and indexes
            await self._create_schemas()
            
            logger.info("Persistent database architecture initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize persistent database: {e}")
            raise

    async def shutdown(self):
        """Shutdown all database connections."""
        logger.info("Shutting down persistent database architecture...")
        
        try:
            # Close cache manager
            await self.cache_manager.close()
            
            # Close DuckDB
            await self.duckdb_manager.close()
            
            # Close InfluxDB
            if self.influx_client:
                self.influx_client.close()
            
            # Close MongoDB
            if self.mongo_client:
                self.mongo_client.close()
            
            # Close PostgreSQL
            if self.postgres_pool:
                await self.postgres_pool.close()
            
            logger.info("Persistent database architecture shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during database shutdown: {e}")

    async def _initialize_influxdb(self):
        """Initialize InfluxDB for time-series data."""
        try:
            influx_config = {
                "url": "http://localhost:8086",
                "token": "your-influxdb-token",
                "org": "crypto-analyzer",
                "bucket": "token-data"
            }
            
            self.influx_client = InfluxDBClient(
                url=influx_config["url"],
                token=influx_config["token"],
                org=influx_config["org"]
            )
            
            self.influx_write_api = self.influx_client.write_api(write_options=SYNCHRONOUS)
            self.influx_query_api = self.influx_client.query_api()
            
            logger.info("InfluxDB initialized for time-series data")
            
        except Exception as e:
            logger.warning(f"InfluxDB initialization failed: {e}")
            self.influxdb_enabled = False

    async def _initialize_mongodb(self):
        """Initialize MongoDB for document storage."""
        try:
            mongo_config = {
                "url": "mongodb://localhost:27017",
                "database": "crypto_analyzer"
            }
            
            self.mongo_client = AsyncIOMotorClient(mongo_config["url"])
            self.mongo_db = self.mongo_client[mongo_config["database"]]
            
            # Test connection
            await self.mongo_client.admin.command('ping')
            
            logger.info("MongoDB initialized for document storage")
            
        except Exception as e:
            logger.warning(f"MongoDB initialization failed: {e}")
            self.mongodb_enabled = False

    async def _initialize_postgresql(self):
        """Initialize PostgreSQL for relational data."""
        try:
            postgres_config = {
                "host": "localhost",
                "port": 5432,
                "database": "crypto_analyzer",
                "user": "postgres",
                "password": "password"
            }
            
            self.postgres_pool = await asyncpg.create_pool(
                host=postgres_config["host"],
                port=postgres_config["port"],
                database=postgres_config["database"],
                user=postgres_config["user"],
                password=postgres_config["password"],
                min_size=5,
                max_size=20
            )
            
            logger.info("PostgreSQL initialized for relational data")
            
        except Exception as e:
            logger.warning(f"PostgreSQL initialization failed: {e}")
            self.postgresql_enabled = False

    async def _create_schemas(self):
        """Create database schemas and indexes."""
        try:
            # Create PostgreSQL schemas
            if self.postgresql_enabled:
                await self._create_postgresql_schemas()
            
            # Create MongoDB collections and indexes
            if self.mongodb_enabled:
                await self._create_mongodb_schemas()
            
            logger.info("Database schemas created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create schemas: {e}")
            raise

    async def _create_postgresql_schemas(self):
        """Create PostgreSQL schemas for relational data."""
        schemas = [
            """
            CREATE TABLE IF NOT EXISTS token_metadata (
                address VARCHAR(42) PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                name VARCHAR(100),
                chain VARCHAR(20) NOT NULL,
                decimals INTEGER,
                total_supply BIGINT,
                contract_verified BOOLEAN DEFAULT FALSE,
                audit_status VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS analysis_sessions (
                session_id UUID PRIMARY KEY,
                token_address VARCHAR(42) NOT NULL,
                analysis_type VARCHAR(50) NOT NULL,
                status VARCHAR(20) NOT NULL,
                started_at TIMESTAMP NOT NULL,
                completed_at TIMESTAMP,
                results JSONB,
                confidence_score FLOAT,
                FOREIGN KEY (token_address) REFERENCES token_metadata(address)
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS risk_assessments (
                id SERIAL PRIMARY KEY,
                token_address VARCHAR(42) NOT NULL,
                risk_type VARCHAR(50) NOT NULL,
                risk_score FLOAT NOT NULL,
                risk_factors JSONB,
                assessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (token_address) REFERENCES token_metadata(address)
            )
            """
        ]
        
        async with self.postgres_pool.acquire() as conn:
            for schema in schemas:
                await conn.execute(schema)

    async def _create_mongodb_schemas(self):
        """Create MongoDB collections and indexes."""
        collections = [
            "analysis_reports",
            "sentiment_data",
            "web_scraping_results",
            "research_papers",
            "social_media_posts"
        ]
        
        for collection_name in collections:
            collection = self.mongo_db[collection_name]
            
            # Create indexes
            await collection.create_index("token_address")
            await collection.create_index("timestamp")
            await collection.create_index([("token_address", 1), ("timestamp", -1)])

    # ==================== TIME-SERIES OPERATIONS ====================

    async def write_time_series(self, points: List[TimeSeriesPoint]) -> bool:
        """Write time-series data points."""
        if not self.influxdb_enabled:
            logger.warning("InfluxDB not available, skipping time-series write")
            return False
        
        try:
            influx_points = []
            for point in points:
                influx_point = Point(point.measurement)
                
                # Add tags
                for tag_key, tag_value in point.tags.items():
                    influx_point = influx_point.tag(tag_key, tag_value)
                
                # Add fields
                for field_key, field_value in point.fields.items():
                    influx_point = influx_point.field(field_key, field_value)
                
                # Set timestamp
                influx_point = influx_point.time(point.timestamp)
                influx_points.append(influx_point)
            
            self.influx_write_api.write(bucket="token-data", record=influx_points)
            return True
            
        except Exception as e:
            logger.error(f"Failed to write time-series data: {e}")
            return False

    async def query_time_series(
        self,
        measurement: str,
        start_time: datetime,
        end_time: datetime,
        filters: Optional[Dict[str, str]] = None
    ) -> List[Dict[str, Any]]:
        """Query time-series data."""
        if not self.influxdb_enabled:
            logger.warning("InfluxDB not available, returning empty results")
            return []
        
        try:
            # Build query
            query = f'''
                from(bucket: "token-data")
                |> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})
                |> filter(fn: (r) => r._measurement == "{measurement}")
            '''
            
            # Add filters
            if filters:
                for key, value in filters.items():
                    query += f'|> filter(fn: (r) => r.{key} == "{value}")'
            
            # Execute query
            result = self.influx_query_api.query(query)
            
            # Convert to list of dictionaries
            data = []
            for table in result:
                for record in table.records:
                    data.append({
                        "time": record.get_time(),
                        "measurement": record.get_measurement(),
                        "field": record.get_field(),
                        "value": record.get_value(),
                        **{k: v for k, v in record.values.items() if k.startswith("tag_")}
                    })
            
            return data
            
        except Exception as e:
            logger.error(f"Failed to query time-series data: {e}")
            return []

    # ==================== DOCUMENT OPERATIONS ====================

    async def store_document(self, record: DocumentRecord) -> bool:
        """Store document in MongoDB."""
        if not self.mongodb_enabled:
            logger.warning("MongoDB not available, skipping document storage")
            return False

        try:
            collection = self.mongo_db[record.collection]

            document = {
                "_id": record.document_id,
                "data": record.data,
                "metadata": record.metadata,
                "created_at": record.created_at,
                "updated_at": record.updated_at
            }

            await collection.replace_one(
                {"_id": record.document_id},
                document,
                upsert=True
            )

            return True

        except Exception as e:
            logger.error(f"Failed to store document: {e}")
            return False

    async def get_document(
        self, collection: str, document_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get document from MongoDB."""
        if not self.mongodb_enabled:
            logger.warning("MongoDB not available, returning None")
            return None

        try:
            collection_obj = self.mongo_db[collection]
            document = await collection_obj.find_one({"_id": document_id})
            return document

        except Exception as e:
            logger.error(f"Failed to get document: {e}")
            return None

    async def query_documents(
        self,
        collection: str,
        query: Dict[str, Any],
        limit: int = 100,
        sort: Optional[List[tuple]] = None
    ) -> List[Dict[str, Any]]:
        """Query documents from MongoDB."""
        if not self.mongodb_enabled:
            logger.warning("MongoDB not available, returning empty list")
            return []

        try:
            collection_obj = self.mongo_db[collection]
            cursor = collection_obj.find(query).limit(limit)

            if sort:
                cursor = cursor.sort(sort)

            documents = await cursor.to_list(length=limit)
            return documents

        except Exception as e:
            logger.error(f"Failed to query documents: {e}")
            return []

    # ==================== RELATIONAL OPERATIONS ====================

    async def execute_sql(
        self, query: str, params: Optional[tuple] = None
    ) -> List[Dict[str, Any]]:
        """Execute SQL query on PostgreSQL."""
        if not self.postgresql_enabled:
            logger.warning("PostgreSQL not available, returning empty list")
            return []

        try:
            async with self.postgres_pool.acquire() as conn:
                if params:
                    rows = await conn.fetch(query, *params)
                else:
                    rows = await conn.fetch(query)

                return [dict(row) for row in rows]

        except Exception as e:
            logger.error(f"Failed to execute SQL query: {e}")
            return []

    async def insert_relational(
        self, table: str, data: Dict[str, Any]
    ) -> bool:
        """Insert data into PostgreSQL table."""
        if not self.postgresql_enabled:
            logger.warning("PostgreSQL not available, skipping insert")
            return False

        try:
            columns = list(data.keys())
            values = list(data.values())
            placeholders = ", ".join([f"${i+1}" for i in range(len(values))])

            query = f"""
                INSERT INTO {table} ({', '.join(columns)})
                VALUES ({placeholders})
                ON CONFLICT DO NOTHING
            """

            async with self.postgres_pool.acquire() as conn:
                await conn.execute(query, *values)

            return True

        except Exception as e:
            logger.error(f"Failed to insert relational data: {e}")
            return False

    # ==================== DATA LAKE OPERATIONS ====================

    async def store_in_data_lake(
        self,
        dataset_name: str,
        data: pd.DataFrame,
        partition_by: Optional[str] = None
    ) -> bool:
        """Store data in data lake as Parquet files."""
        try:
            # Create dataset directory
            dataset_path = self.data_lake_path / dataset_name
            dataset_path.mkdir(parents=True, exist_ok=True)

            if partition_by and partition_by in data.columns:
                # Partition data by specified column
                for partition_value in data[partition_by].unique():
                    partition_data = data[data[partition_by] == partition_value]
                    partition_path = dataset_path / f"{partition_by}={partition_value}"
                    partition_path.mkdir(parents=True, exist_ok=True)

                    file_path = partition_path / f"data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
                    partition_data.to_parquet(file_path, index=False)
            else:
                # Store as single file
                file_path = dataset_path / f"data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
                data.to_parquet(file_path, index=False)

            logger.info(f"Data stored in data lake: {dataset_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to store data in data lake: {e}")
            return False

    async def read_from_data_lake(
        self,
        dataset_name: str,
        filters: Optional[Dict[str, Any]] = None
    ) -> pd.DataFrame:
        """Read data from data lake."""
        try:
            dataset_path = self.data_lake_path / dataset_name

            if not dataset_path.exists():
                logger.warning(f"Dataset not found in data lake: {dataset_name}")
                return pd.DataFrame()

            # Find all parquet files
            parquet_files = list(dataset_path.rglob("*.parquet"))

            if not parquet_files:
                logger.warning(f"No parquet files found for dataset: {dataset_name}")
                return pd.DataFrame()

            # Read and combine all files
            dataframes = []
            for file_path in parquet_files:
                df = pd.read_parquet(file_path)
                dataframes.append(df)

            combined_df = pd.concat(dataframes, ignore_index=True)

            # Apply filters if provided
            if filters:
                for column, value in filters.items():
                    if column in combined_df.columns:
                        combined_df = combined_df[combined_df[column] == value]

            return combined_df

        except Exception as e:
            logger.error(f"Failed to read from data lake: {e}")
            return pd.DataFrame()

    # ==================== CROSS-DATABASE OPERATIONS ====================

    async def get_comprehensive_token_data(
        self, token_address: str
    ) -> Dict[str, Any]:
        """Get comprehensive token data from all databases."""
        try:
            result = {
                "metadata": {},
                "time_series": {},
                "documents": {},
                "analysis": {},
                "cached_data": {}
            }

            # Get metadata from PostgreSQL
            if self.postgresql_enabled:
                metadata_query = """
                    SELECT * FROM token_metadata WHERE address = $1
                """
                metadata = await self.execute_sql(metadata_query, (token_address,))
                result["metadata"] = metadata[0] if metadata else {}

            # Get recent time-series data
            if self.influxdb_enabled:
                end_time = datetime.now()
                start_time = end_time - timedelta(days=7)

                price_data = await self.query_time_series(
                    "price",
                    start_time,
                    end_time,
                    {"token_address": token_address}
                )
                result["time_series"]["price"] = price_data

            # Get analysis documents
            if self.mongodb_enabled:
                analysis_docs = await self.query_documents(
                    "analysis_reports",
                    {"token_address": token_address},
                    limit=10,
                    sort=[("timestamp", -1)]
                )
                result["documents"]["analysis"] = analysis_docs

            # Get cached data
            cached_data = await self.cache_manager.get(
                f"token_summary:{token_address}",
                "tokens"
            )
            result["cached_data"] = cached_data or {}

            return result

        except Exception as e:
            logger.error(f"Failed to get comprehensive token data: {e}")
            return {}

    async def health_check(self) -> Dict[str, bool]:
        """Check health of all database connections."""
        health_status = {
            "cache": False,
            "duckdb": False,
            "influxdb": False,
            "mongodb": False,
            "postgresql": False
        }

        try:
            # Check cache
            health_status["cache"] = await self.cache_manager.health_check()

            # Check DuckDB
            health_status["duckdb"] = True  # DuckDB is file-based, assume healthy if initialized

            # Check InfluxDB
            if self.influxdb_enabled and self.influx_client:
                try:
                    await self.influx_client.ping()
                    health_status["influxdb"] = True
                except:
                    health_status["influxdb"] = False

            # Check MongoDB
            if self.mongodb_enabled and self.mongo_client:
                try:
                    await self.mongo_client.admin.command('ping')
                    health_status["mongodb"] = True
                except:
                    health_status["mongodb"] = False

            # Check PostgreSQL
            if self.postgresql_enabled and self.postgres_pool:
                try:
                    async with self.postgres_pool.acquire() as conn:
                        await conn.fetchval("SELECT 1")
                    health_status["postgresql"] = True
                except:
                    health_status["postgresql"] = False

        except Exception as e:
            logger.error(f"Health check failed: {e}")

        return health_status
