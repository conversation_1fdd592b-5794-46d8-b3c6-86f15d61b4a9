"""
Enhanced logging configuration with 2025 best practices.
Provides structured logging, correlation IDs, and production-ready patterns.
"""

import logging
import logging.config
import os
import sys
import uuid
from contextvars import ContextVar
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import structlog
from structlog.contextvars import merge_contextvars

from .config import config

# Context variables for distributed tracing (2025 best practice)
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)
request_id: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id: ContextVar[Optional[str]] = ContextVar('user_id', default=None)
session_id: ContextVar[Optional[str]] = ContextVar('session_id', default=None)
trace_id: ContextVar[Optional[str]] = ContextVar('trace_id', default=None)


def add_correlation_id(logger, method_name, event_dict):
    """Add correlation ID to log events for distributed tracing."""
    if correlation_id.get() is None:
        correlation_id.set(str(uuid.uuid4()))
    
    event_dict['correlation_id'] = correlation_id.get()
    
    # Add other context variables if available
    if request_id.get():
        event_dict['request_id'] = request_id.get()
    if user_id.get():
        event_dict['user_id'] = user_id.get()
    if session_id.get():
        event_dict['session_id'] = session_id.get()
    if trace_id.get():
        event_dict['trace_id'] = trace_id.get()
    
    return event_dict


def add_system_context(logger, method_name, event_dict):
    """Add system context information to logs."""
    event_dict.update({
        'service': 'token-analyzer',
        'version': config.system.app_version,
        'environment': config.system.environment.value,
        'hostname': os.uname().nodename,
        'process_id': os.getpid(),
    })
    return event_dict


def filter_sensitive_data(logger, method_name, event_dict):
    """Filter sensitive data from logs (2025 security best practice)."""
    sensitive_keys = {
        'password', 'token', 'secret', 'key', 'auth', 'credential',
        'private_key', 'api_key', 'access_token', 'refresh_token'
    }
    
    def _filter_dict(data):
        if isinstance(data, dict):
            return {
                k: '[REDACTED]' if any(sensitive in k.lower() for sensitive in sensitive_keys)
                else _filter_dict(v) for k, v in data.items()
            }
        elif isinstance(data, list):
            return [_filter_dict(item) for item in data]
        return data
    
    return _filter_dict(event_dict)


def add_performance_metrics(logger, method_name, event_dict):
    """Add performance context to logs."""
    event_dict['timestamp_ms'] = int(datetime.utcnow().timestamp() * 1000)
    return event_dict


class AsyncStructlogHandler(logging.Handler):
    """Async-compatible structlog handler for high-performance logging."""
    
    def __init__(self, level=logging.NOTSET):
        super().__init__(level)
        self.structlog_logger = structlog.get_logger()
    
    def emit(self, record):
        """Emit log record asynchronously."""
        try:
            # Convert logging record to structlog format
            event_dict = {
                'message': record.getMessage(),
                'level': record.levelname.lower(),
                'logger_name': record.name,
                'module': record.module,
                'function': record.funcName,
                'line': record.lineno,
            }
            
            # Add exception info if present
            if record.exc_info:
                event_dict['exception'] = self.format(record)
            
            # Use appropriate structlog method based on level
            if record.levelno >= logging.CRITICAL:
                self.structlog_logger.critical(**event_dict)
            elif record.levelno >= logging.ERROR:
                self.structlog_logger.error(**event_dict)
            elif record.levelno >= logging.WARNING:
                self.structlog_logger.warning(**event_dict)
            elif record.levelno >= logging.INFO:
                self.structlog_logger.info(**event_dict)
            else:
                self.structlog_logger.debug(**event_dict)
                
        except Exception:
            self.handleError(record)


def configure_structlog():
    """Configure structlog with 2025 best practices."""
    
    # Determine processors based on environment
    processors = [
        # Context and correlation
        merge_contextvars,
        add_correlation_id,
        add_system_context,
        add_performance_metrics,
        
        # Security
        filter_sensitive_data,
        
        # Standard processors
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    # Add appropriate renderer based on environment
    if config.system.environment.value == "development":
        processors.append(structlog.dev.ConsoleRenderer(colors=True))
    else:
        processors.extend([
            structlog.processors.dict_tracebacks,
            structlog.processors.JSONRenderer()
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def configure_standard_logging():
    """Configure standard Python logging to work with structlog."""
    
    # Ensure log directory exists
    log_dir = config.system.logs_dir
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'json': {
                '()': structlog.stdlib.ProcessorFormatter,
                'processor': structlog.processors.JSONRenderer(),
            },
            'console': {
                '()': structlog.stdlib.ProcessorFormatter,
                'processor': structlog.dev.ConsoleRenderer(colors=True),
            },
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'console' if config.system.environment.value == "development" else 'json',
                'stream': sys.stdout,
            },
            'file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': log_dir / 'application.log',
                'maxBytes': 10 * 1024 * 1024,  # 10MB
                'backupCount': 5,
                'formatter': 'json',
            },
            'error_file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': log_dir / 'error.log',
                'maxBytes': 10 * 1024 * 1024,  # 10MB
                'backupCount': 5,
                'formatter': 'json',
                'level': 'ERROR',
            },
        },
        'loggers': {
            '': {  # Root logger
                'handlers': ['console', 'file'],
                'level': config.system.log_level.value,
                'propagate': False,
            },
            'token_analyzer': {
                'handlers': ['console', 'file', 'error_file'],
                'level': config.system.log_level.value,
                'propagate': False,
            },
            'httpx': {
                'level': 'WARNING',
                'propagate': True,
            },
            'urllib3': {
                'level': 'WARNING',
                'propagate': True,
            },
        },
    }
    
    logging.config.dictConfig(logging_config)


def setup_logging():
    """Setup complete logging configuration with 2025 best practices."""
    configure_structlog()
    configure_standard_logging()
    
    # Get logger and log startup
    logger = structlog.get_logger("token_analyzer.logging")
    logger.info(
        "Logging system initialized",
        environment=config.system.environment.value,
        log_level=config.system.log_level.value,
        structured_logging=True,
        correlation_tracking=True,
    )


# Context managers for correlation tracking
class CorrelationContext:
    """Context manager for correlation ID tracking."""
    
    def __init__(self, correlation_id_value: Optional[str] = None):
        self.correlation_id_value = correlation_id_value or str(uuid.uuid4())
        self.token = None
    
    def __enter__(self):
        self.token = correlation_id.set(self.correlation_id_value)
        return self.correlation_id_value
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        correlation_id.reset(self.token)


class RequestContext:
    """Context manager for request tracking."""
    
    def __init__(self, request_id_value: Optional[str] = None, user_id_value: Optional[str] = None):
        self.request_id_value = request_id_value or str(uuid.uuid4())
        self.user_id_value = user_id_value
        self.request_token = None
        self.user_token = None
    
    def __enter__(self):
        self.request_token = request_id.set(self.request_id_value)
        if self.user_id_value:
            self.user_token = user_id.set(self.user_id_value)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        request_id.reset(self.request_token)
        if self.user_token:
            user_id.reset(self.user_token)


# Convenience functions
def get_logger(name: str = None) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


def set_correlation_id(corr_id: str):
    """Set correlation ID for current context."""
    correlation_id.set(corr_id)


def get_correlation_id() -> Optional[str]:
    """Get current correlation ID."""
    return correlation_id.get()
