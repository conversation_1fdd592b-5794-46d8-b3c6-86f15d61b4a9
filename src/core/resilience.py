"""
Advanced Error Handling & Recovery System with 2025 Best Practices
Implements exponential backoff, dead letter queues, graceful degradation, and automatic recovery.
"""

import asyncio
import json
import time
import uuid
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, Union
import threading
from functools import wraps
import traceback

import structlog

from .config import config
from .logging_config import get_logger, CorrelationContext
from ..monitoring import increment_counter, set_gauge, record_timer


logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels for classification."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    RETRY = "retry"
    FALLBACK = "fallback"
    CIRCUIT_BREAK = "circuit_break"
    GRACEFUL_DEGRADE = "graceful_degrade"
    DEAD_LETTER = "dead_letter"


@dataclass
class ErrorContext:
    """Context information for error handling."""
    error: Exception
    operation: str
    attempt: int
    max_attempts: int
    severity: ErrorSeverity
    timestamp: datetime
    correlation_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging/serialization."""
        return {
            "error_type": type(self.error).__name__,
            "error_message": str(self.error),
            "operation": self.operation,
            "attempt": self.attempt,
            "max_attempts": self.max_attempts,
            "severity": self.severity.value,
            "timestamp": self.timestamp.isoformat(),
            "correlation_id": self.correlation_id,
            "metadata": self.metadata
        }


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    backoff_strategy: str = "exponential"  # exponential, linear, fixed
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number."""
        if self.backoff_strategy == "exponential":
            delay = self.base_delay * (self.exponential_base ** (attempt - 1))
        elif self.backoff_strategy == "linear":
            delay = self.base_delay * attempt
        else:  # fixed
            delay = self.base_delay
        
        # Apply max delay limit
        delay = min(delay, self.max_delay)
        
        # Add jitter to prevent thundering herd
        if self.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    success_threshold: int = 3
    timeout: float = 30.0


class CircuitBreaker:
    """Circuit breaker implementation with 2025 patterns."""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self._lock = threading.RLock()
        
        logger.info("CircuitBreaker initialized", name=name, config=config.__dict__)
    
    def can_execute(self) -> bool:
        """Check if operation can be executed."""
        with self._lock:
            if self.state == CircuitBreakerState.CLOSED:
                return True
            elif self.state == CircuitBreakerState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.success_count = 0
                    logger.info("CircuitBreaker transitioning to HALF_OPEN", name=self.name)
                    return True
                return False
            else:  # HALF_OPEN
                return True
    
    def record_success(self):
        """Record successful operation."""
        with self._lock:
            self.failure_count = 0
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitBreakerState.CLOSED
                    logger.info("CircuitBreaker transitioning to CLOSED", name=self.name)
            
            # Update metrics
            set_gauge(f"circuit_breaker_state", 0, {"name": self.name})  # 0 = closed
    
    def record_failure(self):
        """Record failed operation."""
        with self._lock:
            self.failure_count += 1
            self.last_failure_time = datetime.now(timezone.utc)
            
            if self.state == CircuitBreakerState.CLOSED:
                if self.failure_count >= self.config.failure_threshold:
                    self.state = CircuitBreakerState.OPEN
                    logger.warning("CircuitBreaker transitioning to OPEN", 
                                 name=self.name, failure_count=self.failure_count)
            elif self.state == CircuitBreakerState.HALF_OPEN:
                self.state = CircuitBreakerState.OPEN
                logger.warning("CircuitBreaker transitioning back to OPEN", name=self.name)
            
            # Update metrics
            state_value = 1 if self.state == CircuitBreakerState.OPEN else 2  # 1 = open, 2 = half-open
            set_gauge(f"circuit_breaker_state", state_value, {"name": self.name})
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset."""
        if not self.last_failure_time:
            return False
        
        time_since_failure = datetime.now(timezone.utc) - self.last_failure_time
        return time_since_failure.total_seconds() >= self.config.recovery_timeout


class DeadLetterQueue:
    """Dead letter queue for failed operations."""
    
    def __init__(self, max_size: int = 10000):
        self.max_size = max_size
        self.queue: deque = deque(maxlen=max_size)
        self.failed_operations: Dict[str, int] = defaultdict(int)
        self._lock = threading.RLock()
        
        logger.info("DeadLetterQueue initialized", max_size=max_size)
    
    def add_failed_operation(self, operation: str, error_context: ErrorContext, 
                           payload: Optional[Dict[str, Any]] = None):
        """Add failed operation to dead letter queue."""
        with self._lock:
            dead_letter_item = {
                "id": str(uuid.uuid4()),
                "operation": operation,
                "error_context": error_context.to_dict(),
                "payload": payload,
                "queued_at": datetime.now(timezone.utc).isoformat(),
                "retry_count": 0
            }
            
            self.queue.append(dead_letter_item)
            self.failed_operations[operation] += 1
            
            logger.error("Operation added to dead letter queue",
                        operation=operation,
                        error_type=type(error_context.error).__name__,
                        queue_size=len(self.queue))
            
            # Update metrics
            increment_counter("dead_letter_queue_items", 1, {"operation": operation})
            set_gauge("dead_letter_queue_size", len(self.queue))
    
    def get_failed_operations(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent failed operations."""
        with self._lock:
            return list(self.queue)[-limit:]
    
    def get_failure_stats(self) -> Dict[str, int]:
        """Get failure statistics by operation."""
        with self._lock:
            return dict(self.failed_operations)


class GracefulDegradationManager:
    """Manages graceful degradation strategies."""
    
    def __init__(self):
        self.degradation_levels: Dict[str, int] = defaultdict(int)
        self.fallback_handlers: Dict[str, Callable] = {}
        self._lock = threading.RLock()
        
        logger.info("GracefulDegradationManager initialized")
    
    def register_fallback(self, operation: str, handler: Callable):
        """Register fallback handler for operation."""
        with self._lock:
            self.fallback_handlers[operation] = handler
            logger.info("Fallback handler registered", operation=operation)
    
    def degrade_service(self, service: str, level: int = 1):
        """Degrade service to specified level."""
        with self._lock:
            self.degradation_levels[service] = level
            logger.warning("Service degraded", service=service, level=level)
            
            # Update metrics
            set_gauge("service_degradation_level", level, {"service": service})
    
    def restore_service(self, service: str):
        """Restore service to normal operation."""
        with self._lock:
            if service in self.degradation_levels:
                del self.degradation_levels[service]
                logger.info("Service restored", service=service)
                
                # Update metrics
                set_gauge("service_degradation_level", 0, {"service": service})
    
    def get_degradation_level(self, service: str) -> int:
        """Get current degradation level for service."""
        with self._lock:
            return self.degradation_levels.get(service, 0)
    
    def execute_with_fallback(self, operation: str, *args, **kwargs) -> Any:
        """Execute operation with fallback if available."""
        if operation in self.fallback_handlers:
            try:
                return self.fallback_handlers[operation](*args, **kwargs)
            except Exception as e:
                logger.exception("Fallback handler failed", operation=operation, error=str(e))
                raise
        else:
            raise ValueError(f"No fallback handler registered for operation: {operation}")


# Global instances
dead_letter_queue = DeadLetterQueue()
degradation_manager = GracefulDegradationManager()
circuit_breakers: Dict[str, CircuitBreaker] = {}


def get_circuit_breaker(name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
    """Get or create circuit breaker."""
    if name not in circuit_breakers:
        if config is None:
            config = CircuitBreakerConfig()
        circuit_breakers[name] = CircuitBreaker(name, config)
    return circuit_breakers[name]


def resilient_operation(
    operation_name: str,
    retry_config: Optional[RetryConfig] = None,
    circuit_breaker_config: Optional[CircuitBreakerConfig] = None,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    fallback_handler: Optional[Callable] = None,
    enable_dead_letter: bool = True
):
    """
    Decorator for resilient operations with comprehensive error handling.
    
    Features:
    - Exponential backoff retry
    - Circuit breaker pattern
    - Dead letter queue for failed operations
    - Graceful degradation
    - Comprehensive logging and metrics
    """
    
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await _execute_resilient_operation(
                func, args, kwargs, operation_name, retry_config,
                circuit_breaker_config, severity, fallback_handler, enable_dead_letter
            )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(_execute_resilient_operation(
                func, args, kwargs, operation_name, retry_config,
                circuit_breaker_config, severity, fallback_handler, enable_dead_letter
            ))
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


async def _execute_resilient_operation(
    func: Callable,
    args: tuple,
    kwargs: dict,
    operation_name: str,
    retry_config: Optional[RetryConfig],
    circuit_breaker_config: Optional[CircuitBreakerConfig],
    severity: ErrorSeverity,
    fallback_handler: Optional[Callable],
    enable_dead_letter: bool
) -> Any:
    """Execute operation with full resilience patterns."""
    
    if retry_config is None:
        retry_config = RetryConfig()
    
    # Get circuit breaker
    circuit_breaker = get_circuit_breaker(operation_name, circuit_breaker_config)
    
    # Check circuit breaker
    if not circuit_breaker.can_execute():
        logger.warning("Circuit breaker is OPEN", operation=operation_name)
        increment_counter("circuit_breaker_rejections", 1, {"operation": operation_name})
        
        if fallback_handler:
            logger.info("Executing fallback handler", operation=operation_name)
            return await _safe_execute(fallback_handler, args, kwargs)
        else:
            raise Exception(f"Circuit breaker is OPEN for operation: {operation_name}")
    
    last_error = None
    
    for attempt in range(1, retry_config.max_attempts + 1):
        try:
            with CorrelationContext() as correlation_id:
                start_time = time.time()
                
                # Execute the operation
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # Record success
                duration = (time.time() - start_time) * 1000
                circuit_breaker.record_success()
                
                # Update metrics
                increment_counter("operation_success", 1, {"operation": operation_name})
                record_timer("operation_duration", duration, {"operation": operation_name})
                
                logger.info("Operation succeeded",
                           operation=operation_name,
                           attempt=attempt,
                           duration_ms=duration,
                           correlation_id=correlation_id)
                
                return result
                
        except Exception as error:
            last_error = error
            
            # Create error context
            error_context = ErrorContext(
                error=error,
                operation=operation_name,
                attempt=attempt,
                max_attempts=retry_config.max_attempts,
                severity=severity,
                timestamp=datetime.now(timezone.utc),
                correlation_id=getattr(error, 'correlation_id', None),
                metadata={
                    "args_count": len(args),
                    "kwargs_keys": list(kwargs.keys()),
                    "traceback": traceback.format_exc()
                }
            )
            
            # Record failure
            circuit_breaker.record_failure()
            increment_counter("operation_failure", 1, {
                "operation": operation_name,
                "error_type": type(error).__name__
            })
            
            logger.warning("Operation failed",
                          operation=operation_name,
                          attempt=attempt,
                          error_type=type(error).__name__,
                          error_message=str(error),
                          severity=severity.value)
            
            # Check if we should retry
            if attempt < retry_config.max_attempts:
                delay = retry_config.calculate_delay(attempt)
                logger.info("Retrying operation",
                           operation=operation_name,
                           attempt=attempt + 1,
                           delay_seconds=delay)
                
                await asyncio.sleep(delay)
                continue
            else:
                # Max attempts reached
                logger.error("Operation failed after all attempts",
                            operation=operation_name,
                            max_attempts=retry_config.max_attempts,
                            final_error=str(error))
                
                # Add to dead letter queue if enabled
                if enable_dead_letter:
                    payload = {
                        "args": [str(arg) for arg in args],  # Convert to strings for serialization
                        "kwargs": {k: str(v) for k, v in kwargs.items()}
                    }
                    dead_letter_queue.add_failed_operation(operation_name, error_context, payload)
                
                # Try fallback handler
                if fallback_handler:
                    logger.info("Executing fallback handler after failure", operation=operation_name)
                    try:
                        return await _safe_execute(fallback_handler, args, kwargs)
                    except Exception as fallback_error:
                        logger.exception("Fallback handler also failed",
                                       operation=operation_name,
                                       fallback_error=str(fallback_error))
                
                # Re-raise the last error
                raise last_error


async def _safe_execute(func: Callable, args: tuple, kwargs: dict) -> Any:
    """Safely execute a function (sync or async)."""
    if asyncio.iscoroutinefunction(func):
        return await func(*args, **kwargs)
    else:
        return func(*args, **kwargs)


# Convenience functions for common patterns
def register_fallback(operation: str, handler: Callable):
    """Register a fallback handler for an operation."""
    degradation_manager.register_fallback(operation, handler)


def degrade_service(service: str, level: int = 1):
    """Degrade a service to specified level."""
    degradation_manager.degrade_service(service, level)


def restore_service(service: str):
    """Restore a service to normal operation."""
    degradation_manager.restore_service(service)


def get_system_health() -> Dict[str, Any]:
    """Get overall system health status."""
    return {
        "circuit_breakers": {
            name: {
                "state": cb.state.value,
                "failure_count": cb.failure_count,
                "success_count": cb.success_count
            }
            for name, cb in circuit_breakers.items()
        },
        "dead_letter_queue": {
            "size": len(dead_letter_queue.queue),
            "failure_stats": dead_letter_queue.get_failure_stats()
        },
        "degraded_services": dict(degradation_manager.degradation_levels),
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
