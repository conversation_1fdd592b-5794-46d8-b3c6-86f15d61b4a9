"""
REST API layer for the Token Analyzer system.
Provides HTTP endpoints for external access to token analysis functionality.
"""

from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import Any

from fastapi import BackgroundTasks, Depends, FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uuid

from ..agents.coordinator import AgentCoordinator
from ..core.cache import CacheManager
from ..core.config import get_settings
from ..core.database import DatabaseManager
from ..core.logging_config import setup_logging, RequestContext, get_logger
from ..integrations.metrics import MetricsCollector

# Initialize enhanced logging system (2025 best practices)
setup_logging()
logger = get_logger(__name__)


# ==================== PYDANTIC MODELS ====================


class TokenAnalysisRequest(BaseModel):
    """Request model for token analysis."""

    token_address: str = Field(..., description="Token contract address")
    chain_id: int = Field(default=1, description="Blockchain network ID")
    analysis_types: list[str] | None = Field(
        default=None, description="Specific analysis types to run"
    )
    priority: str = Field(default="medium", description="Analysis priority")


class BatchAnalysisRequest(BaseModel):
    """Request model for batch token analysis."""

    tokens: list[dict[str, Any]] = Field(..., description="List of tokens to analyze")
    analysis_types: list[str] | None = Field(
        default=None, description="Specific analysis types to run"
    )
    priority: str = Field(default="medium", description="Analysis priority")


class DiscoveryRequest(BaseModel):
    """Request model for token discovery."""

    sources: list[str] = Field(
        default=["defillama", "dexscreener"], description="Discovery sources"
    )
    limit: int = Field(default=50, description="Maximum number of tokens to discover")
    min_age_hours: int = Field(default=24, description="Minimum token age in hours")


class TokenAnalysisResponse(BaseModel):
    """Response model for token analysis."""

    token_address: str
    chain_id: int
    analysis_id: str
    timestamp: str
    overall_score: float | None = None
    risk_score: float | None = None
    investment_recommendation: str | None = None
    confidence_level: float | None = None
    execution_time_ms: int | None = None
    discovery_data: dict[str, Any] | None = None
    validation_results: dict[str, Any] | None = None
    chain_info: dict[str, Any] | None = None
    market_data: dict[str, Any] | None = None
    technical_analysis: dict[str, Any] | None = None
    sentiment_analysis: dict[str, Any] | None = None


class HealthResponse(BaseModel):
    """Response model for health check."""

    status: str
    timestamp: str
    version: str
    components: dict[str, Any]


# ==================== DEPENDENCY INJECTION ====================


class AppContext:
    """Application context for dependency injection."""

    def __init__(self):
        self.db_manager: DatabaseManager | None = None
        self.cache_manager: CacheManager | None = None
        self.coordinator: AgentCoordinator | None = None
        self.metrics_collector: MetricsCollector | None = None
        self.settings = get_settings()


app_context = AppContext()


async def get_coordinator() -> AgentCoordinator:
    """Get agent coordinator dependency."""
    if not app_context.coordinator:
        raise HTTPException(status_code=503, detail="Agent coordinator not available")
    return app_context.coordinator


async def get_metrics_collector() -> MetricsCollector:
    """Get metrics collector dependency."""
    if not app_context.metrics_collector:
        raise HTTPException(status_code=503, detail="Metrics collector not available")
    return app_context.metrics_collector


# ==================== LIFESPAN MANAGEMENT ====================


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle."""
    logger.info("Starting Token Analyzer API")

    try:
        # Initialize core components
        app_context.db_manager = DatabaseManager()
        app_context.cache_manager = CacheManager()
        app_context.metrics_collector = MetricsCollector()

        await app_context.db_manager.initialize()
        await app_context.cache_manager.initialize()
        await app_context.metrics_collector.start()

        # Initialize coordinator
        app_context.coordinator = AgentCoordinator(
            db_manager=app_context.db_manager,
            cache_manager=app_context.cache_manager,
            metrics_collector=app_context.metrics_collector,
        )
        await app_context.coordinator.initialize()

        logger.info("Token Analyzer API started successfully")
        yield

    finally:
        # Cleanup
        logger.info("Shutting down Token Analyzer API")

        if app_context.coordinator:
            await app_context.coordinator.shutdown()
        if app_context.metrics_collector:
            await app_context.metrics_collector.stop()
        if app_context.cache_manager:
            await app_context.cache_manager.shutdown()
        if app_context.db_manager:
            await app_context.db_manager.close()


# ==================== FASTAPI APP ====================

app = FastAPI(
    title="Token Analyzer API",
    description="Advanced cryptocurrency token analysis and discovery API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Add correlation tracking middleware (2025 best practice)
@app.middleware("http")
async def correlation_middleware(request: Request, call_next):
    """Add correlation ID to all requests for distributed tracing."""
    correlation_id_value = request.headers.get("X-Correlation-ID") or str(uuid.uuid4())
    request_id_value = request.headers.get("X-Request-ID") or str(uuid.uuid4())

    # Set context variables for logging
    from ..core.logging_config import correlation_id, request_id
    correlation_token = correlation_id.set(correlation_id_value)
    request_token = request_id.set(request_id_value)

    try:
        # Process request
        response = await call_next(request)

        # Add correlation headers to response
        response.headers["X-Correlation-ID"] = correlation_id_value
        response.headers["X-Request-ID"] = request_id_value

        return response
    finally:
        # Clean up context
        correlation_id.reset(correlation_token)
        request_id.reset(request_token)


# ==================== HEALTH & STATUS ENDPOINTS ====================


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    try:
        components = {}

        # Check coordinator health
        if app_context.coordinator:
            coordinator_health = await app_context.coordinator.health_check()
            components["coordinator"] = {
                "status": "healthy" if coordinator_health else "unhealthy"
            }

        # Check agent status
        if app_context.coordinator:
            agent_status = await app_context.coordinator.get_agent_status()
            components["agents"] = agent_status

        # Check database
        if app_context.db_manager:
            try:
                await app_context.db_manager.execute_query("SELECT 1")
                components["database"] = {"status": "healthy"}
            except Exception as e:
                components["database"] = {"status": "unhealthy", "error": str(e)}

        # Check cache
        if app_context.cache_manager:
            try:
                await app_context.cache_manager.set("health_check", "ok", ttl=60)
                components["cache"] = {"status": "healthy"}
            except Exception as e:
                components["cache"] = {"status": "unhealthy", "error": str(e)}

        # Determine overall status
        overall_status = "healthy"
        for component in components.values():
            if isinstance(component, dict) and component.get("status") != "healthy":
                if "agents" in str(component):
                    # Check if any agents are unhealthy
                    for agent_status in component.values():
                        if isinstance(agent_status, dict) and not agent_status.get(
                            "healthy", True
                        ):
                            overall_status = "degraded"
                            break
                else:
                    overall_status = "unhealthy"
                    break

        return HealthResponse(
            status=overall_status,
            timestamp=datetime.now(timezone.utc).isoformat(),
            version="1.0.0",
            components=components,
        )

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="error",
            timestamp=datetime.now(timezone.utc).isoformat(),
            version="1.0.0",
            components={"error": str(e)},
        )


@app.get("/metrics")
async def get_metrics(
    metrics_collector: MetricsCollector = Depends(get_metrics_collector),
):
    """Get system metrics."""
    try:
        return {
            "agent_metrics": metrics_collector.get_agent_metrics(),
            "counters": metrics_collector.get_counters(),
            "gauges": metrics_collector.get_gauges(),
            "system_health": metrics_collector.get_system_health(),
        }
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== TOKEN ANALYSIS ENDPOINTS ====================


@app.post("/analyze/token", response_model=TokenAnalysisResponse)
async def analyze_token(
    request: TokenAnalysisRequest,
    background_tasks: BackgroundTasks,
    coordinator: AgentCoordinator = Depends(get_coordinator),
):
    """Analyze a single token."""
    try:
        # For now, use the existing coordinator method
        # This would need to be adapted to work with our new coordinator
        result = await coordinator.run_analysis_pipeline(
            address=request.token_address,
            chain="ethereum",  # Would map chain_id to chain name
            include_technical=True,
            include_sentiment=True,
        )

        # Convert result to response model
        return TokenAnalysisResponse(
            token_address=request.token_address,
            chain_id=request.chain_id,
            analysis_id=result.get("pipeline_id", ""),
            timestamp=datetime.now(timezone.utc).isoformat(),
            overall_score=result.get("alpha_score"),
            risk_score=result.get("risk_score"),
            investment_recommendation="HOLD",  # Would be derived from scores
            confidence_level=0.8,  # Would be calculated
            execution_time_ms=int(result.get("duration_seconds", 0) * 1000),
            discovery_data={},
            validation_results={},
            chain_info=result.get("token", {}),
            market_data={},
            technical_analysis=result.get("technical_indicators", {}),
            sentiment_analysis=result.get("sentiment_data", {}),
        )

    except Exception as e:
        logger.error(f"Token analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/discover/tokens")
async def discover_tokens(
    request: DiscoveryRequest, coordinator: AgentCoordinator = Depends(get_coordinator)
):
    """Discover new tokens."""
    try:
        result = await coordinator.run_discovery_pipeline(
            sources=request.sources,
            min_age_hours=request.min_age_hours,
            limit=request.limit,
        )

        return result

    except Exception as e:
        logger.error(f"Token discovery failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== ERROR HANDLERS ====================


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        },
    )


if __name__ == "__main__":
    import uvicorn

    # Run the API server
    uvicorn.run(
        "src.api.main:app", host="0.0.0.0", port=8000, reload=True, log_level="info"
    )
