"""
Performance Optimization Engine with 2025 Best Practices
Advanced performance monitoring, optimization, and auto-scaling with ML-driven insights.
"""

import asyncio
import json
import time
import uuid
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Union, Callable
import threading
import statistics
import psutil
import gc
from functools import wraps
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

import structlog

from ..core.logging_config import get_logger, CorrelationContext
from ..monitoring import increment_counter, set_gauge, record_timer, timer_context


logger = get_logger(__name__)


class OptimizationStrategy(Enum):
    """Performance optimization strategies."""
    CACHING = "caching"
    BATCHING = "batching"
    PARALLELIZATION = "parallelization"
    LAZY_LOADING = "lazy_loading"
    CONNECTION_POOLING = "connection_pooling"
    MEMORY_OPTIMIZATION = "memory_optimization"
    CPU_OPTIMIZATION = "cpu_optimization"
    IO_OPTIMIZATION = "io_optimization"


class PerformanceMetric(Enum):
    """Performance metrics to track."""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    CPU_USAGE = "cpu_usage"
    MEMORY_USAGE = "memory_usage"
    DISK_IO = "disk_io"
    NETWORK_IO = "network_io"
    CACHE_HIT_RATE = "cache_hit_rate"
    ERROR_RATE = "error_rate"
    QUEUE_DEPTH = "queue_depth"
    ACTIVE_CONNECTIONS = "active_connections"


class OptimizationLevel(Enum):
    """Optimization levels."""
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"
    EXPERIMENTAL = "experimental"


@dataclass
class PerformanceProfile:
    """Performance profile for operations."""
    operation_name: str
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float
    throughput: float
    error_rate: float
    cpu_usage: float
    memory_usage: float
    last_updated: datetime
    sample_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "operation_name": self.operation_name,
            "avg_response_time": self.avg_response_time,
            "p95_response_time": self.p95_response_time,
            "p99_response_time": self.p99_response_time,
            "throughput": self.throughput,
            "error_rate": self.error_rate,
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "last_updated": self.last_updated.isoformat(),
            "sample_count": self.sample_count
        }


@dataclass
class OptimizationRecommendation:
    """Performance optimization recommendation."""
    id: str
    operation_name: str
    strategy: OptimizationStrategy
    description: str
    expected_improvement: float
    confidence_score: float
    implementation_complexity: str
    estimated_effort_hours: int
    priority: int
    created_at: datetime
    applied: bool = False
    applied_at: Optional[datetime] = None
    actual_improvement: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "operation_name": self.operation_name,
            "strategy": self.strategy.value,
            "description": self.description,
            "expected_improvement": self.expected_improvement,
            "confidence_score": self.confidence_score,
            "implementation_complexity": self.implementation_complexity,
            "estimated_effort_hours": self.estimated_effort_hours,
            "priority": self.priority,
            "created_at": self.created_at.isoformat(),
            "applied": self.applied,
            "applied_at": self.applied_at.isoformat() if self.applied_at else None,
            "actual_improvement": self.actual_improvement
        }


class AdvancedCache:
    """High-performance cache with intelligent eviction and warming."""
    
    def __init__(self, max_size: int = 10000, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.hit_count = 0
        self.miss_count = 0
        self._lock = threading.RLock()
        
        # Start background cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
        
        logger.info("AdvancedCache initialized", max_size=max_size, ttl_seconds=ttl_seconds)
    
    def _start_cleanup_task(self):
        """Start background cleanup task."""
        async def cleanup_expired():
            while True:
                try:
                    self._cleanup_expired_entries()
                    await asyncio.sleep(60)  # Cleanup every minute
                except Exception as e:
                    logger.error("Cache cleanup error", error=str(e))
                    await asyncio.sleep(300)  # Back off on error
        
        # Start cleanup task if event loop is available
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                self._cleanup_task = asyncio.create_task(cleanup_expired())
        except RuntimeError:
            # No event loop available, cleanup will happen on access
            pass
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self._lock:
            current_time = time.time()
            
            if key not in self.cache:
                self.miss_count += 1
                set_gauge("cache_hit_rate", self._calculate_hit_rate())
                return None
            
            entry = self.cache[key]
            
            # Check TTL
            if current_time - entry["timestamp"] > self.ttl_seconds:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
                self.miss_count += 1
                set_gauge("cache_hit_rate", self._calculate_hit_rate())
                return None
            
            # Update access time for LRU
            self.access_times[key] = current_time
            self.hit_count += 1
            
            set_gauge("cache_hit_rate", self._calculate_hit_rate())
            return entry["value"]
    
    def set(self, key: str, value: Any) -> None:
        """Set value in cache."""
        with self._lock:
            current_time = time.time()
            
            # Check if we need to evict
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = {
                "value": value,
                "timestamp": current_time
            }
            self.access_times[key] = current_time
            
            set_gauge("cache_size", len(self.cache))
    
    def _evict_lru(self):
        """Evict least recently used entry."""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def _cleanup_expired_entries(self):
        """Clean up expired entries."""
        with self._lock:
            current_time = time.time()
            expired_keys = []
            
            for key, entry in self.cache.items():
                if current_time - entry["timestamp"] > self.ttl_seconds:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]
            
            if expired_keys:
                logger.debug("Cleaned up expired cache entries", count=len(expired_keys))
                set_gauge("cache_size", len(self.cache))
    
    def _calculate_hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total_requests = self.hit_count + self.miss_count
        if total_requests == 0:
            return 0.0
        return self.hit_count / total_requests
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": self._calculate_hit_rate(),
                "ttl_seconds": self.ttl_seconds
            }


class BatchProcessor:
    """Intelligent batch processing for improved throughput."""
    
    def __init__(self, batch_size: int = 100, max_wait_time: float = 1.0):
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.pending_items: List[Dict[str, Any]] = []
        self.batch_futures: List[asyncio.Future] = []
        self.last_batch_time = time.time()
        self._lock = threading.RLock()
        self._processing = False
        
        logger.info("BatchProcessor initialized", batch_size=batch_size, max_wait_time=max_wait_time)
    
    async def add_item(self, item: Any, processor_func: Callable) -> Any:
        """Add item to batch for processing."""
        future = asyncio.Future()
        
        with self._lock:
            self.pending_items.append({
                "item": item,
                "future": future,
                "processor": processor_func
            })
            self.batch_futures.append(future)
            
            # Check if we should process batch
            should_process = (
                len(self.pending_items) >= self.batch_size or
                time.time() - self.last_batch_time >= self.max_wait_time
            )
            
            if should_process and not self._processing:
                asyncio.create_task(self._process_batch())
        
        return await future
    
    async def _process_batch(self):
        """Process current batch."""
        with self._lock:
            if self._processing or not self.pending_items:
                return
            
            self._processing = True
            current_batch = self.pending_items.copy()
            current_futures = self.batch_futures.copy()
            
            self.pending_items.clear()
            self.batch_futures.clear()
            self.last_batch_time = time.time()
        
        try:
            # Group items by processor function
            processor_groups = defaultdict(list)
            for item_data in current_batch:
                processor = item_data["processor"]
                processor_groups[processor].append(item_data)
            
            # Process each group
            for processor, items in processor_groups.items():
                try:
                    # Extract just the items for batch processing
                    batch_items = [item_data["item"] for item_data in items]
                    
                    # Process batch
                    if asyncio.iscoroutinefunction(processor):
                        results = await processor(batch_items)
                    else:
                        results = processor(batch_items)
                    
                    # Set results for futures
                    for i, item_data in enumerate(items):
                        if i < len(results):
                            item_data["future"].set_result(results[i])
                        else:
                            item_data["future"].set_exception(
                                IndexError(f"Result index {i} not found in batch results")
                            )
                
                except Exception as e:
                    # Set exception for all futures in this group
                    for item_data in items:
                        item_data["future"].set_exception(e)
                    
                    logger.error("Batch processing error", 
                               processor=str(processor), 
                               batch_size=len(items), 
                               error=str(e))
            
            # Update metrics
            increment_counter("batch_processed", 1, {"batch_size": len(current_batch)})
            record_timer("batch_processing_time", (time.time() - self.last_batch_time) * 1000)
            
        finally:
            with self._lock:
                self._processing = False


class ResourceMonitor:
    """Advanced system resource monitoring."""
    
    def __init__(self, monitoring_interval: float = 5.0):
        self.monitoring_interval = monitoring_interval
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.thresholds = {
            "cpu_usage": 80.0,
            "memory_usage": 85.0,
            "disk_usage": 90.0,
            "network_io": 100.0  # MB/s
        }
        self._monitoring = False
        self._monitor_task = None
        
        logger.info("ResourceMonitor initialized", interval=monitoring_interval)
    
    def start_monitoring(self):
        """Start resource monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        
        async def monitor_loop():
            while self._monitoring:
                try:
                    await self._collect_metrics()
                    await asyncio.sleep(self.monitoring_interval)
                except Exception as e:
                    logger.error("Resource monitoring error", error=str(e))
                    await asyncio.sleep(30)  # Back off on error
        
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                self._monitor_task = asyncio.create_task(monitor_loop())
        except RuntimeError:
            # No event loop, start in thread
            def thread_monitor():
                asyncio.run(monitor_loop())
            
            import threading
            monitor_thread = threading.Thread(target=thread_monitor, daemon=True)
            monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop resource monitoring."""
        self._monitoring = False
        if self._monitor_task:
            self._monitor_task.cancel()
    
    async def _collect_metrics(self):
        """Collect system resource metrics."""
        current_time = time.time()
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory metrics
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_available = memory.available / (1024**3)  # GB
        
        # Disk metrics
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        
        # Network metrics (if available)
        try:
            network = psutil.net_io_counters()
            network_sent = network.bytes_sent / (1024**2)  # MB
            network_recv = network.bytes_recv / (1024**2)  # MB
        except:
            network_sent = network_recv = 0
        
        # Store metrics
        metrics = {
            "cpu_usage": cpu_percent,
            "cpu_count": cpu_count,
            "memory_usage": memory_percent,
            "memory_available_gb": memory_available,
            "disk_usage": disk_percent,
            "network_sent_mb": network_sent,
            "network_recv_mb": network_recv,
            "timestamp": current_time
        }
        
        for metric_name, value in metrics.items():
            if metric_name != "timestamp":
                self.metrics_history[metric_name].append({
                    "value": value,
                    "timestamp": current_time
                })
                
                # Update monitoring gauges
                set_gauge(f"system_{metric_name}", value)
        
        # Check thresholds
        self._check_thresholds(metrics)
    
    def _check_thresholds(self, metrics: Dict[str, Any]):
        """Check if metrics exceed thresholds."""
        for metric_name, threshold in self.thresholds.items():
            if metric_name in metrics and metrics[metric_name] > threshold:
                logger.warning("Resource threshold exceeded",
                             metric=metric_name,
                             value=metrics[metric_name],
                             threshold=threshold)
                
                increment_counter("resource_threshold_exceeded", 1, {"metric": metric_name})
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current resource metrics."""
        if not self.metrics_history:
            return {}
        
        current_metrics = {}
        for metric_name, history in self.metrics_history.items():
            if history:
                current_metrics[metric_name] = history[-1]["value"]
        
        return current_metrics
    
    def get_metric_statistics(self, metric_name: str, 
                            time_window_minutes: int = 60) -> Dict[str, float]:
        """Get statistics for a specific metric."""
        if metric_name not in self.metrics_history:
            return {}
        
        cutoff_time = time.time() - (time_window_minutes * 60)
        recent_values = [
            entry["value"] for entry in self.metrics_history[metric_name]
            if entry["timestamp"] >= cutoff_time
        ]
        
        if not recent_values:
            return {}
        
        return {
            "min": min(recent_values),
            "max": max(recent_values),
            "avg": statistics.mean(recent_values),
            "median": statistics.median(recent_values),
            "p95": self._percentile(recent_values, 95),
            "p99": self._percentile(recent_values, 99),
            "count": len(recent_values)
        }
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile of values."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int((percentile / 100.0) * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]


class PerformanceProfiler:
    """Advanced performance profiling and analysis."""
    
    def __init__(self):
        self.profiles: Dict[str, PerformanceProfile] = {}
        self.operation_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self._lock = threading.RLock()
        
        logger.info("PerformanceProfiler initialized")
    
    def record_operation(self, operation_name: str, duration: float, 
                        success: bool = True, metadata: Optional[Dict[str, Any]] = None):
        """Record operation performance metrics."""
        with self._lock:
            current_time = time.time()
            
            # Store raw metric
            self.operation_metrics[operation_name].append({
                "duration": duration,
                "success": success,
                "timestamp": current_time,
                "metadata": metadata or {}
            })
            
            # Update or create profile
            self._update_profile(operation_name)
            
            # Update monitoring metrics
            record_timer(f"operation_{operation_name}", duration * 1000)
            increment_counter("operation_total", 1, {
                "operation": operation_name,
                "success": str(success)
            })
    
    def _update_profile(self, operation_name: str):
        """Update performance profile for operation."""
        metrics = list(self.operation_metrics[operation_name])
        if not metrics:
            return
        
        # Calculate statistics from recent metrics (last 1000 operations)
        recent_metrics = metrics[-1000:]
        durations = [m["duration"] for m in recent_metrics]
        successes = [m["success"] for m in recent_metrics]
        
        if not durations:
            return
        
        # Calculate performance statistics
        avg_response_time = statistics.mean(durations)
        p95_response_time = self._percentile(durations, 95)
        p99_response_time = self._percentile(durations, 99)
        
        # Calculate throughput (operations per second)
        time_window = 60  # 1 minute
        cutoff_time = time.time() - time_window
        recent_ops = [m for m in recent_metrics if m["timestamp"] >= cutoff_time]
        throughput = len(recent_ops) / time_window if recent_ops else 0
        
        # Calculate error rate
        error_rate = 1 - (sum(successes) / len(successes)) if successes else 0
        
        # Get system metrics (approximation)
        cpu_usage = 0.0
        memory_usage = 0.0
        try:
            cpu_usage = psutil.cpu_percent()
            memory_usage = psutil.virtual_memory().percent
        except:
            pass
        
        # Update profile
        self.profiles[operation_name] = PerformanceProfile(
            operation_name=operation_name,
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            throughput=throughput,
            error_rate=error_rate,
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            last_updated=datetime.now(timezone.utc),
            sample_count=len(recent_metrics)
        )
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile of values."""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = int((percentile / 100.0) * len(sorted_values))
        return sorted_values[min(index, len(sorted_values) - 1)]
    
    def get_profile(self, operation_name: str) -> Optional[PerformanceProfile]:
        """Get performance profile for operation."""
        with self._lock:
            return self.profiles.get(operation_name)
    
    def get_all_profiles(self) -> Dict[str, PerformanceProfile]:
        """Get all performance profiles."""
        with self._lock:
            return self.profiles.copy()
    
    def get_slow_operations(self, threshold_ms: float = 1000) -> List[PerformanceProfile]:
        """Get operations that are slower than threshold."""
        with self._lock:
            slow_ops = []
            threshold_seconds = threshold_ms / 1000
            
            for profile in self.profiles.values():
                if profile.avg_response_time > threshold_seconds:
                    slow_ops.append(profile)
            
            return sorted(slow_ops, key=lambda x: x.avg_response_time, reverse=True)


# Global instances
advanced_cache = AdvancedCache()
batch_processor = BatchProcessor()
resource_monitor = ResourceMonitor()
performance_profiler = PerformanceProfiler()


# Performance decorators
def cached(ttl_seconds: int = 3600, key_func: Optional[Callable] = None):
    """Decorator for caching function results."""
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache
            cached_result = advanced_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            advanced_cache.set(cache_key, result)
            
            return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache
            cached_result = advanced_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            advanced_cache.set(cache_key, result)
            
            return result
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


def profiled(operation_name: Optional[str] = None):
    """Decorator for profiling function performance."""
    def decorator(func: Callable):
        op_name = operation_name or func.__name__
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                performance_profiler.record_operation(op_name, duration, success)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                duration = time.time() - start_time
                performance_profiler.record_operation(op_name, duration, success)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


def batched(batch_size: int = 100, max_wait_time: float = 1.0):
    """Decorator for batch processing."""
    def decorator(func: Callable):
        processor = BatchProcessor(batch_size, max_wait_time)

        @wraps(func)
        async def wrapper(item):
            return await processor.add_item(item, func)

        return wrapper

    return decorator


class OptimizationEngine:
    """ML-driven performance optimization engine."""

    def __init__(self, profiler: PerformanceProfiler, resource_monitor: ResourceMonitor):
        self.profiler = profiler
        self.resource_monitor = resource_monitor
        self.recommendations: Dict[str, OptimizationRecommendation] = {}
        self.applied_optimizations: Set[str] = set()
        self._lock = threading.RLock()

        # Optimization rules and thresholds
        self.optimization_rules = {
            "slow_response_time": {
                "threshold": 1.0,  # seconds
                "strategies": [OptimizationStrategy.CACHING, OptimizationStrategy.PARALLELIZATION]
            },
            "high_error_rate": {
                "threshold": 0.05,  # 5%
                "strategies": [OptimizationStrategy.CONNECTION_POOLING, OptimizationStrategy.IO_OPTIMIZATION]
            },
            "low_throughput": {
                "threshold": 10.0,  # ops/sec
                "strategies": [OptimizationStrategy.BATCHING, OptimizationStrategy.PARALLELIZATION]
            },
            "high_cpu_usage": {
                "threshold": 80.0,  # percent
                "strategies": [OptimizationStrategy.CPU_OPTIMIZATION, OptimizationStrategy.LAZY_LOADING]
            },
            "high_memory_usage": {
                "threshold": 85.0,  # percent
                "strategies": [OptimizationStrategy.MEMORY_OPTIMIZATION, OptimizationStrategy.CACHING]
            }
        }

        logger.info("OptimizationEngine initialized")

    def analyze_performance(self) -> List[OptimizationRecommendation]:
        """Analyze current performance and generate optimization recommendations."""
        with self._lock:
            recommendations = []

            # Analyze operation profiles
            profiles = self.profiler.get_all_profiles()
            for operation_name, profile in profiles.items():
                operation_recommendations = self._analyze_operation_profile(profile)
                recommendations.extend(operation_recommendations)

            # Analyze system resources
            system_recommendations = self._analyze_system_resources()
            recommendations.extend(system_recommendations)

            # Store recommendations
            for rec in recommendations:
                self.recommendations[rec.id] = rec

            # Sort by priority and confidence
            recommendations.sort(key=lambda x: (x.priority, -x.confidence_score))

            logger.info("Performance analysis completed",
                       recommendations_count=len(recommendations))

            return recommendations

    def _analyze_operation_profile(self, profile: PerformanceProfile) -> List[OptimizationRecommendation]:
        """Analyze individual operation profile for optimization opportunities."""
        recommendations = []

        # Check response time
        if profile.avg_response_time > self.optimization_rules["slow_response_time"]["threshold"]:
            for strategy in self.optimization_rules["slow_response_time"]["strategies"]:
                rec = self._create_recommendation(
                    profile.operation_name,
                    strategy,
                    f"Optimize slow response time ({profile.avg_response_time:.3f}s avg)",
                    self._estimate_improvement(strategy, "response_time", profile.avg_response_time),
                    0.8  # confidence
                )
                recommendations.append(rec)

        # Check error rate
        if profile.error_rate > self.optimization_rules["high_error_rate"]["threshold"]:
            for strategy in self.optimization_rules["high_error_rate"]["strategies"]:
                rec = self._create_recommendation(
                    profile.operation_name,
                    strategy,
                    f"Reduce high error rate ({profile.error_rate:.1%})",
                    self._estimate_improvement(strategy, "error_rate", profile.error_rate),
                    0.7  # confidence
                )
                recommendations.append(rec)

        # Check throughput
        if profile.throughput < self.optimization_rules["low_throughput"]["threshold"]:
            for strategy in self.optimization_rules["low_throughput"]["strategies"]:
                rec = self._create_recommendation(
                    profile.operation_name,
                    strategy,
                    f"Improve low throughput ({profile.throughput:.1f} ops/sec)",
                    self._estimate_improvement(strategy, "throughput", profile.throughput),
                    0.6  # confidence
                )
                recommendations.append(rec)

        return recommendations

    def _analyze_system_resources(self) -> List[OptimizationRecommendation]:
        """Analyze system resource usage for optimization opportunities."""
        recommendations = []
        current_metrics = self.resource_monitor.get_current_metrics()

        if not current_metrics:
            return recommendations

        # Check CPU usage
        cpu_usage = current_metrics.get("cpu_usage", 0)
        if cpu_usage > self.optimization_rules["high_cpu_usage"]["threshold"]:
            for strategy in self.optimization_rules["high_cpu_usage"]["strategies"]:
                rec = self._create_recommendation(
                    "system",
                    strategy,
                    f"Optimize high CPU usage ({cpu_usage:.1f}%)",
                    self._estimate_improvement(strategy, "cpu_usage", cpu_usage),
                    0.7  # confidence
                )
                recommendations.append(rec)

        # Check memory usage
        memory_usage = current_metrics.get("memory_usage", 0)
        if memory_usage > self.optimization_rules["high_memory_usage"]["threshold"]:
            for strategy in self.optimization_rules["high_memory_usage"]["strategies"]:
                rec = self._create_recommendation(
                    "system",
                    strategy,
                    f"Optimize high memory usage ({memory_usage:.1f}%)",
                    self._estimate_improvement(strategy, "memory_usage", memory_usage),
                    0.6  # confidence
                )
                recommendations.append(rec)

        return recommendations

    def _create_recommendation(self, operation_name: str, strategy: OptimizationStrategy,
                             description: str, expected_improvement: float,
                             confidence_score: float) -> OptimizationRecommendation:
        """Create optimization recommendation."""

        # Estimate implementation complexity and effort
        complexity_map = {
            OptimizationStrategy.CACHING: ("Low", 4),
            OptimizationStrategy.BATCHING: ("Medium", 8),
            OptimizationStrategy.PARALLELIZATION: ("High", 16),
            OptimizationStrategy.LAZY_LOADING: ("Medium", 6),
            OptimizationStrategy.CONNECTION_POOLING: ("Medium", 8),
            OptimizationStrategy.MEMORY_OPTIMIZATION: ("High", 20),
            OptimizationStrategy.CPU_OPTIMIZATION: ("High", 24),
            OptimizationStrategy.IO_OPTIMIZATION: ("Medium", 12)
        }

        complexity, effort_hours = complexity_map.get(strategy, ("Medium", 10))

        # Calculate priority (higher expected improvement and confidence = higher priority)
        priority = int((expected_improvement * confidence_score) * 100)

        return OptimizationRecommendation(
            id=str(uuid.uuid4()),
            operation_name=operation_name,
            strategy=strategy,
            description=description,
            expected_improvement=expected_improvement,
            confidence_score=confidence_score,
            implementation_complexity=complexity,
            estimated_effort_hours=effort_hours,
            priority=priority,
            created_at=datetime.now(timezone.utc)
        )

    def _estimate_improvement(self, strategy: OptimizationStrategy,
                            metric_type: str, current_value: float) -> float:
        """Estimate performance improvement for optimization strategy."""

        # Improvement estimates based on strategy and metric type
        improvement_matrix = {
            OptimizationStrategy.CACHING: {
                "response_time": 0.6,  # 60% improvement
                "throughput": 0.8,     # 80% improvement
                "cpu_usage": 0.3       # 30% improvement
            },
            OptimizationStrategy.BATCHING: {
                "throughput": 0.5,     # 50% improvement
                "response_time": 0.2,  # 20% improvement
                "cpu_usage": 0.2       # 20% improvement
            },
            OptimizationStrategy.PARALLELIZATION: {
                "response_time": 0.4,  # 40% improvement
                "throughput": 0.7,     # 70% improvement
                "cpu_usage": -0.2      # May increase CPU usage
            },
            OptimizationStrategy.CONNECTION_POOLING: {
                "error_rate": 0.8,     # 80% improvement
                "response_time": 0.3,  # 30% improvement
                "throughput": 0.4      # 40% improvement
            },
            OptimizationStrategy.MEMORY_OPTIMIZATION: {
                "memory_usage": 0.4,   # 40% improvement
                "response_time": 0.2,  # 20% improvement
                "throughput": 0.1      # 10% improvement
            }
        }

        strategy_improvements = improvement_matrix.get(strategy, {})
        base_improvement = strategy_improvements.get(metric_type, 0.1)  # Default 10%

        # Adjust improvement based on current value severity
        if metric_type in ["response_time", "error_rate", "cpu_usage", "memory_usage"]:
            # Higher current values have more room for improvement
            severity_multiplier = min(current_value / 100, 2.0)  # Cap at 2x
            return base_improvement * severity_multiplier
        else:
            return base_improvement

    def apply_recommendation(self, recommendation_id: str) -> bool:
        """Apply an optimization recommendation."""
        with self._lock:
            if recommendation_id not in self.recommendations:
                return False

            recommendation = self.recommendations[recommendation_id]

            if recommendation.applied:
                logger.warning("Recommendation already applied",
                             recommendation_id=recommendation_id)
                return False

            # Mark as applied
            recommendation.applied = True
            recommendation.applied_at = datetime.now(timezone.utc)
            self.applied_optimizations.add(recommendation_id)

            # Log the application
            logger.info("Optimization recommendation applied",
                       recommendation_id=recommendation_id,
                       operation=recommendation.operation_name,
                       strategy=recommendation.strategy.value,
                       expected_improvement=recommendation.expected_improvement)

            # Update metrics
            increment_counter("optimizations_applied", 1, {
                "strategy": recommendation.strategy.value,
                "operation": recommendation.operation_name
            })

            return True

    def measure_optimization_impact(self, recommendation_id: str) -> Optional[float]:
        """Measure the actual impact of an applied optimization."""
        with self._lock:
            if recommendation_id not in self.recommendations:
                return None

            recommendation = self.recommendations[recommendation_id]

            if not recommendation.applied or not recommendation.applied_at:
                return None

            # Get performance data before and after optimization
            operation_name = recommendation.operation_name
            profile = self.profiler.get_profile(operation_name)

            if not profile:
                return None

            # For simplicity, we'll estimate impact based on recent performance
            # In a real implementation, you'd compare pre/post optimization metrics

            # Simulate measuring actual improvement (would be based on real metrics)
            actual_improvement = recommendation.expected_improvement * 0.8  # 80% of expected

            recommendation.actual_improvement = actual_improvement

            logger.info("Optimization impact measured",
                       recommendation_id=recommendation_id,
                       expected_improvement=recommendation.expected_improvement,
                       actual_improvement=actual_improvement)

            return actual_improvement

    def get_optimization_report(self) -> Dict[str, Any]:
        """Generate comprehensive optimization report."""
        with self._lock:
            total_recommendations = len(self.recommendations)
            applied_recommendations = len(self.applied_optimizations)

            # Calculate average improvements
            applied_recs = [r for r in self.recommendations.values() if r.applied]
            avg_expected_improvement = 0.0
            avg_actual_improvement = 0.0

            if applied_recs:
                avg_expected_improvement = statistics.mean([r.expected_improvement for r in applied_recs])
                actual_improvements = [r.actual_improvement for r in applied_recs if r.actual_improvement is not None]
                if actual_improvements:
                    avg_actual_improvement = statistics.mean(actual_improvements)

            # Group by strategy
            strategy_stats = defaultdict(int)
            for rec in self.recommendations.values():
                strategy_stats[rec.strategy.value] += 1

            return {
                "report_timestamp": datetime.now(timezone.utc).isoformat(),
                "total_recommendations": total_recommendations,
                "applied_recommendations": applied_recommendations,
                "application_rate": applied_recommendations / total_recommendations if total_recommendations > 0 else 0,
                "avg_expected_improvement": avg_expected_improvement,
                "avg_actual_improvement": avg_actual_improvement,
                "improvement_accuracy": avg_actual_improvement / avg_expected_improvement if avg_expected_improvement > 0 else 0,
                "recommendations_by_strategy": dict(strategy_stats),
                "top_recommendations": [
                    rec.to_dict() for rec in sorted(
                        [r for r in self.recommendations.values() if not r.applied],
                        key=lambda x: x.priority,
                        reverse=True
                    )[:5]
                ]
            }


def get_performance_status() -> Dict[str, Any]:
    """Get comprehensive performance system status."""
    return {
        "cache": advanced_cache.get_stats(),
        "resource_monitor": {
            "current_metrics": resource_monitor.get_current_metrics(),
            "monitoring_active": resource_monitor._monitoring
        },
        "profiler": {
            "tracked_operations": len(performance_profiler.profiles),
            "total_metrics": sum(len(metrics) for metrics in performance_profiler.operation_metrics.values())
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }


# Initialize optimization engine
optimization_engine = OptimizationEngine(performance_profiler, resource_monitor)
