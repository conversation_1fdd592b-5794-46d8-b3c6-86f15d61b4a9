#!/usr/bin/env python3
"""
Real Honeypot Detection Test - Production Quality Validation
Tests the actual implementation with real APIs and data
"""

import asyncio
import sys
import os
from datetime import datetime
sys.path.append('src')

async def test_real_honeypot_detection():
    """Test real honeypot detection with actual APIs"""
    print("🛡️ REAL HONEYPOT DETECTION TEST")
    print("=" * 60)
    
    try:
        from src.agents.scam_detector import AdvancedScamDetector
        from src.core.cache import CacheManager
        
        # Initialize components
        cache_manager = CacheManager()
        await cache_manager.initialize()
        
        scam_detector = AdvancedScamDetector(cache_manager)
        await scam_detector.initialize()
        
        print("✅ Components initialized successfully")
        
        # Test tokens - mix of legitimate and potentially problematic
        test_tokens = [
            {
                "name": "USDC (Legitimate)",
                "address": "******************************************",
                "chain_id": 1,
                "expected_safe": True
            },
            {
                "name": "WETH (Legitimate)", 
                "address": "******************************************",
                "chain_id": 1,
                "expected_safe": True
            },
            {
                "name": "PEPE (Meme but legitimate)",
                "address": "******************************************",
                "chain_id": 1,
                "expected_safe": True
            }
        ]
        
        print(f"\n🧪 Testing {len(test_tokens)} tokens with real APIs...")
        print("-" * 60)
        
        results = []
        
        for i, token in enumerate(test_tokens, 1):
            print(f"\n{i}. Testing {token['name']}")
            print(f"   Address: {token['address']}")
            print(f"   Chain: {token['chain_id']}")
            
            start_time = datetime.now()
            
            try:
                # Test individual API methods first
                print("   📡 Testing Honeypot.is API...")
                honeypot_result = await scam_detector._check_honeypot_is(
                    token['address'], token['chain_id']
                )
                
                if honeypot_result.get('simulation_success'):
                    print(f"      ✅ Honeypot.is: {honeypot_result.get('risk', 'unknown')} risk")
                    print(f"      📊 Is Honeypot: {honeypot_result.get('is_honeypot', 'unknown')}")
                    print(f"      💰 Buy Tax: {honeypot_result.get('buy_tax', 0)}%")
                    print(f"      💰 Sell Tax: {honeypot_result.get('sell_tax', 0)}%")
                else:
                    print(f"      ⚠️  Honeypot.is failed: {honeypot_result.get('error', 'Unknown error')}")
                
                print("   📡 Testing GoPlus API...")
                goplus_result = await scam_detector._check_goplus_security(
                    token['address'], token['chain_id']
                )
                
                if goplus_result.get('simulation_success'):
                    print(f"      ✅ GoPlus: Analysis successful")
                    print(f"      📊 Is Honeypot: {goplus_result.get('is_honeypot', 'unknown')}")
                    print(f"      💰 Buy Tax: {goplus_result.get('buy_tax', 0)}%")
                    print(f"      💰 Sell Tax: {goplus_result.get('sell_tax', 0)}%")
                    print(f"      👥 Holders: {goplus_result.get('holder_count', 0):,}")
                else:
                    print(f"      ⚠️  GoPlus failed: {goplus_result.get('error', 'Unknown error')}")
                
                # Test combined honeypot detection
                print("   🔬 Testing combined detection...")
                combined_result = await scam_detector._detect_honeypot(
                    token['address'], token['chain_id']
                )
                
                end_time = datetime.now()
                analysis_time = (end_time - start_time).total_seconds()
                
                print(f"   📊 COMBINED RESULTS:")
                print(f"      Is Honeypot: {combined_result.get('is_honeypot', 'unknown')}")
                print(f"      Can Buy: {combined_result.get('can_buy', 'unknown')}")
                print(f"      Can Sell: {combined_result.get('can_sell', 'unknown')}")
                print(f"      Buy Tax: {combined_result.get('buy_tax', 0)}%")
                print(f"      Sell Tax: {combined_result.get('sell_tax', 0)}%")
                print(f"      Analysis Time: {analysis_time:.2f}s")
                
                # Validate against expectations
                is_safe = not combined_result.get('is_honeypot', True)
                expected_safe = token['expected_safe']
                
                if is_safe == expected_safe:
                    print(f"   ✅ RESULT MATCHES EXPECTATION: {'Safe' if is_safe else 'Risky'}")
                    status = "PASS"
                else:
                    print(f"   ❌ RESULT MISMATCH: Expected {'Safe' if expected_safe else 'Risky'}, Got {'Safe' if is_safe else 'Risky'}")
                    status = "FAIL"
                
                results.append({
                    "token": token['name'],
                    "address": token['address'],
                    "analysis_time": analysis_time,
                    "is_honeypot": combined_result.get('is_honeypot', True),
                    "buy_tax": combined_result.get('buy_tax', 0),
                    "sell_tax": combined_result.get('sell_tax', 0),
                    "can_buy": combined_result.get('can_buy', False),
                    "can_sell": combined_result.get('can_sell', False),
                    "simulation_success": combined_result.get('simulation_success', False),
                    "status": status,
                    "honeypot_api_success": honeypot_result.get('simulation_success', False),
                    "goplus_api_success": goplus_result.get('simulation_success', False)
                })
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results.append({
                    "token": token['name'],
                    "address": token['address'],
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # Summary Report
        print("\n" + "=" * 60)
        print("📊 REAL HONEYPOT DETECTION SUMMARY")
        print("-" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('status') == 'PASS')
        failed_tests = sum(1 for r in results if r.get('status') == 'FAIL')
        error_tests = sum(1 for r in results if r.get('status') == 'ERROR')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        
        # API Success Rates
        honeypot_success = sum(1 for r in results if r.get('honeypot_api_success', False))
        goplus_success = sum(1 for r in results if r.get('goplus_api_success', False))
        
        print(f"\nAPI Success Rates:")
        print(f"Honeypot.is: {honeypot_success}/{total_tests} ({honeypot_success/total_tests*100:.1f}%)")
        print(f"GoPlus: {goplus_success}/{total_tests} ({goplus_success/total_tests*100:.1f}%)")
        
        # Performance Metrics
        successful_results = [r for r in results if 'analysis_time' in r]
        if successful_results:
            avg_time = sum(r['analysis_time'] for r in successful_results) / len(successful_results)
            max_time = max(r['analysis_time'] for r in successful_results)
            min_time = min(r['analysis_time'] for r in successful_results)
            
            print(f"\nPerformance Metrics:")
            print(f"Average Analysis Time: {avg_time:.2f}s")
            print(f"Max Analysis Time: {max_time:.2f}s")
            print(f"Min Analysis Time: {min_time:.2f}s")
            
            # Check if we meet the <30s requirement
            if max_time < 30:
                print("✅ Performance Requirement Met: <30s analysis time")
            else:
                print("❌ Performance Requirement Failed: >30s analysis time")
        
        # Final Assessment
        print("\n" + "=" * 60)
        print("🎯 FINAL ASSESSMENT:")
        print("-" * 60)
        
        if passed_tests == total_tests and error_tests == 0:
            print("✅ REAL HONEYPOT DETECTION: FULLY FUNCTIONAL")
            print("   - All tests passed with real API data")
            print("   - Both Honeypot.is and GoPlus APIs working")
            print("   - Performance meets requirements")
            print("   - Mock implementations successfully replaced")
        elif passed_tests >= total_tests * 0.8:
            print("⚠️  REAL HONEYPOT DETECTION: MOSTLY FUNCTIONAL")
            print("   - Most tests passed with real API data")
            print("   - Some API issues may need attention")
            print("   - Overall system is operational")
        else:
            print("❌ REAL HONEYPOT DETECTION: NEEDS IMPROVEMENT")
            print("   - Significant test failures detected")
            print("   - API integration issues present")
            print("   - Further debugging required")
        
        await scam_detector.shutdown()
        # Cache manager doesn't have shutdown method
        # await cache_manager.shutdown()
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_honeypot_detection())
