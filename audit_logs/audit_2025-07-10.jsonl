{"id": "6e55ec93-95dd-4d74-9a44-1b6eab2fa0f3", "timestamp": "2025-07-10T06:28:30.093992+00:00", "event_type": "data_access", "user_id": "test_user", "resource_id": "test_resource", "action": "test_action", "details": {"test": "data"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "067200e9-1e36-4694-84c4-31bdbe640edd", "timestamp": "2025-07-10T06:28:30.094827+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject_123", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data", "behavioral_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "c4b6e40c-384f-4b4b-bc2b-2c4fd81a00e4", "timestamp": "2025-07-10T06:28:48.155928+00:00", "event_type": "data_access", "user_id": "test_user_1", "resource_id": "resource_1", "action": "view_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "d50199ce-0535-485a-92bf-886c477463a6", "timestamp": "2025-07-10T06:28:48.156240+00:00", "event_type": "data_modification", "user_id": "test_user_2", "resource_id": "resource_2", "action": "update_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "fa9e5014-d5b6-481f-b15f-35c722351442", "timestamp": "2025-07-10T06:28:48.156733+00:00", "event_type": "data_deletion", "user_id": "test_user_3", "resource_id": "resource_3", "action": "delete_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "77ec28da-dcb0-4884-b613-a7c2be1b9168", "timestamp": "2025-07-10T06:28:48.157140+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject_1752128928", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data", "behavioral_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "47849d0b-992e-48ab-b018-fb6cee8b6030", "timestamp": "2025-07-10T06:28:48.157389+00:00", "event_type": "data_modification", "user_id": null, "resource_id": "test_subject_1752128928", "action": "record_consent", "details": {"purpose": "data_processing", "consent_given": true, "legal_basis": "consent", "consent_id": "cce86b4e-5f21-4d69-a37e-a45679ffb0da"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "medium"}
{"id": "548b036d-a5b7-436b-acbd-8d9c62832674", "timestamp": "2025-07-10T06:28:48.157561+00:00", "event_type": "data_deletion", "user_id": null, "resource_id": "test_subject_1752128928", "action": "request_data_deletion", "details": {"reason": "test_deletion"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "high"}
{"id": "2340af2b-0284-4fc2-979e-14cc5e0b9ec1", "timestamp": "2025-07-10T06:28:50.176260+00:00", "event_type": "data_access", "user_id": "test_user_1", "resource_id": "resource_1", "action": "view_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "7abfc6a0-180a-4218-99f4-c524f133f95f", "timestamp": "2025-07-10T06:28:50.176658+00:00", "event_type": "data_modification", "user_id": "test_user_2", "resource_id": "resource_2", "action": "update_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "e8243af1-9385-474d-9c8a-b2c3bf81584d", "timestamp": "2025-07-10T06:28:50.177025+00:00", "event_type": "data_deletion", "user_id": "test_user_3", "resource_id": "resource_3", "action": "delete_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "32d2a0fb-2c23-49df-be5a-03cbd026e7c5", "timestamp": "2025-07-10T06:28:50.177477+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject_1752128930", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data", "behavioral_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "c53c1005-988d-4e32-8184-e9a881403698", "timestamp": "2025-07-10T06:28:50.177749+00:00", "event_type": "data_modification", "user_id": null, "resource_id": "test_subject_1752128930", "action": "record_consent", "details": {"purpose": "data_processing", "consent_given": true, "legal_basis": "consent", "consent_id": "213ea483-de41-4422-9d5e-c3de9ab18413"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "medium"}
{"id": "552d356d-c869-4de5-b652-393ef85096f9", "timestamp": "2025-07-10T06:28:50.177901+00:00", "event_type": "data_deletion", "user_id": null, "resource_id": "test_subject_1752128930", "action": "request_data_deletion", "details": {"reason": "test_deletion"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "high"}
{"id": "1d0385b6-e8a5-47f2-93ec-feffe6bc5746", "timestamp": "2025-07-10T06:28:52.235280+00:00", "event_type": "data_access", "user_id": "test_user_1", "resource_id": "resource_1", "action": "view_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "749ba220-a370-44e2-8dd3-ed540cda9135", "timestamp": "2025-07-10T06:28:52.235584+00:00", "event_type": "data_modification", "user_id": "test_user_2", "resource_id": "resource_2", "action": "update_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "55a8eaae-bbc1-4aa2-88c1-51bae5cd2b1c", "timestamp": "2025-07-10T06:28:52.235703+00:00", "event_type": "data_deletion", "user_id": "test_user_3", "resource_id": "resource_3", "action": "delete_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "3cfc4d42-1f7c-4b55-8719-be64c6ef96e4", "timestamp": "2025-07-10T06:28:52.235896+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject_1752128932", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data", "behavioral_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "ec784cd9-265d-41c1-9547-52dcd8868f3e", "timestamp": "2025-07-10T06:28:52.236251+00:00", "event_type": "data_modification", "user_id": null, "resource_id": "test_subject_1752128932", "action": "record_consent", "details": {"purpose": "data_processing", "consent_given": true, "legal_basis": "consent", "consent_id": "97f10a77-d56f-4e6b-8d69-dcacf739ef00"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "medium"}
{"id": "7113bc9e-cef6-4dbb-b43c-3c829f64fc66", "timestamp": "2025-07-10T06:28:52.236405+00:00", "event_type": "data_deletion", "user_id": null, "resource_id": "test_subject_1752128932", "action": "request_data_deletion", "details": {"reason": "test_deletion"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "high"}
{"id": "6a4c75c1-6aa6-400f-93db-282a8f56f695", "timestamp": "2025-07-10T06:31:00.843808+00:00", "event_type": "data_access", "user_id": "test_user", "resource_id": "test_resource", "action": "test_action", "details": {"test": "data"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "c7f24a3f-77c9-40eb-a850-d04cb10f3281", "timestamp": "2025-07-10T06:31:00.844501+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject_123", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data", "behavioral_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "6618882e-acc2-421b-af37-d8d731fd7f6e", "timestamp": "2025-07-10T06:31:06.773461+00:00", "event_type": "data_access", "user_id": "test_user", "resource_id": "test_resource", "action": "integration_test", "details": {"test": "compliance_integration"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "41aea265-3971-4b01-bf52-f721bbf4c110", "timestamp": "2025-07-10T06:31:06.773791+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "63dc1ea0-7413-4d47-8706-155e15693f61", "timestamp": "2025-07-10T06:31:08.833830+00:00", "event_type": "data_access", "user_id": "test_user_1", "resource_id": "resource_1", "action": "view_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "2ebc9e54-5bec-4ac7-89cd-165e929eb08e", "timestamp": "2025-07-10T06:31:08.834279+00:00", "event_type": "data_modification", "user_id": "test_user_2", "resource_id": "resource_2", "action": "update_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "2f1e79e2-1ba5-4cc2-ad1b-80b41c5d6b5e", "timestamp": "2025-07-10T06:31:08.834704+00:00", "event_type": "data_deletion", "user_id": "test_user_3", "resource_id": "resource_3", "action": "delete_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "2840bc9d-befd-4666-b71e-a8a18926b025", "timestamp": "2025-07-10T06:31:08.834951+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject_1752129068", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data", "behavioral_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "1b96f1b4-c1a6-45ac-b893-71584fa65d66", "timestamp": "2025-07-10T06:31:08.835087+00:00", "event_type": "data_modification", "user_id": null, "resource_id": "test_subject_1752129068", "action": "record_consent", "details": {"purpose": "data_processing", "consent_given": true, "legal_basis": "consent", "consent_id": "462b0587-4cd9-4a93-a063-e6897825f736"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "medium"}
{"id": "fa846be5-a52c-44aa-bd51-5a5a0574d969", "timestamp": "2025-07-10T06:31:08.835212+00:00", "event_type": "data_deletion", "user_id": null, "resource_id": "test_subject_1752129068", "action": "request_data_deletion", "details": {"reason": "test_deletion"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "high"}
{"id": "6bd8a7cb-96fa-4b83-a682-1f236c3cf3c3", "timestamp": "2025-07-10T06:31:10.848357+00:00", "event_type": "data_access", "user_id": "test_user_1", "resource_id": "resource_1", "action": "view_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "2b73e231-33c0-4d60-9595-8fbbea2c768e", "timestamp": "2025-07-10T06:31:10.850354+00:00", "event_type": "data_modification", "user_id": "test_user_2", "resource_id": "resource_2", "action": "update_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "3571a3a3-bd18-4bd8-a899-6c7a593e0f64", "timestamp": "2025-07-10T06:31:10.850705+00:00", "event_type": "data_deletion", "user_id": "test_user_3", "resource_id": "resource_3", "action": "delete_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "c1f67f76-6659-488a-9d0a-0056d98c6d24", "timestamp": "2025-07-10T06:31:10.851172+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject_1752129070", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data", "behavioral_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "6d61d7da-9e24-4a96-9ac5-1e261ebd2a38", "timestamp": "2025-07-10T06:31:10.851493+00:00", "event_type": "data_modification", "user_id": null, "resource_id": "test_subject_1752129070", "action": "record_consent", "details": {"purpose": "data_processing", "consent_given": true, "legal_basis": "consent", "consent_id": "8d21b025-2693-40c7-b38f-26bb1fe7b646"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "medium"}
{"id": "8f1b09e2-97d9-4458-9604-81965a2b6269", "timestamp": "2025-07-10T06:31:10.851650+00:00", "event_type": "data_deletion", "user_id": null, "resource_id": "test_subject_1752129070", "action": "request_data_deletion", "details": {"reason": "test_deletion"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "high"}
{"id": "096f0bce-6ee5-4445-bc19-a72f59815d97", "timestamp": "2025-07-10T06:31:10.852646+00:00", "event_type": "data_access", "user_id": "test_user", "resource_id": "test_resource", "action": "integration_test", "details": {"test": "compliance_integration"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "480fbc85-910f-45ef-8d0e-806cd85f02a6", "timestamp": "2025-07-10T06:31:12.906288+00:00", "event_type": "data_access", "user_id": "test_user_1", "resource_id": "resource_1", "action": "view_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "c0eed625-f6d0-4565-b010-5ab6d836142b", "timestamp": "2025-07-10T06:31:12.907136+00:00", "event_type": "data_modification", "user_id": "test_user_2", "resource_id": "resource_2", "action": "update_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "0515f2d0-83bf-415d-b389-64c9cc45df25", "timestamp": "2025-07-10T06:31:12.908835+00:00", "event_type": "data_deletion", "user_id": "test_user_3", "resource_id": "resource_3", "action": "delete_data", "details": {"test": "audit_integrity"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "4b69d21c-5e80-4868-96a5-789a990725e4", "timestamp": "2025-07-10T06:31:12.909402+00:00", "event_type": "data_access", "user_id": null, "resource_id": "test_subject_1752129072", "action": "register_data_subject", "details": {"email": "<EMAIL>", "data_categories": ["personal_data", "behavioral_data"]}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "low"}
{"id": "18132653-e932-417e-b45a-e17ce27b9a2d", "timestamp": "2025-07-10T06:31:12.909612+00:00", "event_type": "data_modification", "user_id": null, "resource_id": "test_subject_1752129072", "action": "record_consent", "details": {"purpose": "data_processing", "consent_given": true, "legal_basis": "consent", "consent_id": "fa1493bd-b0ce-42f2-82ff-5086ce4d1d0d"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "medium"}
{"id": "c2207de3-7e4e-4c6a-b097-fb8f710e9e0c", "timestamp": "2025-07-10T06:31:12.909993+00:00", "event_type": "data_deletion", "user_id": null, "resource_id": "test_subject_1752129072", "action": "request_data_deletion", "details": {"reason": "test_deletion"}, "ip_address": null, "user_agent": null, "correlation_id": null, "compliance_relevant": true, "risk_level": "high"}
