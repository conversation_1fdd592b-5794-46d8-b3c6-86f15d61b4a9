#!/usr/bin/env python3
import asyncio
import sys
sys.path.append('src')

async def quick_birdeye_test():
    from src.agents.discovery import DiscoveryAgent
    from src.core.database import DatabaseManager
    from src.core.cache import CacheManager
    from src.integrations.metrics import MetricsCollector
    from src.agents.coordinator import AgentCoordinator
    
    db_manager = DatabaseManager()
    cache_manager = CacheManager()
    await cache_manager.initialize()
    metrics_collector = MetricsCollector()
    coordinator = AgentCoordinator(db_manager, cache_manager, metrics_collector)
    
    discovery = DiscoveryAgent(db_manager, cache_manager, metrics_collector, coordinator)
    await discovery.initialize()
    
    print('🐦 Quick Birdeye Test')
    result = await discovery._discover_from_birdeye(24, 5)
    tokens = result.get('tokens', [])
    print(f'✅ Found {len(tokens)} Solana tokens')
    
    if tokens:
        sample = tokens[0]
        print(f'📊 Sample: {sample.get("symbol")} - ${sample.get("price_usd", 0):.6f}')
        print(f'🔗 Chain: {sample.get("chain")}')
    
    await discovery.shutdown()

if __name__ == "__main__":
    asyncio.run(quick_birdeye_test())
