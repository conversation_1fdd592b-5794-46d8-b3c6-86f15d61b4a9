#!/usr/bin/env python3
"""
AutoGen + OpenRouter Integration Test - Production Quality Validation
Tests the AutoGen GroupChat with DeepSeek R1 via OpenRouter
"""

import asyncio
import sys
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

sys.path.append('src')

async def test_autogen_openrouter():
    """Test AutoGen GroupChat with OpenRouter DeepSeek R1"""
    print("🤖 AUTOGEN + OPENROUTER INTEGRATION TEST")
    print("=" * 60)
    
    try:
        # Check environment setup
        print("🔍 Checking Environment Setup...")
        openrouter_key = os.getenv("OPENROUTER_API_KEY")
        if not openrouter_key:
            print("❌ OPENROUTER_API_KEY not found in environment")
            return
        
        print(f"✅ OpenRouter API Key: {openrouter_key[:20]}...")
        
        # Import AutoGen components
        print("\n📦 Importing AutoGen Components...")
        try:
            from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
            print("✅ AutoGen components imported successfully")
        except ImportError as e:
            print(f"❌ AutoGen import failed: {e}")
            return
        
        # Test LLM configuration
        print("\n⚙️  Testing LLM Configuration...")
        llm_config = {
            "config_list": [
                {
                    "model": "deepseek/deepseek-r1-0528-qwen3-8b:free",
                    "api_key": openrouter_key,
                    "base_url": "https://openrouter.ai/api/v1",
                    "api_type": "openai"
                }
            ],
            "temperature": 0.1,
            "timeout": 120,
            "cache_seed": None
        }
        print("✅ LLM configuration created")
        
        # Create test agents
        print("\n🤖 Creating AutoGen Agents...")
        
        # Scheduler Agent
        scheduler = AssistantAgent(
            name="scheduler",
            system_message="""You are the Scheduler Agent responsible for coordinating token analysis.
            
Your responsibilities:
- Receive analysis requests with token addresses
- Validate input parameters and format
- Initiate the analysis pipeline by calling the Discovery Agent
- Monitor overall progress and handle timeouts

Always respond with structured JSON containing the next agent to call and required parameters.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=2
        )
        
        # Discovery Agent
        discovery = AssistantAgent(
            name="discovery",
            system_message="""You are the Discovery Agent responsible for token discovery and validation.
            
Your responsibilities:
- Validate token addresses and chain compatibility
- Perform basic contract existence checks
- Gather initial token metadata (name, symbol, decimals)
- Check for known scam/rug-pull indicators
- Pass validated tokens to ChainInfo Agent

Always include confidence scores in your responses.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=3
        )
        
        # Analyst Agent
        analyst = AssistantAgent(
            name="analyst",
            system_message="""You are the Analyst Agent responsible for comprehensive token analysis.
            
Your responsibilities:
- Synthesize all collected data into actionable insights
- Generate investment recommendations with confidence scores
- Identify key risks and opportunities
- Provide clear, structured analysis reports

Always provide specific recommendations: BUY, SELL, HOLD with reasoning.""",
            llm_config=llm_config,
            max_consecutive_auto_reply=3
        )
        
        # User Proxy Agent
        user_proxy = UserProxyAgent(
            name="user_proxy",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=0,
            code_execution_config=False
        )
        
        print("✅ Created 4 AutoGen agents")
        
        # Create GroupChat
        print("\n👥 Creating AutoGen GroupChat...")
        group_chat = GroupChat(
            agents=[user_proxy, scheduler, discovery, analyst],
            messages=[],
            max_round=10,
            speaker_selection_method="round_robin",
            allow_repeat_speaker=False
        )
        
        # Create GroupChat Manager
        manager = GroupChatManager(
            groupchat=group_chat,
            llm_config=llm_config
        )
        
        print("✅ GroupChat and Manager created successfully")
        
        # Test scenarios
        test_scenarios = [
            {
                "name": "Simple Token Analysis Request",
                "message": "Please analyze token USDC (******************************************) on Ethereum mainnet",
                "expected_agents": ["scheduler", "discovery", "analyst"]
            },
            {
                "name": "Multi-Token Analysis",
                "message": "Analyze and compare WETH vs USDC for investment potential",
                "expected_agents": ["scheduler", "discovery", "analyst"]
            }
        ]
        
        print(f"\n🧪 Testing {len(test_scenarios)} AutoGen scenarios...")
        print("-" * 60)
        
        results = []
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n{i}. Testing {scenario['name']}")
            print(f"   Message: {scenario['message']}")
            
            start_time = datetime.now()
            
            try:
                # Initiate chat with timeout
                print("   🚀 Initiating AutoGen GroupChat...")
                
                # Use asyncio.wait_for to add timeout
                chat_result = await asyncio.wait_for(
                    user_proxy.a_initiate_chat(
                        manager,
                        message=scenario['message'],
                        max_turns=5
                    ),
                    timeout=60.0  # 60 second timeout
                )
                
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                print(f"   📊 RESULTS:")
                print(f"      Chat completed: True")
                print(f"      Execution time: {execution_time:.2f}s")
                print(f"      Messages exchanged: {len(group_chat.messages)}")
                
                # Analyze the conversation
                if group_chat.messages:
                    last_message = group_chat.messages[-1]
                    print(f"      Final response from: {last_message.get('name', 'Unknown')}")
                    print(f"      Response length: {len(last_message.get('content', ''))}")
                    
                    # Check if expected agents participated
                    participating_agents = set()
                    for msg in group_chat.messages:
                        agent_name = msg.get('name', '')
                        if agent_name and agent_name != 'user_proxy':
                            participating_agents.add(agent_name)
                    
                    expected_agents = set(scenario['expected_agents'])
                    participation_score = len(participating_agents & expected_agents) / len(expected_agents)
                    
                    print(f"      Participating agents: {list(participating_agents)}")
                    print(f"      Expected agents: {list(expected_agents)}")
                    print(f"      Participation score: {participation_score:.2%}")
                    
                    if participation_score >= 0.5:
                        status = "PASS"
                        print(f"   ✅ {status}: GroupChat orchestration successful")
                    else:
                        status = "PARTIAL"
                        print(f"   ⚠️  {status}: Limited agent participation")
                else:
                    status = "FAIL"
                    print(f"   ❌ {status}: No messages exchanged")
                
                results.append({
                    "scenario": scenario['name'],
                    "status": status,
                    "execution_time": execution_time,
                    "messages_count": len(group_chat.messages),
                    "participating_agents": list(participating_agents),
                    "participation_score": participation_score if 'participation_score' in locals() else 0
                })
                
                # Clear messages for next test
                group_chat.messages = []
                
            except asyncio.TimeoutError:
                print(f"   ⏰ Test timed out after 60 seconds")
                results.append({
                    "scenario": scenario['name'],
                    "status": "TIMEOUT",
                    "error": "Test timed out"
                })
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results.append({
                    "scenario": scenario['name'],
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # Summary Report
        print("\n" + "=" * 60)
        print("📊 AUTOGEN + OPENROUTER SUMMARY")
        print("-" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('status') == 'PASS')
        partial_tests = sum(1 for r in results if r.get('status') == 'PARTIAL')
        failed_tests = sum(1 for r in results if r.get('status') == 'FAIL')
        timeout_tests = sum(1 for r in results if r.get('status') == 'TIMEOUT')
        error_tests = sum(1 for r in results if r.get('status') == 'ERROR')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Partial: {partial_tests} ({partial_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Timeouts: {timeout_tests} ({timeout_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        
        # Performance Metrics
        successful_results = [r for r in results if 'execution_time' in r]
        if successful_results:
            avg_time = sum(r['execution_time'] for r in successful_results) / len(successful_results)
            max_time = max(r['execution_time'] for r in successful_results)
            min_time = min(r['execution_time'] for r in successful_results)
            
            print(f"\nPerformance Metrics:")
            print(f"Average Execution Time: {avg_time:.2f}s")
            print(f"Max Execution Time: {max_time:.2f}s")
            print(f"Min Execution Time: {min_time:.2f}s")
            
            # Check cost efficiency
            total_messages = sum(r.get('messages_count', 0) for r in successful_results)
            print(f"Total Messages: {total_messages}")
            print(f"Average Messages per Test: {total_messages/len(successful_results):.1f}")
        
        # Final Assessment
        print("\n" + "=" * 60)
        print("🎯 FINAL ASSESSMENT:")
        print("-" * 60)
        
        success_rate = (passed_tests + partial_tests) / total_tests if total_tests > 0 else 0
        
        if success_rate >= 0.8 and error_tests == 0:
            print("✅ AUTOGEN + OPENROUTER: FULLY FUNCTIONAL")
            print("   - DeepSeek R1 integration working")
            print("   - GroupChat orchestration successful")
            print("   - Cost-efficient AI coordination achieved")
            print("   - Multi-agent workflows operational")
        elif success_rate >= 0.5:
            print("⚠️  AUTOGEN + OPENROUTER: MOSTLY FUNCTIONAL")
            print("   - Basic integration working")
            print("   - Some scenarios may need refinement")
            print("   - Overall system is operational")
        else:
            print("❌ AUTOGEN + OPENROUTER: NEEDS IMPROVEMENT")
            print("   - Significant integration issues")
            print("   - GroupChat coordination problems")
            print("   - Further debugging required")
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_autogen_openrouter())
