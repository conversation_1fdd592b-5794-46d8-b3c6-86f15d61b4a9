{"test_start_time": "2025-07-10 02:02:25.928631", "test_end_time": "2025-07-10 02:02:38.481776", "total_duration": 12.553145, "agent_communication_tests": [{"test_name": "Agent Communication - Basic Status Check", "success": false, "duration": 1.7881393432617188e-05, "details": {"agents_found": 0, "message_bus_status": {"total_queues": 0, "total_subscribers": 0, "delivery_guarantees": 0}, "event_store_status": {"total_events": 0, "total_snapshots": 0, "event_handlers": {"task_assigned": 1, "task_completed": 1}}}, "error": null, "timestamp": "2025-07-10 02:02:26.073065"}, {"test_name": "Agent Communication - Message Bus Reliability", "success": false, "duration": 7.152557373046875e-06, "details": {}, "error": "DATA", "timestamp": "2025-07-10 02:02:26.073073"}, {"test_name": "Agent Communication - Response Time Test", "success": true, "duration": 3.0994415283203125e-05, "details": {"agents_tested": 5, "average_response_time": 0.001, "max_response_time": 0.001, "min_response_time": 0.001, "response_times": [0.001, 0.001, 0.001, 0.001, 0.001]}, "error": null, "timestamp": "2025-07-10 02:02:26.073107"}], "agent_coordination_tests": [{"test_name": "Agent Coordination - Sequential Pipeline", "success": false, "duration": 0.32396388053894043, "details": {}, "error": "'dict' object has no attribute 'success'", "timestamp": "2025-07-10 02:02:26.397090"}, {"test_name": "Agent Coordination - Concurrent Operations", "success": false, "duration": 1.6326117515563965, "details": {}, "error": "'dict' object has no attribute 'success'", "timestamp": "2025-07-10 02:02:28.029711"}, {"test_name": "Agent Coordination - State Synchronization", "success": true, "duration": 1.7881393432617188e-05, "details": {"agents_checked": 9, "agent_states": {"discovery": {"status": "unknown"}, "validator": {"status": "unknown"}, "chain_info": {"status": "unknown"}, "market_data": {"status": "unknown"}, "technical": {"status": "unknown"}, "sentiment": {"status": "unknown"}, "analyst": {"status": "unknown"}, "audit": {"status": "unknown"}, "scheduler": {"status": "unknown"}}, "state_consistency": true}, "error": null, "timestamp": "2025-07-10 02:02:28.029737"}], "agent_fault_tolerance_tests": [{"test_name": "Agent <PERSON>ault Tolerance - Circuit Breaker", "success": false, "duration": 8.344650268554688e-06, "details": {"circuit_breakers_tested": 2, "circuit_breakers_working": 0, "agents_with_circuit_breakers": ["discovery", "validator", "chain_info", "market_data", "technical", "sentiment", "analyst", "audit", "scheduler"]}, "error": null, "timestamp": "2025-07-10 02:02:28.029876"}, {"test_name": "Agent <PERSON><PERSON> Tolerance - Graceful Degradation", "success": false, "duration": 1.0669801235198975, "details": {}, "error": "'dict' object has no attribute 'success'", "timestamp": "2025-07-10 02:02:29.096860"}], "agent_performance_metrics": {"discovery": {"agent_name": "discovery", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "validator": {"agent_name": "validator", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "chain_info": {"agent_name": "chain_info", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "market_data": {"agent_name": "market_data", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "sentiment": {"agent_name": "sentiment", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "analyst": {"agent_name": "analyst", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "audit": {"agent_name": "audit", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "scheduler": {"agent_name": "scheduler", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "technical": {"agent_name": "technical", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}}, "workflow_tests": [{"test_name": "Complete Workflow - Discovery to Analysis", "success": false, "duration": 1.1068060398101807, "details": {}, "error": "'dict' object has no attribute 'tokens'", "timestamp": "2025-07-10 02:02:30.203771"}], "api_integration_tests": [{"test_name": "API Integration - Honeypot.is", "success": false, "duration": 2.1457672119140625e-06, "details": {}, "error": "Honeypot check method not found", "timestamp": "2025-07-10 02:02:30.203835"}, {"test_name": "API Integration - GoPlus Labs", "success": false, "duration": 0.0, "details": {}, "error": "Contract security check method not found", "timestamp": "2025-07-10 02:02:30.203837"}, {"test_name": "API Integration - DexScreener", "success": false, "duration": 9.5367431640625e-07, "details": {}, "error": "Search tokens method not found", "timestamp": "2025-07-10 02:02:30.203839"}], "accuracy_tests": [{"test_name": "Accuracy Test - Legitimate Tokens", "success": false, "duration": 1.0889649391174316, "details": {}, "error": "'dict' object has no attribute 'success'", "timestamp": "2025-07-10 02:02:31.292822"}], "real_data_tests": [{"test_name": "Volatility Test - Multiple Crypto Types", "success": false, "duration": 1.066849946975708, "details": {}, "error": "'dict' object has no attribute 'success'", "timestamp": "2025-07-10 02:02:32.359724"}], "load_tests": [{"test_name": "Production Load - Concurrent Requests", "success": false, "duration": 5.0622780323028564, "details": {}, "error": "'dict' object has no attribute 'success'", "timestamp": "2025-07-10 02:02:37.422062"}], "stress_tests": [{"test_name": "Stress Test - Rapid Sequential Requests", "success": false, "duration": 1.0586051940917969, "details": {}, "error": "'dict' object has no attribute 'success'", "timestamp": "2025-07-10 02:02:38.480730"}], "error_handling_tests": [{"test_name": "Error Handling - API Failure Recovery", "success": true, "duration": 0.000823974609375, "details": {"invalid_source_handled": true}, "error": null, "timestamp": "2025-07-10 02:02:38.481649"}, {"test_name": "Error <PERSON>ling - Circuit Breaker Recovery", "success": false, "duration": 2.2172927856445312e-05, "details": {"circuit_breaker_recovery": false, "agents_tested": ["discovery", "validator", "chain_info", "market_data", "technical", "sentiment", "analyst", "audit", "scheduler"]}, "error": null, "timestamp": "2025-07-10 02:02:38.481674"}], "monitoring_tests": [{"test_name": "Monitoring - Metrics Collection", "success": false, "duration": 4.0531158447265625e-06, "details": {}, "error": "'MetricsCollector' object has no attribute 'record_metric'", "timestamp": "2025-07-10 02:02:38.481769"}, {"test_name": "Monitoring - System Health Checks", "success": true, "duration": 3.0994415283203125e-06, "details": {"health_checks": {"database": true, "cache": true, "agents": true}, "overall_health": true, "components_checked": 3}, "error": null, "timestamp": "2025-07-10 02:02:38.481775"}], "overall_success_rate": 20.0, "critical_failures": [], "recommendations": ["Strengthen error handling and recovery mechanisms", "Review and improve API integration reliability", "Enhance system capacity for production load handling", "Improve agent coordination and communication patterns", "Conduct thorough system review - high failure rate detected"]}