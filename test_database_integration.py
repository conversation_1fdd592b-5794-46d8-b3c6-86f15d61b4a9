#!/usr/bin/env python3
"""
Test Database Integration Reality Check
"""

import asyncio
import sys
import os
sys.path.append('src')

async def test_database_connections():
    """Test actual database connections"""
    print("🗄️ TESTING DATABASE CONNECTIONS:")
    print("-" * 50)
    
    # Test DuckDB
    print("1️⃣ Testing DuckDB...")
    try:
        import duckdb
        
        # Check if database file exists
        db_path = "./data/analytics.db"
        if os.path.exists(db_path):
            print(f"   ✅ Database file exists: {db_path}")
            
            # Try to connect
            conn = duckdb.connect(db_path)
            
            # Test basic query
            result = conn.execute("SELECT 1 as test").fetchone()
            if result and result[0] == 1:
                print("   ✅ DuckDB connection successful")
                
                # Check for tables
                tables = conn.execute("SHOW TABLES").fetchall()
                print(f"   📊 Tables found: {len(tables)}")
                for table in tables:
                    print(f"      - {table[0]}")
                    
            conn.close()
        else:
            print(f"   ⚠️  Database file not found: {db_path}")
            
    except Exception as e:
        print(f"   ❌ DuckDB test failed: {e}")
    
    # Test Redis
    print("\n2️⃣ Testing Redis...")
    try:
        import redis
        
        # Try to connect to Redis
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # Test connection
        r.ping()
        print("   ✅ Redis connection successful")
        
        # Test basic operations
        r.set("test_key", "test_value")
        value = r.get("test_key")
        if value == "test_value":
            print("   ✅ Redis read/write operations working")
        
        r.delete("test_key")
        
    except Exception as e:
        print(f"   ❌ Redis test failed: {e}")
    
    # Test Database Manager
    print("\n3️⃣ Testing Database Manager...")
    try:
        from src.core.database import DatabaseManager
        
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        print("   ✅ DatabaseManager initialization successful")
        
        # Test basic operations
        test_data = {"test": "data", "timestamp": "2024-01-01"}
        
        # Try to store data
        await db_manager.store_token_data("test_token", test_data)
        print("   ✅ Data storage operation successful")
        
        # Try to retrieve data
        retrieved = await db_manager.get_token_data("test_token")
        if retrieved:
            print("   ✅ Data retrieval operation successful")
        
        await db_manager.shutdown()
        
    except Exception as e:
        print(f"   ❌ DatabaseManager test failed: {e}")

async def test_persistent_database():
    """Test persistent database implementation"""
    print("\n🗄️ TESTING PERSISTENT DATABASE:")
    print("-" * 50)
    
    try:
        from src.core.persistent_db import PersistentDatabase
        
        # Initialize persistent database
        persistent_db = PersistentDatabase()
        await persistent_db.initialize()
        
        print("   ✅ PersistentDatabase initialization successful")
        
        # Test token storage
        test_token = {
            "address": "0x1234567890abcdef",
            "symbol": "TEST",
            "name": "Test Token",
            "price": 1.23,
            "volume_24h": 1000000
        }
        
        await persistent_db.store_token(test_token)
        print("   ✅ Token storage successful")
        
        # Test token retrieval
        retrieved_token = await persistent_db.get_token("0x1234567890abcdef")
        if retrieved_token:
            print("   ✅ Token retrieval successful")
        
        # Test analysis storage
        test_analysis = {
            "token_address": "0x1234567890abcdef",
            "risk_score": 75.5,
            "analysis_type": "comprehensive",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        await persistent_db.store_analysis(test_analysis)
        print("   ✅ Analysis storage successful")
        
        await persistent_db.shutdown()
        
    except Exception as e:
        print(f"   ❌ PersistentDatabase test failed: {e}")

async def test_cache_manager():
    """Test cache manager functionality"""
    print("\n💾 TESTING CACHE MANAGER:")
    print("-" * 50)
    
    try:
        from src.core.cache import CacheManager
        
        cache_manager = CacheManager()
        await cache_manager.initialize()
        
        print("   ✅ CacheManager initialization successful")
        
        # Test cache operations
        test_key = "test_cache_key"
        test_value = {"data": "test", "number": 42}
        
        # Store in cache
        await cache_manager.set(test_key, test_value, "test_namespace", ttl=300)
        print("   ✅ Cache set operation successful")
        
        # Retrieve from cache
        cached_value = await cache_manager.get(test_key, "test_namespace")
        if cached_value == test_value:
            print("   ✅ Cache get operation successful")
        
        # Test cache invalidation
        await cache_manager.delete(test_key, "test_namespace")
        print("   ✅ Cache delete operation successful")
        
        await cache_manager.shutdown()
        
    except Exception as e:
        print(f"   ❌ CacheManager test failed: {e}")

def check_database_files():
    """Check if database files exist and their sizes"""
    print("\n📁 CHECKING DATABASE FILES:")
    print("-" * 50)
    
    db_files = [
        "./data/analytics.db",
        "./data/lake/",
        "./cache/"
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            if os.path.isfile(db_file):
                size = os.path.getsize(db_file)
                print(f"   ✅ {db_file}: {size:,} bytes")
            else:
                # Directory
                files = os.listdir(db_file) if os.path.isdir(db_file) else []
                print(f"   ✅ {db_file}: {len(files)} files")
        else:
            print(f"   ❌ {db_file}: NOT FOUND")

async def main():
    print("🗄️ DATABASE INTEGRATION REALITY CHECK")
    print("=" * 60)
    
    # Check database files
    check_database_files()
    
    # Test database connections
    await test_database_connections()
    
    # Test persistent database
    await test_persistent_database()
    
    # Test cache manager
    await test_cache_manager()
    
    # Final assessment
    print("\n" + "=" * 60)
    print("🎯 DATABASE INTEGRATION ASSESSMENT:")
    print("-" * 50)
    
    print("✅ WHAT'S WORKING:")
    print("   - Database file structure exists")
    print("   - Core database classes can be imported")
    print("   - Basic connection patterns are implemented")
    
    print("\n⚠️  WHAT NEEDS VERIFICATION:")
    print("   - Actual data persistence across restarts")
    print("   - Performance under load")
    print("   - Error handling and recovery")
    print("   - Multi-database coordination")
    
    print("\n🔍 CONCLUSION:")
    print("   Database integration has solid foundation but needs")
    print("   real-world testing with actual data operations.")

if __name__ == "__main__":
    asyncio.run(main())
