#!/usr/bin/env python3
"""
Test production fixes for critical issues.
"""

import asyncio
import sys
import time
from pathlib import Path
from unittest.mock import MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()

from src.compliance.audit_framework import (
    AuditEventType,
    DataCategory,
    AuditTrail,
    GDPRComplianceManager
)
from src.ml.risk_calibration import (
    RiskFeatures,
    RiskLevel,
    risk_calibration_system
)
from src.pipelines.advanced_pipeline import (
    ValidationProcessor,
    DataRecord
)


async def test_audit_trail_fix():
    """Test audit trail compatibility fix."""
    print("🧪 Testing audit trail fix...")
    
    audit_trail = AuditTrail()
    
    # Test that AuditEventType alias works
    assert hasattr(audit_trail, 'AuditEventType')
    
    # Test logging events with proper enum
    event_id = audit_trail.log_event(
        AuditEventType.DATA_ACCESS,
        "test_user",
        "test_resource",
        "test_action",
        {"test": "data"}
    )
    
    assert event_id is not None
    
    # Test integrity verification
    integrity_ok = audit_trail.verify_integrity()
    assert integrity_ok == True
    
    print("✅ Audit trail fix verified")
    return True


async def test_gdpr_compliance_fix():
    """Test GDPR compliance enum fix."""
    print("🧪 Testing GDPR compliance fix...")

    audit_trail = AuditTrail()
    gdpr_manager = GDPRComplianceManager(audit_trail)
    
    # Test with proper enum values
    data_subject = gdpr_manager.register_data_subject(
        "test_subject_123",
        "<EMAIL>",
        {DataCategory.PERSONAL_DATA, DataCategory.BEHAVIORAL_DATA}
    )
    
    assert data_subject is not None
    assert data_subject.id == "test_subject_123"
    assert DataCategory.PERSONAL_DATA in data_subject.data_categories
    
    print("✅ GDPR compliance fix verified")
    return True


async def test_improved_risk_calibration():
    """Test improved risk calibration accuracy."""
    print("🧪 Testing improved risk calibration...")
    
    # Test high-risk scenario (should be detected accurately)
    high_risk_features = RiskFeatures(
        price_volatility=150.0,
        volume_24h=100000.0,
        market_cap=500000.0,
        liquidity_score=0.05,
        rsi=95.0,
        social_sentiment=-0.8,
        holder_concentration=0.95,
        unique_addresses=10,
        rug_pull_indicators=0.95,
        pump_dump_signals=0.9,
        wash_trading_score=0.85
    )
    
    high_risk_prediction = await risk_calibration_system.assess_risk(
        "0xhighrisk", high_risk_features
    )
    
    # Should detect as high risk
    assert high_risk_prediction.risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]
    assert high_risk_prediction.confidence > 0.5
    
    # Test low-risk scenario (should be detected accurately)
    low_risk_features = RiskFeatures(
        price_volatility=5.0,
        volume_24h=50000000.0,
        market_cap=1000000000.0,
        liquidity_score=0.95,
        rsi=45.0,
        social_sentiment=0.7,
        holder_concentration=0.02,
        unique_addresses=100000,
        rug_pull_indicators=0.0,
        pump_dump_signals=0.0,
        wash_trading_score=0.0
    )
    
    low_risk_prediction = await risk_calibration_system.assess_risk(
        "0xlowrisk", low_risk_features
    )
    
    # Should detect as low risk
    assert low_risk_prediction.risk_level in [RiskLevel.VERY_LOW, RiskLevel.LOW, RiskLevel.MEDIUM]
    
    print("✅ Improved risk calibration verified")
    return True


async def test_flexible_validation():
    """Test flexible data validation."""
    print("🧪 Testing flexible validation...")
    
    # Create validation schema that expects float for volume
    validation_schema = {
        "required": ["symbol", "price", "volume"],
        "types": {
            "symbol": str,
            "price": float,
            "volume": float  # This should accept int and convert to float
        }
    }
    
    validator = ValidationProcessor("test_validator", validation_schema)
    
    # Test record with int volume (should be converted to float)
    test_record = DataRecord(
        id="test_1",
        data={
            "symbol": "TEST",
            "price": 100.5,
            "volume": 1000  # int that should be converted to float
        }
    )
    
    # Process should succeed and convert int to float
    result = await validator.process(test_record)
    
    assert result is not None
    assert isinstance(result.data["volume"], float)
    assert result.data["volume"] == 1000.0
    
    # Test with string that can be converted to float
    test_record2 = DataRecord(
        id="test_2",
        data={
            "symbol": "TEST2",
            "price": "99.5",  # String that can be converted to float
            "volume": "2000"  # String that can be converted to float
        }
    )
    
    result2 = await validator.process(test_record2)
    
    assert result2 is not None
    assert isinstance(result2.data["price"], float)
    assert isinstance(result2.data["volume"], float)
    assert result2.data["price"] == 99.5
    assert result2.data["volume"] == 2000.0
    
    print("✅ Flexible validation verified")
    return True


async def test_performance_improvements():
    """Test that performance improvements work."""
    print("🧪 Testing performance improvements...")
    
    # Test multiple risk assessments to check accuracy
    test_cases = [
        # (features, expected_risk_level_range)
        (RiskFeatures(rug_pull_indicators=0.9, pump_dump_signals=0.8), [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]),
        (RiskFeatures(liquidity_score=0.95, market_cap=1000000000), [RiskLevel.VERY_LOW, RiskLevel.LOW]),
        (RiskFeatures(holder_concentration=0.95, unique_addresses=5), [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]),
        (RiskFeatures(social_sentiment=0.8, volume_24h=100000000), [RiskLevel.VERY_LOW, RiskLevel.LOW, RiskLevel.MEDIUM]),
    ]
    
    correct_predictions = 0
    total_predictions = len(test_cases)
    
    for i, (features, expected_range) in enumerate(test_cases):
        prediction = await risk_calibration_system.assess_risk(f"0xtest{i}", features)
        
        if prediction.risk_level in expected_range:
            correct_predictions += 1
    
    accuracy = correct_predictions / total_predictions
    
    # Should have improved accuracy
    assert accuracy >= 0.75, f"Accuracy {accuracy} should be >= 0.75"
    
    print(f"   Accuracy: {accuracy:.1%}")
    print("✅ Performance improvements verified")
    return True


async def main():
    """Run all production fix tests."""
    print("🚀 Testing Production Fixes")
    print("=" * 50)
    
    test_results = {}
    
    try:
        tests = [
            ("audit_trail_fix", test_audit_trail_fix),
            ("gdpr_compliance_fix", test_gdpr_compliance_fix),
            ("improved_risk_calibration", test_improved_risk_calibration),
            ("flexible_validation", test_flexible_validation),
            ("performance_improvements", test_performance_improvements),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                result = await test_func()
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 50)
        print("🎉 Production Fix Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate == 1.0:
            print("🏆 All production fixes verified - Ready for deployment!")
            return 0
        else:
            print("⚠️ Some fixes need attention")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
