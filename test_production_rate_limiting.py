#!/usr/bin/env python3
"""
Production Rate Limiting Test - Comprehensive Validation
Tests production-ready rate limiting across all API integrations
"""

import asyncio
import sys
import time
from datetime import datetime
sys.path.append('src')

async def test_production_rate_limiting():
    """Test production rate limiting system"""
    print("⚡ PRODUCTION RATE LIMITING TEST")
    print("=" * 60)
    
    try:
        from src.utils.rate_limit import (
            RateLimiter, RateLimitConfig, RateLimitStrategy, 
            APIRateLimiter, _global_rate_limiter, rate_limited
        )
        
        print("✅ Rate limiting components imported successfully")
        
        # Test scenarios for rate limiting
        test_scenarios = [
            {
                "name": "Token Bucket Rate Limiting",
                "description": "Test token bucket algorithm with burst capacity",
                "test_method": "token_bucket"
            },
            {
                "name": "Sliding Window Rate Limiting",
                "description": "Test sliding window algorithm",
                "test_method": "sliding_window"
            },
            {
                "name": "Global API Rate Limiter",
                "description": "Test global rate limiter for all APIs",
                "test_method": "global_rate_limiter"
            },
            {
                "name": "Rate Limited Decorator",
                "description": "Test rate limited decorator functionality",
                "test_method": "rate_limited_decorator"
            },
            {
                "name": "Concurrent Request Handling",
                "description": "Test rate limiting under concurrent load",
                "test_method": "concurrent_requests"
            },
            {
                "name": "Real API Integration",
                "description": "Test rate limiting with real API calls",
                "test_method": "real_api_integration"
            }
        ]
        
        print(f"\n🧪 Testing {len(test_scenarios)} rate limiting scenarios...")
        print("-" * 60)
        
        results = []
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n{i}. Testing {scenario['name']}")
            print(f"   Description: {scenario['description']}")
            
            start_time = datetime.now()
            
            try:
                if scenario['test_method'] == 'token_bucket':
                    # Test token bucket rate limiting
                    print("   🪣 Testing token bucket algorithm...")
                    
                    config = RateLimitConfig(
                        requests_per_second=2.0,
                        burst_size=5,
                        strategy=RateLimitStrategy.TOKEN_BUCKET
                    )
                    limiter = RateLimiter(config)
                    
                    # Test burst capacity
                    burst_start = time.time()
                    for i in range(5):  # Should all go through quickly
                        await limiter.acquire()
                    burst_time = time.time() - burst_start
                    
                    # Test rate limiting after burst
                    rate_start = time.time()
                    await limiter.acquire()  # This should be rate limited
                    rate_time = time.time() - rate_start
                    
                    print(f"   📊 Burst time: {burst_time:.3f}s (should be <0.1s)")
                    print(f"   📊 Rate limited time: {rate_time:.3f}s (should be ~0.5s)")
                    
                    assert burst_time < 0.1, "Burst should be fast"
                    assert 0.3 < rate_time < 0.7, "Rate limiting should delay ~0.5s"
                    
                    print("   ✅ Token bucket algorithm working correctly")
                    status = "PASS"
                
                elif scenario['test_method'] == 'sliding_window':
                    # Test sliding window rate limiting
                    print("   🪟 Testing sliding window algorithm...")
                    
                    config = RateLimitConfig(
                        requests_per_second=3.0,
                        burst_size=3,
                        strategy=RateLimitStrategy.SLIDING_WINDOW
                    )
                    limiter = RateLimiter(config)
                    
                    # Make requests within window
                    window_start = time.time()
                    for i in range(3):
                        await limiter.acquire()
                    
                    # Next request should be delayed
                    delay_start = time.time()
                    await limiter.acquire()
                    delay_time = time.time() - delay_start
                    
                    print(f"   📊 Sliding window delay: {delay_time:.3f}s")
                    
                    # Should have some delay due to sliding window
                    assert delay_time > 0.1, "Sliding window should impose delay"
                    
                    print("   ✅ Sliding window algorithm working correctly")
                    status = "PASS"
                
                elif scenario['test_method'] == 'global_rate_limiter':
                    # Test global API rate limiter
                    print("   🌐 Testing global API rate limiter...")
                    
                    # Test different API limiters
                    api_tests = [
                        ("honeypot_is", 1.0),
                        ("goplus", 2.0),
                        ("birdeye", 1.0),
                        ("dexscreener", 2.0),
                        ("etherscan", 5.0)
                    ]
                    
                    for api_name, expected_rps in api_tests:
                        # Test rate limiting for each API
                        test_start = time.time()
                        
                        # Make 3 requests
                        for i in range(3):
                            await _global_rate_limiter.acquire(api_name)
                        
                        test_time = time.time() - test_start
                        actual_rps = 3 / test_time if test_time > 0 else float('inf')
                        
                        print(f"      {api_name}: {actual_rps:.1f} RPS (limit: {expected_rps} RPS)")
                        
                        # Should respect rate limits (allow some tolerance)
                        assert actual_rps <= expected_rps * 1.5, f"{api_name} exceeded rate limit"
                    
                    print("   ✅ Global rate limiter working for all APIs")
                    status = "PASS"
                
                elif scenario['test_method'] == 'rate_limited_decorator':
                    # Test rate limited decorator
                    print("   🎯 Testing rate limited decorator...")
                    
                    call_times = []
                    
                    @rate_limited("test_api", max_retries=1)
                    async def test_function():
                        call_times.append(time.time())
                        return f"Call at {call_times[-1]}"
                    
                    # Make multiple calls
                    for i in range(3):
                        result = await test_function()
                        print(f"      Call {i+1}: {result}")
                    
                    # Check timing between calls
                    if len(call_times) >= 2:
                        time_diff = call_times[1] - call_times[0]
                        print(f"   📊 Time between calls: {time_diff:.3f}s")
                        
                        # Should have some delay due to rate limiting
                        assert time_diff > 0.1, "Decorator should impose rate limiting"
                    
                    print("   ✅ Rate limited decorator working correctly")
                    status = "PASS"
                
                elif scenario['test_method'] == 'concurrent_requests':
                    # Test concurrent request handling
                    print("   🔄 Testing concurrent request handling...")
                    
                    config = RateLimitConfig(
                        requests_per_second=5.0,
                        burst_size=10,
                        strategy=RateLimitStrategy.TOKEN_BUCKET
                    )
                    limiter = RateLimiter(config)
                    
                    async def make_request(request_id):
                        start = time.time()
                        await limiter.acquire()
                        end = time.time()
                        return {"id": request_id, "wait_time": end - start}
                    
                    # Launch 20 concurrent requests
                    concurrent_start = time.time()
                    tasks = [make_request(i) for i in range(20)]
                    results_list = await asyncio.gather(*tasks)
                    concurrent_time = time.time() - concurrent_start
                    
                    # Analyze results
                    wait_times = [r["wait_time"] for r in results_list]
                    avg_wait = sum(wait_times) / len(wait_times)
                    max_wait = max(wait_times)
                    
                    print(f"   📊 Concurrent requests: 20 in {concurrent_time:.3f}s")
                    print(f"   📊 Average wait time: {avg_wait:.3f}s")
                    print(f"   📊 Max wait time: {max_wait:.3f}s")
                    
                    # Should handle concurrent requests properly
                    assert concurrent_time > 2.0, "Should take time due to rate limiting"
                    assert max_wait < 10.0, "No request should wait too long"
                    
                    print("   ✅ Concurrent request handling working correctly")
                    status = "PASS"
                
                elif scenario['test_method'] == 'real_api_integration':
                    # Test with real API integration
                    print("   🌐 Testing rate limiting with real API integration...")
                    
                    from src.agents.scam_detector import AdvancedScamDetector
                    from src.core.cache import CacheManager
                    
                    cache_manager = CacheManager()
                    await cache_manager.initialize()
                    
                    scam_detector = AdvancedScamDetector(cache_manager)
                    await scam_detector.initialize()
                    
                    # Test multiple API calls with rate limiting
                    api_start = time.time()
                    
                    # Make 3 honeypot detection calls
                    test_addresses = [
                        "******************************************",  # USDC
                        "******************************************",  # WETH
                        "******************************************"   # PEPE
                    ]
                    
                    for addr in test_addresses:
                        try:
                            result = await scam_detector._detect_honeypot(addr, 1)
                            print(f"      API call for {addr[:10]}... completed")
                        except Exception as e:
                            print(f"      API call for {addr[:10]}... failed: {type(e).__name__}")
                    
                    api_time = time.time() - api_start
                    print(f"   📊 Real API calls: 3 calls in {api_time:.3f}s")
                    
                    # Should respect rate limits (honeypot_is is 1 RPS)
                    expected_min_time = 2.0  # At least 2 seconds for 3 calls at 1 RPS
                    assert api_time >= expected_min_time, f"Should take at least {expected_min_time}s due to rate limiting"
                    
                    await scam_detector.shutdown()
                    
                    print("   ✅ Real API integration rate limiting working")
                    status = "PASS"
                
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                print(f"      Execution time: {execution_time:.2f}s")
                print(f"   ✅ {status}: {scenario['name']} completed successfully")
                
                results.append({
                    "scenario": scenario['name'],
                    "status": status,
                    "execution_time": execution_time,
                    "description": scenario['description']
                })
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results.append({
                    "scenario": scenario['name'],
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # Summary Report
        print("\n" + "=" * 60)
        print("📊 PRODUCTION RATE LIMITING SUMMARY")
        print("-" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('status') == 'PASS')
        failed_tests = sum(1 for r in results if r.get('status') == 'FAIL')
        error_tests = sum(1 for r in results if r.get('status') == 'ERROR')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        
        # Performance Metrics
        successful_results = [r for r in results if 'execution_time' in r]
        if successful_results:
            avg_time = sum(r['execution_time'] for r in successful_results) / len(successful_results)
            max_time = max(r['execution_time'] for r in successful_results)
            min_time = min(r['execution_time'] for r in successful_results)
            
            print(f"\nPerformance Metrics:")
            print(f"Average Execution Time: {avg_time:.2f}s")
            print(f"Max Execution Time: {max_time:.2f}s")
            print(f"Min Execution Time: {min_time:.2f}s")
        
        # Final Assessment
        print("\n" + "=" * 60)
        print("🎯 FINAL ASSESSMENT:")
        print("-" * 60)
        
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        if success_rate >= 0.9 and error_tests == 0:
            print("✅ PRODUCTION RATE LIMITING: FULLY FUNCTIONAL")
            print("   - Token bucket and sliding window algorithms working")
            print("   - Global API rate limiter operational")
            print("   - Concurrent request handling robust")
            print("   - Real API integration rate limiting effective")
        elif success_rate >= 0.7:
            print("⚠️  PRODUCTION RATE LIMITING: MOSTLY FUNCTIONAL")
            print("   - Core rate limiting features working")
            print("   - Some edge cases may need refinement")
            print("   - Overall API protection improved")
        else:
            print("❌ PRODUCTION RATE LIMITING: NEEDS IMPROVEMENT")
            print("   - Significant issues with rate limiting")
            print("   - API protection may be insufficient")
            print("   - Further development required")
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_production_rate_limiting())
