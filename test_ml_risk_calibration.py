#!/usr/bin/env python3
"""
Tests for ML-based risk calibration system.
"""

import asyncio
import sys
import time
from pathlib import Path
from unittest.mock import MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()

from src.ml.risk_calibration import (
    RiskLevel,
    ModelType,
    RiskFeatures,
    RiskPrediction,
    SimpleLogisticRegression,
    EnsembleRiskModel,
    RiskCalibrationSystem,
    risk_calibration_system,
    get_risk_calibration_status
)


def test_risk_features():
    """Test risk features functionality."""
    print("🧪 Testing RiskFeatures...")
    
    # Create test features
    features = RiskFeatures(
        price_volatility=25.5,
        volume_24h=1000000.0,
        market_cap=50000000.0,
        liquidity_score=0.8,
        rsi=65.0,
        social_sentiment=0.3,
        holder_concentration=0.15,
        rug_pull_indicators=0.2,
        pump_dump_signals=0.1
    )
    
    # Test serialization
    feature_dict = features.to_dict()
    assert feature_dict["price_volatility"] == 25.5
    assert feature_dict["volume_24h"] == 1000000.0
    assert feature_dict["rsi"] == 65.0
    
    # Test normalization
    normalized = features.normalize()
    
    # Check that normalized values are in [0, 1] range
    normalized_dict = normalized.to_dict()
    for key, value in normalized_dict.items():
        assert 0.0 <= value <= 1.0, f"{key} normalization failed: {value}"
    
    # Check specific normalizations
    assert normalized.rsi == 0.65  # 65/100
    assert normalized.social_sentiment == 0.65  # (0.3 + 1) / 2
    
    print("✅ RiskFeatures tests passed")
    return True


def test_risk_prediction():
    """Test risk prediction functionality."""
    print("🧪 Testing RiskPrediction...")
    
    prediction = RiskPrediction(
        risk_level=RiskLevel.HIGH,
        confidence=0.85,
        probability_scores={
            "low": 0.1,
            "medium": 0.2,
            "high": 0.7
        },
        feature_importance={
            "rug_pull_indicators": 0.4,
            "price_volatility": 0.3,
            "liquidity_score": 0.3
        },
        model_used=ModelType.ENSEMBLE
    )
    
    # Test serialization
    pred_dict = prediction.to_dict()
    
    assert pred_dict["risk_level"] == "high"
    assert pred_dict["confidence"] == 0.85
    assert pred_dict["model_used"] == "ensemble"
    assert "prediction_time" in pred_dict
    
    print("✅ RiskPrediction tests passed")
    return True


def test_simple_logistic_regression():
    """Test simple logistic regression model."""
    print("🧪 Testing SimpleLogisticRegression...")
    
    feature_names = ["price_volatility", "rug_pull_indicators", "liquidity_score"]
    model = SimpleLogisticRegression(feature_names)
    
    # Test initialization
    assert model.is_trained == True
    assert len(model.weights) == len(feature_names)
    
    # Test prediction
    test_features = {
        "price_volatility": 0.8,
        "rug_pull_indicators": 0.9,
        "liquidity_score": 0.2
    }
    
    # Test probability prediction
    probability = model.predict_proba(test_features)
    assert 0.0 <= probability <= 1.0
    
    # Test risk level prediction
    risk_level, confidence = model.predict(test_features)
    assert isinstance(risk_level, RiskLevel)
    assert 0.0 <= confidence <= 1.0
    
    # High risk indicators should lead to high risk prediction
    high_risk_features = {
        "price_volatility": 1.0,
        "rug_pull_indicators": 1.0,
        "liquidity_score": 0.0
    }
    
    high_risk_prob = model.predict_proba(high_risk_features)
    low_risk_prob = model.predict_proba({
        "price_volatility": 0.1,
        "rug_pull_indicators": 0.0,
        "liquidity_score": 1.0
    })
    
    assert high_risk_prob > low_risk_prob, "High risk features should have higher probability"
    
    print("✅ SimpleLogisticRegression tests passed")
    return True


def test_ensemble_risk_model():
    """Test ensemble risk model."""
    print("🧪 Testing EnsembleRiskModel...")
    
    model = EnsembleRiskModel()
    
    # Test initialization
    assert model.is_trained == True
    assert len(model.feature_names) > 0
    assert ModelType.LOGISTIC_REGRESSION in model.models
    
    # Test prediction with low risk features
    low_risk_features = RiskFeatures(
        price_volatility=5.0,
        volume_24h=10000000.0,
        market_cap=100000000.0,
        liquidity_score=0.9,
        rsi=50.0,
        social_sentiment=0.5,
        holder_concentration=0.05,
        rug_pull_indicators=0.0,
        pump_dump_signals=0.0,
        wash_trading_score=0.0
    )
    
    low_risk_prediction = model.predict(low_risk_features)
    
    assert isinstance(low_risk_prediction, RiskPrediction)
    assert isinstance(low_risk_prediction.risk_level, RiskLevel)
    assert 0.0 <= low_risk_prediction.confidence <= 1.0
    assert len(low_risk_prediction.probability_scores) > 0
    assert len(low_risk_prediction.feature_importance) > 0
    
    # Test prediction with high risk features
    high_risk_features = RiskFeatures(
        price_volatility=80.0,
        volume_24h=1000.0,
        market_cap=10000.0,
        liquidity_score=0.1,
        rsi=90.0,
        social_sentiment=-0.8,
        holder_concentration=0.9,
        rug_pull_indicators=0.9,
        pump_dump_signals=0.8,
        wash_trading_score=0.7
    )
    
    high_risk_prediction = model.predict(high_risk_features)
    
    # High risk features should generally lead to higher risk classification
    risk_level_values = {
        RiskLevel.VERY_LOW: 1,
        RiskLevel.LOW: 2,
        RiskLevel.MEDIUM: 3,
        RiskLevel.HIGH: 4,
        RiskLevel.VERY_HIGH: 5,
        RiskLevel.CRITICAL: 6
    }
    
    low_risk_value = risk_level_values[low_risk_prediction.risk_level]
    high_risk_value = risk_level_values[high_risk_prediction.risk_level]
    
    # Allow some tolerance for model variability
    assert high_risk_value >= low_risk_value - 1, "High risk features should not be classified much lower than low risk features"
    
    print("✅ EnsembleRiskModel tests passed")
    return True


async def test_risk_calibration_system():
    """Test risk calibration system."""
    print("🧪 Testing RiskCalibrationSystem...")
    
    system = RiskCalibrationSystem()
    
    # Test initialization
    assert system.ensemble_model.is_trained == True
    
    # Test risk assessment
    test_features = RiskFeatures(
        price_volatility=30.0,
        volume_24h=5000000.0,
        market_cap=25000000.0,
        liquidity_score=0.6,
        rsi=70.0,
        social_sentiment=0.2,
        holder_concentration=0.3,
        rug_pull_indicators=0.4,
        pump_dump_signals=0.2
    )
    
    token_address = "0x1234567890abcdef"
    prediction = await system.assess_risk(token_address, test_features)
    
    assert isinstance(prediction, RiskPrediction)
    assert isinstance(prediction.risk_level, RiskLevel)
    assert 0.0 <= prediction.confidence <= 1.0
    
    # Test prediction history
    recent_predictions = system.get_recent_predictions(limit=10)
    assert len(recent_predictions) >= 1
    assert recent_predictions[-1]["token_address"] == token_address
    
    # Test model performance update
    system.update_model_performance(token_address, RiskLevel.HIGH)
    
    performance = system.get_model_performance()
    assert "overall_metrics" in performance
    assert "level_performance" in performance
    assert "total_predictions" in performance
    
    # Test system status
    status = system.get_system_status()
    assert "model_trained" in status
    assert "prediction_history_size" in status
    assert status["model_trained"] == True
    
    print("✅ RiskCalibrationSystem tests passed")
    return True


async def test_risk_assessment_scenarios():
    """Test various risk assessment scenarios."""
    print("🧪 Testing risk assessment scenarios...")
    
    system = RiskCalibrationSystem()
    
    # Scenario 1: Legitimate established token
    legitimate_features = RiskFeatures(
        price_volatility=15.0,
        volume_24h=50000000.0,
        market_cap=1000000000.0,
        liquidity_score=0.95,
        rsi=45.0,
        social_sentiment=0.6,
        holder_concentration=0.02,
        unique_addresses=50000,
        rug_pull_indicators=0.0,
        pump_dump_signals=0.0,
        wash_trading_score=0.0
    )
    
    legitimate_prediction = await system.assess_risk("0xlegitimate", legitimate_features)
    
    # Scenario 2: Suspicious new token
    suspicious_features = RiskFeatures(
        price_volatility=150.0,
        volume_24h=100000.0,
        market_cap=1000000.0,
        liquidity_score=0.1,
        rsi=95.0,
        social_sentiment=-0.5,
        holder_concentration=0.8,
        unique_addresses=50,
        rug_pull_indicators=0.9,
        pump_dump_signals=0.8,
        wash_trading_score=0.7
    )
    
    suspicious_prediction = await system.assess_risk("0xsuspicious", suspicious_features)
    
    # Scenario 3: Medium risk token
    medium_features = RiskFeatures(
        price_volatility=40.0,
        volume_24h=2000000.0,
        market_cap=10000000.0,
        liquidity_score=0.5,
        rsi=60.0,
        social_sentiment=0.1,
        holder_concentration=0.3,
        unique_addresses=1000,
        rug_pull_indicators=0.3,
        pump_dump_signals=0.2,
        wash_trading_score=0.1
    )
    
    medium_prediction = await system.assess_risk("0xmedium", medium_features)
    
    # Validate that risk levels make sense relative to each other
    risk_values = {
        RiskLevel.VERY_LOW: 1, RiskLevel.LOW: 2, RiskLevel.MEDIUM: 3,
        RiskLevel.HIGH: 4, RiskLevel.VERY_HIGH: 5, RiskLevel.CRITICAL: 6
    }
    
    legitimate_value = risk_values[legitimate_prediction.risk_level]
    suspicious_value = risk_values[suspicious_prediction.risk_level]
    medium_value = risk_values[medium_prediction.risk_level]
    
    # Suspicious should be higher risk than legitimate
    assert suspicious_value > legitimate_value, f"Suspicious ({suspicious_prediction.risk_level}) should be higher risk than legitimate ({legitimate_prediction.risk_level})"
    
    # Medium should be between legitimate and suspicious (with some tolerance)
    assert legitimate_value <= medium_value + 1, "Medium risk should not be much lower than legitimate"
    assert medium_value <= suspicious_value + 1, "Medium risk should not be much higher than suspicious"
    
    print("✅ Risk assessment scenarios tests passed")
    return True


def test_feature_importance():
    """Test feature importance calculation."""
    print("🧪 Testing feature importance...")
    
    model = EnsembleRiskModel()
    
    # Create features with strong risk indicators
    features = RiskFeatures(
        rug_pull_indicators=0.9,  # Should be highly important
        pump_dump_signals=0.8,   # Should be highly important
        price_volatility=0.1,    # Should be less important
        liquidity_score=0.9      # Should be important (negative weight)
    )
    
    prediction = model.predict(features)
    importance = prediction.feature_importance
    
    # Check that importance scores sum to approximately 1
    total_importance = sum(importance.values())
    assert 0.8 <= total_importance <= 1.2, f"Feature importance should sum to ~1, got {total_importance}"
    
    # Check that high-risk indicators have higher importance
    if "rug_pull_indicators" in importance and "price_volatility" in importance:
        assert importance["rug_pull_indicators"] > importance["price_volatility"], \
            "Rug pull indicators should have higher importance than price volatility"
    
    print("✅ Feature importance tests passed")
    return True


def test_global_risk_system():
    """Test global risk calibration system."""
    print("🧪 Testing global risk system...")
    
    # Test global system status
    status = get_risk_calibration_status()
    
    assert "system" in status
    assert "status" in status
    assert "performance" in status
    assert "timestamp" in status
    
    assert status["system"] == "initialized"
    
    # Test that global system is the same instance
    global_system = risk_calibration_system
    assert global_system.ensemble_model.is_trained == True
    
    print("✅ Global risk system tests passed")
    return True


async def test_performance_tracking():
    """Test performance tracking and calibration."""
    print("🧪 Testing performance tracking...")
    
    system = RiskCalibrationSystem()
    
    # Make several predictions
    test_cases = [
        ("0xtoken1", RiskFeatures(rug_pull_indicators=0.1), RiskLevel.LOW),
        ("0xtoken2", RiskFeatures(rug_pull_indicators=0.9), RiskLevel.HIGH),
        ("0xtoken3", RiskFeatures(rug_pull_indicators=0.5), RiskLevel.MEDIUM),
    ]
    
    for token_address, features, actual_outcome in test_cases:
        # Make prediction
        prediction = await system.assess_risk(token_address, features)
        
        # Update with actual outcome
        system.update_model_performance(token_address, actual_outcome)
    
    # Check performance metrics
    performance = system.get_model_performance()
    
    assert "overall_metrics" in performance
    assert "level_performance" in performance
    assert performance["total_predictions"] >= len(test_cases)
    
    # Check that accuracy is calculated
    if performance["overall_metrics"]["accuracy"] > 0:
        assert 0.0 <= performance["overall_metrics"]["accuracy"] <= 1.0
    
    print("✅ Performance tracking tests passed")
    return True


async def main():
    """Run all ML risk calibration tests."""
    print("🚀 Starting ML Risk Calibration Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Run all tests
        tests = [
            ("risk_features", test_risk_features),
            ("risk_prediction", test_risk_prediction),
            ("simple_logistic_regression", test_simple_logistic_regression),
            ("ensemble_risk_model", test_ensemble_risk_model),
            ("risk_calibration_system", test_risk_calibration_system),
            ("risk_assessment_scenarios", test_risk_assessment_scenarios),
            ("feature_importance", test_feature_importance),
            ("global_risk_system", test_global_risk_system),
            ("performance_tracking", test_performance_tracking),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 ML Risk Calibration Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate >= 0.9:
            print("🏆 ML risk calibration tests meet high standards - System ready!")
            return 0
        else:
            print("⚠️ Some ML tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
