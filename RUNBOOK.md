# Token Analyzer - Production Runbook

**Version:** 1.0  
**Last Updated:** July 9, 2025  
**Target Platform:** Mac M2 Max (Apple Silicon)  
**Python Version:** 3.12+  
**Node.js Version:** 20+

## Table of Contents

1. [System Overview](#system-overview)
2. [Prerequisites](#prerequisites)
3. [Local Development Setup](#local-development-setup)
4. [Environment Configuration](#environment-configuration)
5. [Running the System](#running-the-system)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Monitoring & Maintenance](#monitoring--maintenance)
9. [Troubleshooting](#troubleshooting)
10. [API Rate Limits](#api-rate-limits)

## System Overview

The Token Analyzer is a production-grade multi-agent system for cryptocurrency token analysis using:

- **Backend**: Python 3.12 with AutoGen multi-agent orchestration
- **Frontend**: Next.js with TradingView Lightweight Charts
- **Database**: DuckDB for analytics, Redis for caching
- **APIs**: Free-tier only (DeFiLlama, Etherscan, CoinGecko, etc.)
- **Architecture**: Microservices with rate limiting and circuit breakers

### Agent Pipeline Flow
```
Scheduler → Discovery → ChainInfo → MarketData → Trend → Technical → Validator → Analyst → Audit
```

## Prerequisites

### System Requirements (Mac M2 Max)
- **OS**: macOS 13.0+ (Ventura) or later
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 10GB free space
- **Network**: Stable internet connection for API calls

### Required Software
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Python 3.12
brew install python@3.12

# Install Node.js 20
brew install node@20

# Install Redis
brew install redis

# Install Git (if not installed)
brew install git
```

### Development Tools
```bash
# Install development tools
brew install --cask visual-studio-code
brew install --cask docker
brew install postgresql  # Optional: for advanced database features
```

## Local Development Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd ar
```

### 2. Python Environment Setup
```bash
# Create virtual environment
python3.12 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -e ".[dev,test]"
```

### 3. Frontend Setup
```bash
cd ui
npm install
cd ..
```

### 4. Database Setup
```bash
# Start Redis
brew services start redis

# Verify Redis is running
redis-cli ping  # Should return "PONG"

# DuckDB will be created automatically on first run
```

## Environment Configuration

### 1. Create Environment File
```bash
cp .env.example .env
```

### 2. Configure API Keys

Edit `.env` with your API keys:

```bash
# Required API Keys (all free tier)
ETHERSCAN_API_KEY=your_etherscan_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional API Keys (free tier)
COINGECKO_API_KEY=your_coingecko_api_key_here

# Database Configuration
DATABASE_URL=duckdb:///data/token_analyzer.db
REDIS_URL=redis://localhost:6379/0

# System Configuration
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=true

# Rate Limiting (adjust based on your API tier)
ETHERSCAN_RATE_LIMIT=5  # requests per second (free tier)
COINGECKO_RATE_LIMIT=10  # requests per minute (free tier)
```

### 3. API Key Setup Instructions

#### Etherscan API Key (Required)
1. Visit [https://etherscan.io/apis](https://etherscan.io/apis)
2. Create free account
3. Generate API key
4. Free tier: 5 requests/second, 100,000 requests/day

#### OpenRouter API Key (Required)
1. Visit [https://openrouter.ai/](https://openrouter.ai/)
2. Create account
3. Generate API key
4. Model: `deepseek/deepseek-r1-0528-qwen3-8b:free`

#### CoinGecko API Key (Optional)
1. Visit [https://www.coingecko.com/en/api](https://www.coingecko.com/en/api)
2. Create free account
3. Generate API key
4. Free tier: 10-50 calls/minute

## Running the System

### 1. Start Backend Services
```bash
# Activate virtual environment
source venv/bin/activate

# Start Redis (if not running)
brew services start redis

# Run the main application
python -m src.main

# Or run with specific configuration
ENVIRONMENT=development python -m src.main
```

### 2. Start Frontend (Separate Terminal)
```bash
cd ui
npm run dev
```

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **API**: http://localhost:8000
- **Health Check**: http://localhost:8000/health

### 4. Example Usage
```bash
# Analyze a token via CLI
python -m src.cli analyze 0x1234567890123456789012345678901234567890

# Or via API
curl -X POST http://localhost:8000/analyze \
  -H "Content-Type: application/json" \
  -d '{"token_address": "0x1234567890123456789012345678901234567890", "chain_id": 1}'
```

## Testing

### 1. Run All Tests
```bash
# Activate virtual environment
source venv/bin/activate

# Run complete test suite
pytest -v

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m "not external_api"  # Skip external API tests
```

### 2. Run Linting and Formatting
```bash
# Check code quality
ruff check src/ tests/
ruff format src/ tests/

# Type checking
mypy src/ --strict

# Security scan
bandit -r src/
```

### 3. Data Quality Validation
```bash
# Run Great Expectations validation
pytest tests/validation/ -v
```

### 4. Performance Testing
```bash
# Run benchmark tests
pytest --benchmark-only
```

## Deployment

### 1. Production Environment Setup
```bash
# Create production environment file
cp .env.example .env.production

# Edit with production values
vim .env.production
```

### 2. Build for Production
```bash
# Build Python package
python -m build

# Build frontend
cd ui
npm run build
npm run export
cd ..
```

### 3. Docker Deployment (Optional)
```bash
# Build Docker image
docker build -t token-analyzer .

# Run with Docker Compose
docker-compose up -d
```

### 4. Systemd Service (Linux)
```bash
# Copy service file
sudo cp deployment/token-analyzer.service /etc/systemd/system/

# Enable and start service
sudo systemctl enable token-analyzer
sudo systemctl start token-analyzer
```

## Monitoring & Maintenance

### 1. Health Checks
```bash
# Check system health
curl http://localhost:8000/health

# Check individual components
curl http://localhost:8000/health/database
curl http://localhost:8000/health/redis
curl http://localhost:8000/health/apis
```

### 2. Log Monitoring
```bash
# View application logs
tail -f logs/token_analyzer.log

# View error logs
tail -f logs/error.log

# View access logs
tail -f logs/access.log
```

### 3. Performance Monitoring
```bash
# Check system metrics
curl http://localhost:8000/metrics

# Monitor Redis
redis-cli info

# Monitor database
python -c "from src.core.database import DatabaseManager; print(DatabaseManager().get_stats())"
```

### 4. Cron Job Setup
```bash
# Edit crontab
crontab -e

# Add scheduled analysis (every hour)
0 * * * * cd /path/to/token-analyzer && source venv/bin/activate && python -m src.scheduler

# Add daily cleanup (2 AM)
0 2 * * * cd /path/to/token-analyzer && source venv/bin/activate && python -m src.maintenance.cleanup
```

## Troubleshooting

### Common Issues

#### 1. API Rate Limiting
**Symptoms**: HTTP 429 errors, slow responses
**Solution**:
```bash
# Check rate limit configuration
grep RATE_LIMIT .env

# Adjust rate limits in .env
ETHERSCAN_RATE_LIMIT=3  # Reduce from 5
COINGECKO_RATE_LIMIT=5  # Reduce from 10
```

#### 2. Database Connection Issues
**Symptoms**: Database connection errors
**Solution**:
```bash
# Check DuckDB file permissions
ls -la data/token_analyzer.db

# Recreate database
rm data/token_analyzer.db
python -c "from src.core.database import DatabaseManager; DatabaseManager().initialize()"
```

#### 3. Redis Connection Issues
**Symptoms**: Cache errors, Redis connection failures
**Solution**:
```bash
# Check Redis status
brew services list | grep redis

# Restart Redis
brew services restart redis

# Test connection
redis-cli ping
```

#### 4. Memory Issues (Mac M2 Max)
**Symptoms**: Out of memory errors, slow performance
**Solution**:
```bash
# Check memory usage
top -o MEM

# Adjust worker processes in .env
MAX_WORKERS=4  # Reduce from default

# Enable memory optimization
OPTIMIZE_MEMORY=true
```

#### 5. AutoGen Agent Issues
**Symptoms**: Agent communication failures
**Solution**:
```bash
# Check OpenRouter API key
curl -H "Authorization: Bearer $OPENROUTER_API_KEY" https://openrouter.ai/api/v1/models

# Reset agent state
python -c "from src.agents.coordinator import AutoGenCoordinator; coordinator = AutoGenCoordinator(); coordinator.reset()"
```

## API Rate Limits

### Free Tier Limits (as of July 2025)

| API | Rate Limit | Daily Limit | Notes |
|-----|------------|-------------|-------|
| Etherscan | 5 req/sec | 100,000 | Required for blockchain data |
| CoinGecko | 10-50 req/min | Varies | Price data |
| DeFiLlama | No official limit | - | Protocol data |
| DexScreener | No official limit | - | DEX data |
| Fear & Greed | No official limit | - | Sentiment data |
| OpenRouter | Model-specific | Varies | LLM inference |

### Rate Limit Best Practices

1. **Implement exponential backoff** (already included)
2. **Cache responses** when possible (Redis caching enabled)
3. **Batch requests** where supported
4. **Monitor usage** via logs and metrics
5. **Upgrade to paid tiers** if needed for production

### Monitoring Rate Limits
```bash
# Check current rate limit status
curl http://localhost:8000/api/rate-limits

# View rate limit logs
grep "rate.limit" logs/token_analyzer.log
```

---

## Support & Maintenance

For issues and support:
1. Check logs first: `tail -f logs/token_analyzer.log`
2. Review this runbook
3. Check GitHub issues
4. Contact development team

**Remember**: This system uses only free-tier APIs and open-source tools to stay within the $100 budget constraint.
