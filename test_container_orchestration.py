#!/usr/bin/env python3
"""
Tests for container orchestration and deployment configurations.
"""

import asyncio
import json
import sys
import yaml
from pathlib import Path
from unittest.mock import MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()

from src.deployment.container_orchestration import (
    DeploymentEnvironment,
    ServiceType,
    HealthCheck,
    ResourceRequirements,
    ServiceConfiguration,
    KubernetesManifestGenerator,
    DockerfileGenerator,
    HealthCheckEndpoint,
    get_orchestration_status
)


def test_service_configuration():
    """Test service configuration creation."""
    print("🧪 Testing ServiceConfiguration...")
    
    # Create health check
    health_check = HealthCheck(
        path="/health",
        port=8000,
        interval_seconds=30,
        timeout_seconds=5
    )
    
    # Create resource requirements
    resources = ResourceRequirements(
        cpu_request="200m",
        cpu_limit="1000m",
        memory_request="256Mi",
        memory_limit="1Gi"
    )
    
    # Create service configuration
    service_config = ServiceConfiguration(
        name="api",
        service_type=ServiceType.API,
        image="crypto-analytics/api:latest",
        port=8000,
        environment=DeploymentEnvironment.PRODUCTION,
        replicas=3,
        health_check=health_check,
        resources=resources,
        environment_variables={
            "LOG_LEVEL": "INFO",
            "ENVIRONMENT": "production"
        },
        labels={
            "version": "1.0.0",
            "team": "platform"
        }
    )
    
    # Test serialization
    config_dict = service_config.to_dict()
    
    assert config_dict["name"] == "api"
    assert config_dict["service_type"] == "api"
    assert config_dict["replicas"] == 3
    assert config_dict["health_check"]["path"] == "/health"
    assert config_dict["resources"]["requests"]["cpu"] == "200m"
    assert config_dict["environment_variables"]["LOG_LEVEL"] == "INFO"
    assert config_dict["labels"]["version"] == "1.0.0"
    
    print("✅ ServiceConfiguration tests passed")
    return True


def test_kubernetes_manifest_generation():
    """Test Kubernetes manifest generation."""
    print("🧪 Testing KubernetesManifestGenerator...")
    
    generator = KubernetesManifestGenerator()
    
    # Create test service configuration
    service_config = ServiceConfiguration(
        name="test-api",
        service_type=ServiceType.API,
        image="test/api:v1.0.0",
        port=8080,
        environment=DeploymentEnvironment.PRODUCTION,
        replicas=2,
        health_check=HealthCheck("/health", 8080),
        resources=ResourceRequirements(),
        environment_variables={"ENV": "test"},
        volumes=[{
            "name": "logs",
            "mountPath": "/app/logs"
        }]
    )
    
    # Test deployment generation
    deployment = generator.generate_deployment(service_config)
    
    assert deployment["kind"] == "Deployment"
    assert deployment["metadata"]["name"] == "test-api-deployment"
    assert deployment["metadata"]["namespace"] == "crypto-analytics"
    assert deployment["spec"]["replicas"] == 2
    
    # Check container configuration
    container = deployment["spec"]["template"]["spec"]["containers"][0]
    assert container["name"] == "test-api"
    assert container["image"] == "test/api:v1.0.0"
    assert container["ports"][0]["containerPort"] == 8080
    
    # Check health checks
    assert "livenessProbe" in container
    assert "readinessProbe" in container
    assert container["livenessProbe"]["httpGet"]["path"] == "/health"
    
    # Check resources
    assert "resources" in container
    assert container["resources"]["requests"]["cpu"] == "100m"
    
    # Check environment variables
    env_vars = {env["name"]: env["value"] for env in container["env"]}
    assert env_vars["ENV"] == "test"
    
    # Test service generation
    service = generator.generate_service(service_config)
    
    assert service["kind"] == "Service"
    assert service["metadata"]["name"] == "test-api-service"
    assert service["spec"]["ports"][0]["port"] == 8080
    assert service["spec"]["selector"]["service"] == "test-api"
    
    # Test ConfigMap generation
    configmap = generator.generate_configmap("app-config", {
        "database.host": "postgres",
        "redis.host": "redis"
    })
    
    assert configmap["kind"] == "ConfigMap"
    assert configmap["metadata"]["name"] == "app-config-config"
    assert configmap["data"]["database.host"] == "postgres"
    
    # Test Secret generation
    secret = generator.generate_secret("app-secrets", {
        "database.password": "secret123",
        "api.key": "key456"
    })
    
    assert secret["kind"] == "Secret"
    assert secret["type"] == "Opaque"
    assert "data" in secret
    
    # Test Ingress generation
    ingress = generator.generate_ingress([service_config])
    
    assert ingress["kind"] == "Ingress"
    assert ingress["spec"]["rules"][0]["host"] == "crypto-analytics.local"
    assert len(ingress["spec"]["rules"][0]["http"]["paths"]) == 1
    
    print("✅ KubernetesManifestGenerator tests passed")
    return True


def test_dockerfile_generation():
    """Test Dockerfile generation."""
    print("🧪 Testing DockerfileGenerator...")
    
    generator = DockerfileGenerator()
    
    # Test Python API Dockerfile
    api_dockerfile = generator.generate_python_api_dockerfile("api")
    
    assert "FROM python:3.12-slim as builder" in api_dockerfile
    assert "COPY requirements.txt" in api_dockerfile
    assert "RUN groupadd -r appuser" in api_dockerfile
    assert "USER appuser" in api_dockerfile
    assert "HEALTHCHECK" in api_dockerfile
    assert "EXPOSE 8000" in api_dockerfile
    assert "uvicorn" in api_dockerfile
    
    # Test Worker Dockerfile
    worker_dockerfile = generator.generate_worker_dockerfile("worker")
    
    assert "FROM python:3.12-slim as builder" in worker_dockerfile
    assert "RUN groupadd -r worker" in worker_dockerfile
    assert "USER worker" in worker_dockerfile
    assert "src.workers.main" in worker_dockerfile
    
    # Test Docker Compose generation
    service_configs = [
        ServiceConfiguration(
            name="api",
            service_type=ServiceType.API,
            image="crypto-analytics/api:latest",
            port=8000,
            environment=DeploymentEnvironment.DEVELOPMENT,
            health_check=HealthCheck("/health", 8000),
            environment_variables={"LOG_LEVEL": "DEBUG"}
        ),
        ServiceConfiguration(
            name="worker",
            service_type=ServiceType.WORKER,
            image="crypto-analytics/worker:latest",
            port=8001,
            environment=DeploymentEnvironment.DEVELOPMENT
        )
    ]
    
    compose_yaml = generator.generate_docker_compose(service_configs)
    
    assert "version: \"3.8\"" in compose_yaml
    assert "services:" in compose_yaml
    assert "networks:" in compose_yaml
    assert "volumes:" in compose_yaml
    assert "postgres:" in compose_yaml
    assert "redis:" in compose_yaml
    
    print("✅ DockerfileGenerator tests passed")
    return True


async def test_health_check_endpoint():
    """Test health check endpoint functionality."""
    print("🧪 Testing HealthCheckEndpoint...")
    
    health_endpoint = HealthCheckEndpoint()
    
    # Test initial health check
    health_result = await health_endpoint.health_check()
    
    assert "status" in health_result
    assert "timestamp" in health_result
    assert "uptime_seconds" in health_result
    assert "ready" in health_result
    assert "checks" in health_result
    
    # Should not be ready initially
    assert health_result["ready"] == False
    
    # Test readiness check
    readiness_result = await health_endpoint.readiness_check()
    
    assert "status" in readiness_result
    assert "ready" in readiness_result
    assert readiness_result["ready"] == False
    
    # Set ready and test again
    health_endpoint.set_ready(True)
    
    readiness_result = await health_endpoint.readiness_check()
    assert readiness_result["ready"] == True
    
    print("✅ HealthCheckEndpoint tests passed")
    return True


def test_kubernetes_manifests_validity():
    """Test that generated Kubernetes manifests are valid YAML."""
    print("🧪 Testing Kubernetes manifest validity...")
    
    # Test namespace manifest
    namespace_file = Path("k8s/namespace.yaml")
    if namespace_file.exists():
        with open(namespace_file) as f:
            namespace_docs = list(yaml.safe_load_all(f))
        
        assert len(namespace_docs) == 3  # Namespace, ResourceQuota, LimitRange
        assert namespace_docs[0]["kind"] == "Namespace"
        assert namespace_docs[1]["kind"] == "ResourceQuota"
        assert namespace_docs[2]["kind"] == "LimitRange"
    
    # Test API deployment manifest
    api_deployment_file = Path("k8s/api-deployment.yaml")
    if api_deployment_file.exists():
        with open(api_deployment_file) as f:
            api_docs = list(yaml.safe_load_all(f))
        
        # Should have Deployment, Service, and HPA
        kinds = [doc["kind"] for doc in api_docs]
        assert "Deployment" in kinds
        assert "Service" in kinds
        assert "HorizontalPodAutoscaler" in kinds
        
        # Check deployment structure
        deployment = next(doc for doc in api_docs if doc["kind"] == "Deployment")
        assert deployment["spec"]["replicas"] == 3
        assert "livenessProbe" in deployment["spec"]["template"]["spec"]["containers"][0]
        assert "readinessProbe" in deployment["spec"]["template"]["spec"]["containers"][0]
    
    print("✅ Kubernetes manifest validity tests passed")
    return True


def test_docker_compose_validity():
    """Test that Docker Compose file is valid."""
    print("🧪 Testing Docker Compose validity...")
    
    compose_file = Path("docker-compose.yml")
    if compose_file.exists():
        with open(compose_file) as f:
            compose_data = yaml.safe_load(f)
        
        assert compose_data["version"] == "3.8"
        assert "services" in compose_data
        assert "networks" in compose_data
        assert "volumes" in compose_data
        
        # Check required services
        services = compose_data["services"]
        required_services = ["api", "postgres", "redis"]
        
        for service in required_services:
            assert service in services
        
        # Check API service configuration
        api_service = services["api"]
        assert "build" in api_service
        assert "ports" in api_service
        assert "environment" in api_service
        assert "healthcheck" in api_service
        
        # Check database service
        postgres_service = services["postgres"]
        assert postgres_service["image"] == "postgres:15-alpine"
        assert "healthcheck" in postgres_service
        
        # Check networks
        assert "crypto-analytics" in compose_data["networks"]
    
    print("✅ Docker Compose validity tests passed")
    return True


def test_orchestration_status():
    """Test orchestration status reporting."""
    print("🧪 Testing orchestration status...")
    
    status = get_orchestration_status()
    
    assert "manifest_generator" in status
    assert "dockerfile_generator" in status
    assert "health_endpoint" in status
    assert "namespace" in status
    assert "app_name" in status
    assert "timestamp" in status
    
    assert status["namespace"] == "crypto-analytics"
    assert status["app_name"] == "token-analysis-platform"
    
    print("✅ Orchestration status tests passed")
    return True


async def main():
    """Run all container orchestration tests."""
    print("🚀 Starting Container Orchestration Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Run all tests
        tests = [
            ("service_configuration", test_service_configuration),
            ("kubernetes_manifest_generation", test_kubernetes_manifest_generation),
            ("dockerfile_generation", test_dockerfile_generation),
            ("health_check_endpoint", test_health_check_endpoint),
            ("kubernetes_manifests_validity", test_kubernetes_manifests_validity),
            ("docker_compose_validity", test_docker_compose_validity),
            ("orchestration_status", test_orchestration_status),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed"
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 Container Orchestration Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate == 1.0:
            print("🏆 All orchestration tests passed - Container deployment ready!")
            return 0
        else:
            print("⚠️ Some orchestration tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
