#!/usr/bin/env python3
"""
Comprehensive tests for the advanced multi-layer caching system.
"""

import asyncio
import sys
import time
from pathlib import Path
from unittest.mock import MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()

from src.caching.advanced_cache import (
    CacheLayer,
    CacheStrategy,
    L1MemoryCache,
    L2RedisCache,
    L3DatabaseCache,
    MultiLayerCache,
    CacheWarmer,
    get_cache_status
)


async def test_l1_memory_cache():
    """Test L1 memory cache functionality."""
    print("🧪 Testing L1MemoryCache...")
    
    cache = L1MemoryCache(max_size=5, max_memory_mb=1, strategy=CacheStrategy.LRU)
    
    # Test basic set/get
    await cache.set("key1", "value1", ttl=60)
    value = await cache.get("key1")
    assert value == "value1"
    
    # Test cache miss
    value = await cache.get("nonexistent")
    assert value is None
    
    # Test TTL expiration
    await cache.set("ttl_key", "ttl_value", ttl=1)
    await asyncio.sleep(1.1)
    value = await cache.get("ttl_key")
    assert value is None
    
    # Test LRU eviction
    for i in range(6):  # Exceed max_size of 5
        await cache.set(f"key{i}", f"value{i}")
    
    # First key should be evicted
    value = await cache.get("key0")
    assert value is None
    
    # Last key should still exist
    value = await cache.get("key5")
    assert value == "value5"
    
    # Test tag-based invalidation
    await cache.set("tagged1", "value1", tags={"tag1", "tag2"})
    await cache.set("tagged2", "value2", tags={"tag2", "tag3"})
    await cache.set("untagged", "value3")
    
    invalidated = await cache.invalidate_by_tags({"tag2"})
    assert invalidated == 2
    
    assert await cache.get("tagged1") is None
    assert await cache.get("tagged2") is None
    assert await cache.get("untagged") == "value3"
    
    # Test statistics
    stats = cache.get_stats()
    assert stats.hits > 0
    assert stats.misses > 0
    assert stats.evictions > 0
    
    print("✅ L1MemoryCache tests passed")
    return True


async def test_l2_redis_cache():
    """Test L2 Redis cache functionality."""
    print("🧪 Testing L2RedisCache...")
    
    cache = L2RedisCache()
    
    # Test basic operations
    await cache.set("redis_key", {"data": "complex_value"}, ttl=60)
    value = await cache.get("redis_key")
    assert value["data"] == "complex_value"
    
    # Test TTL
    await cache.set("ttl_key", "ttl_value", ttl=1)
    await asyncio.sleep(1.1)
    value = await cache.get("ttl_key")
    assert value is None
    
    # Test tag invalidation
    await cache.set("tagged1", "value1", tags={"redis_tag"})
    await cache.set("tagged2", "value2", tags={"redis_tag"})
    
    invalidated = await cache.invalidate_by_tags({"redis_tag"})
    assert invalidated == 2
    
    # Test clear
    await cache.set("clear_test", "value")
    await cache.clear()
    value = await cache.get("clear_test")
    assert value is None
    
    print("✅ L2RedisCache tests passed")
    return True


async def test_l3_database_cache():
    """Test L3 database cache functionality."""
    print("🧪 Testing L3DatabaseCache...")
    
    cache = L3DatabaseCache()
    
    # Test basic operations with simulated latency
    start_time = time.time()
    await cache.set("db_key", {"persistent": "data"}, ttl=60)
    set_duration = time.time() - start_time
    assert set_duration > 0.001  # Should have simulated latency
    
    start_time = time.time()
    value = await cache.get("db_key")
    get_duration = time.time() - start_time
    assert get_duration > 0.0005  # Should have simulated latency
    assert value["persistent"] == "data"
    
    # Test delete
    deleted = await cache.delete("db_key")
    assert deleted == True
    
    value = await cache.get("db_key")
    assert value is None
    
    print("✅ L3DatabaseCache tests passed")
    return True


async def test_multi_layer_cache():
    """Test multi-layer cache functionality."""
    print("🧪 Testing MultiLayerCache...")
    
    l1 = L1MemoryCache(max_size=100)
    l2 = L2RedisCache()
    l3 = L3DatabaseCache()
    
    multi_cache = MultiLayerCache(l1, l2, l3)
    
    # Test cache miss and warming
    value = await multi_cache.get("missing_key")
    assert value is None
    
    # Test setting across all layers
    await multi_cache.set("multi_key", {"data": "multi_value"})
    
    # Should hit L1 (fastest)
    start_time = time.time()
    value = await multi_cache.get("multi_key")
    l1_duration = time.time() - start_time
    assert value["data"] == "multi_value"
    
    # Clear L1, should hit L2
    await l1.clear()
    start_time = time.time()
    value = await multi_cache.get("multi_key", warm_lower_layers=True)
    l2_duration = time.time() - start_time
    assert value["data"] == "multi_value"
    assert l2_duration > l1_duration  # L2 should be slower than L1
    
    # Verify L1 was warmed
    value = await l1.get("multi_key")
    assert value["data"] == "multi_value"
    
    # Test layer-specific operations
    await multi_cache.set("l1_only", "value", layers=[CacheLayer.L1_MEMORY])
    
    assert await l1.get("l1_only") == "value"
    assert await l2.get("l1_only") is None
    assert await l3.get("l1_only") is None
    
    # Test tag invalidation across layers
    await multi_cache.set("tagged_multi", "value", tags={"multi_tag"})
    results = await multi_cache.invalidate_by_tags({"multi_tag"})
    
    assert sum(results.values()) >= 1
    
    # Test cache warming
    def value_generator():
        return {"generated": "value", "timestamp": time.time()}
    
    success = await multi_cache.warm_cache("warm_key", value_generator, ttl=300)
    assert success == True
    
    value = await multi_cache.get("warm_key")
    assert value["generated"] == "value"
    
    # Test batch warming
    warm_requests = [
        {
            "key": f"batch_key_{i}",
            "value_generator": lambda i=i: f"batch_value_{i}",
            "ttl": 300
        }
        for i in range(3)
    ]
    
    results = await multi_cache.batch_warm(warm_requests)
    assert len(results) == 3
    assert all(results.values())
    
    # Verify batch warmed values
    for i in range(3):
        value = await multi_cache.get(f"batch_key_{i}")
        assert value == f"batch_value_{i}"
    
    # Test comprehensive stats
    stats = multi_cache.get_comprehensive_stats()
    assert "overall" in stats
    assert "l1_memory" in stats
    assert "l2_redis" in stats
    assert "l3_database" in stats
    
    assert stats["overall"]["total_requests"] > 0
    assert stats["l1_memory"]["hits"] > 0
    
    print("✅ MultiLayerCache tests passed")
    return True


async def test_cache_warmer():
    """Test cache warming functionality."""
    print("🧪 Testing CacheWarmer...")
    
    l1 = L1MemoryCache(max_size=100)
    l2 = L2RedisCache()
    l3 = L3DatabaseCache()
    multi_cache = MultiLayerCache(l1, l2, l3)
    
    warmer = CacheWarmer(multi_cache)
    
    # Register warming pattern
    def key_generator():
        return [f"warm_pattern_key_{i}" for i in range(5)]
    
    def value_generator(key):
        return f"warmed_value_for_{key}"
    
    schedule = {
        "interval_minutes": 1,
        "max_batch_size": 10,
        "ttl": 300,
        "tags": {"warmed"}
    }
    
    warmer.register_warming_pattern("test_pattern", key_generator, value_generator, schedule)
    
    # Record some access patterns
    for i in range(3):
        warmer.record_access(f"warm_pattern_key_{i}")
    
    # Run warming cycle
    await warmer.run_warming_cycle()
    
    # Verify keys were warmed
    for i in range(5):
        key = f"warm_pattern_key_{i}"
        value = await multi_cache.get(key)
        assert value == f"warmed_value_for_{key}"
    
    # Test access pattern recording
    warmer.record_access("test_access_key")
    assert len(warmer.access_patterns["test_access_key"]) == 1
    
    print("✅ CacheWarmer tests passed")
    return True


async def test_cache_performance():
    """Test cache performance characteristics."""
    print("🧪 Testing cache performance...")
    
    l1 = L1MemoryCache(max_size=1000)
    l2 = L2RedisCache()
    l3 = L3DatabaseCache()
    multi_cache = MultiLayerCache(l1, l2, l3)
    
    # Performance test: many operations
    num_operations = 100
    
    # Set operations
    start_time = time.time()
    for i in range(num_operations):
        await multi_cache.set(f"perf_key_{i}", f"perf_value_{i}")
    set_duration = time.time() - start_time
    
    # Get operations (should hit L1)
    start_time = time.time()
    for i in range(num_operations):
        value = await multi_cache.get(f"perf_key_{i}")
        assert value == f"perf_value_{i}"
    get_duration = time.time() - start_time
    
    # Get operations should be much faster than set operations
    assert get_duration < set_duration
    
    # Test concurrent operations
    async def concurrent_operation(operation_id):
        key = f"concurrent_key_{operation_id}"
        value = f"concurrent_value_{operation_id}"
        
        await multi_cache.set(key, value)
        retrieved = await multi_cache.get(key)
        return retrieved == value
    
    # Run concurrent operations
    tasks = [concurrent_operation(i) for i in range(50)]
    results = await asyncio.gather(*tasks)
    
    # All operations should succeed
    assert all(results)
    
    # Check final statistics
    stats = multi_cache.get_comprehensive_stats()
    assert stats["overall"]["total_requests"] > num_operations
    assert stats["overall"]["hit_rate"] > 0.5  # Should have good hit rate
    
    print(f"   Set {num_operations} items in {set_duration:.3f}s ({num_operations/set_duration:.1f} ops/sec)")
    print(f"   Get {num_operations} items in {get_duration:.3f}s ({num_operations/get_duration:.1f} ops/sec)")
    print(f"   Overall hit rate: {stats['overall']['hit_rate']:.1%}")
    
    print("✅ Cache performance tests passed")
    return True


def test_cache_status():
    """Test cache status reporting."""
    print("🧪 Testing cache status...")
    
    status = get_cache_status()
    
    assert "multi_layer_stats" in status
    assert "warming_patterns" in status
    assert "active_warming_tasks" in status
    assert "timestamp" in status
    
    # Check multi-layer stats structure
    multi_stats = status["multi_layer_stats"]
    assert "overall" in multi_stats
    assert "l1_memory" in multi_stats
    assert "l2_redis" in multi_stats
    assert "l3_database" in multi_stats
    
    print("✅ Cache status tests passed")
    return True


async def test_cache_edge_cases():
    """Test cache edge cases and error conditions."""
    print("🧪 Testing cache edge cases...")
    
    cache = L1MemoryCache(max_size=2, max_memory_mb=1)
    
    # Test very large values
    large_value = "x" * 1000000  # 1MB string
    success = await cache.set("large_key", large_value)
    assert success == True
    
    # Should trigger eviction due to memory limit
    await cache.set("small_key", "small_value")
    
    # Test None values
    await cache.set("none_key", None)
    value = await cache.get("none_key")
    assert value is None  # Should be distinguishable from cache miss
    
    # Test complex objects
    complex_obj = {
        "list": [1, 2, 3],
        "dict": {"nested": "value"},
        "tuple": (4, 5, 6)
    }
    await cache.set("complex_key", complex_obj)
    retrieved = await cache.get("complex_key")
    assert retrieved["list"] == [1, 2, 3]
    assert retrieved["dict"]["nested"] == "value"
    
    # Test empty tags
    await cache.set("empty_tags", "value", tags=set())
    invalidated = await cache.invalidate_by_tags(set())
    assert invalidated == 0
    
    print("✅ Cache edge case tests passed")
    return True


async def main():
    """Run all advanced caching tests."""
    print("🚀 Starting Advanced Multi-Layer Caching Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Run all tests
        tests = [
            ("l1_memory_cache", test_l1_memory_cache),
            ("l2_redis_cache", test_l2_redis_cache),
            ("l3_database_cache", test_l3_database_cache),
            ("multi_layer_cache", test_multi_layer_cache),
            ("cache_warmer", test_cache_warmer),
            ("cache_performance", test_cache_performance),
            ("cache_status", test_cache_status),
            ("cache_edge_cases", test_cache_edge_cases),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 Advanced Multi-Layer Caching Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate == 1.0:
            print("🏆 All caching tests passed - Advanced caching system ready!")
            return 0
        else:
            print("⚠️ Some caching tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
