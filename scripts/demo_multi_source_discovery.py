#!/usr/bin/env python3
"""
Multi-Source Token Discovery Demo

This script demonstrates the enhanced token discovery system with multiple API sources:
- DexScreener: Cross-chain DEX trending tokens
- DexTools: Ethereum/BSC/Polygon trending with advanced metrics
- CoinGecko: Trending tokens with detailed market data
- Birdeye: Solana-focused token discovery
- CoinMarketCap: New listings and trending

Usage:
    python scripts/demo_multi_source_discovery.py
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from src.agents.discovery import DiscoveryAgent
from src.core.database import DatabaseManager
from src.core.cache import CacheManager
from src.core.metrics import MetricsCollector
from src.agents.coordinator import AgentCoordinator


async def demo_multi_source_discovery():
    """Demonstrate multi-source token discovery capabilities."""
    print("🚀 Multi-Source Token Discovery Demo")
    print("=" * 50)
    
    # Initialize components (mock for demo)
    db_manager = None  # Would be DatabaseManager() in production
    cache_manager = None  # Would be CacheManager() in production
    metrics_collector = None  # Would be MetricsCollector() in production
    coordinator = None  # Would be AgentCoordinator() in production
    
    # Create discovery agent
    discovery_agent = DiscoveryAgent(
        db_manager=db_manager,
        cache_manager=cache_manager, 
        metrics_collector=metrics_collector,
        coordinator=coordinator
    )
    
    try:
        # Initialize agent
        await discovery_agent.initialize()
        
        print("📡 Available Discovery Sources:")
        sources = [
            ("dexscreener", "Cross-chain DEX trending tokens"),
            ("dextools", "Ethereum/BSC/Polygon with advanced metrics"),
            ("coingecko", "Trending tokens with detailed market data"),
            ("birdeye", "Solana-focused token discovery"),
            ("coinmarketcap", "New listings and trending"),
            ("defillama", "DeFi protocol tokens")
        ]
        
        for source, description in sources:
            print(f"   ✅ {source}: {description}")
        
        print("\n🔍 Discovery Scenarios:")
        
        # Scenario 1: Multi-source trending discovery
        print("\n1️⃣ Multi-Source Trending Discovery")
        print("-" * 40)
        
        trending_sources = ["dexscreener", "dextools", "coingecko", "birdeye"]
        print(f"Sources: {', '.join(trending_sources)}")
        print("Discovering trending tokens from all sources...")
        
        # This would work with real API keys
        # result = await discovery_agent.discover_tokens(
        #     sources=trending_sources,
        #     min_age_hours=24,
        #     limit=20
        # )
        
        # Mock result for demo
        mock_result = {
            "tokens": [
                {
                    "symbol": "NEWTOKEN1",
                    "name": "New Token 1",
                    "address": "0x1234...5678",
                    "chain": "ethereum",
                    "price_usd": 0.001,
                    "market_cap_usd": 1000000,
                    "volume_24h_usd": 50000,
                    "liquidity_usd": 100000,
                    "source": "dextools",
                    "discovered_at": datetime.now()
                },
                {
                    "symbol": "SOLTOKEN",
                    "name": "Solana Token",
                    "address": "So11...1112",
                    "chain": "solana", 
                    "price_usd": 0.05,
                    "market_cap_usd": 5000000,
                    "volume_24h_usd": 200000,
                    "source": "birdeye",
                    "discovered_at": datetime.now()
                }
            ],
            "source_results": {
                "dextools": {"tokens_found": 15, "success": True},
                "birdeye": {"tokens_found": 8, "success": True},
                "dexscreener": {"tokens_found": 12, "success": True},
                "coingecko": {"tokens_found": 5, "success": True}
            },
            "total_tokens": 2,
            "execution_time_ms": 2500
        }
        
        print(f"✅ Found {mock_result['total_tokens']} tokens in {mock_result['execution_time_ms']}ms")
        
        for token in mock_result["tokens"]:
            print(f"   📊 {token['symbol']} ({token['name']})")
            print(f"      Chain: {token['chain']}")
            print(f"      Price: ${token['price_usd']:.6f}")
            print(f"      Market Cap: ${token['market_cap_usd']:,.0f}")
            print(f"      Source: {token['source']}")
            print()
        
        # Scenario 2: Chain-specific discovery
        print("2️⃣ Chain-Specific Discovery")
        print("-" * 40)
        
        chains_demo = [
            ("ethereum", ["dexscreener", "dextools", "coingecko"]),
            ("solana", ["birdeye"]),
            ("polygon", ["dexscreener", "dextools"]),
            ("bsc", ["dexscreener", "dextools"])
        ]
        
        for chain, expected_sources in chains_demo:
            print(f"🔗 {chain.upper()} Chain:")
            print(f"   Optimal sources: {', '.join(expected_sources)}")
            
            # This would filter sources automatically
            # result = await discovery_agent.discover_trending_tokens(
            #     limit=10, 
            #     chains=[chain]
            # )
            
            print(f"   ✅ Would discover from {len(expected_sources)} specialized sources")
            print()
        
        # Scenario 3: Age-based filtering
        print("3️⃣ Age-Based Token Filtering")
        print("-" * 40)
        
        age_scenarios = [
            (1, "Ultra-new tokens (< 1 hour)"),
            (24, "New tokens (< 24 hours)"),
            (168, "Recent tokens (< 1 week)")
        ]
        
        for hours, description in age_scenarios:
            print(f"⏰ {description}")
            print(f"   Filter: min_age_hours={hours}")
            print(f"   ✅ Would filter tokens by creation time")
            print()
        
        # Scenario 4: Quality filtering
        print("4️⃣ Quality & Liquidity Filtering")
        print("-" * 40)
        
        quality_filters = [
            ("High Quality", {"min_liquidity": 100000, "min_volume": 50000}),
            ("Medium Quality", {"min_liquidity": 10000, "min_volume": 5000}),
            ("All Tokens", {"min_liquidity": 1000, "min_volume": 100})
        ]
        
        for tier, filters in quality_filters:
            print(f"🎯 {tier}:")
            print(f"   Min Liquidity: ${filters['min_liquidity']:,}")
            print(f"   Min Volume: ${filters['min_volume']:,}")
            print(f"   ✅ Would filter by quality metrics")
            print()
        
        print("🎉 Multi-Source Discovery System Ready!")
        print("\n📈 Key Benefits:")
        print("   ✅ 6 different API sources for comprehensive coverage")
        print("   ✅ Cross-chain support (Ethereum, Solana, BSC, Polygon)")
        print("   ✅ Age-based filtering for new token discovery")
        print("   ✅ Quality filtering by liquidity and volume")
        print("   ✅ Concurrent API calls for fast discovery")
        print("   ✅ Automatic deduplication across sources")
        print("   ✅ Chain-specific source optimization")
        
        print("\n🔧 Next Steps:")
        print("   1. Add API keys for live testing")
        print("   2. Implement advanced scam detection")
        print("   3. Add web scraping for social signals")
        print("   4. Set up continuous monitoring pipeline")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        
    finally:
        # Cleanup
        if discovery_agent:
            await discovery_agent.shutdown()


def print_source_comparison():
    """Print comparison of different discovery sources."""
    print("\n📊 Discovery Source Comparison:")
    print("=" * 60)
    
    sources_info = [
        {
            "name": "DexScreener",
            "chains": ["Ethereum", "BSC", "Polygon", "Arbitrum", "Optimism"],
            "strengths": ["Real-time DEX data", "Cross-chain", "Liquidity info"],
            "focus": "DEX trading pairs"
        },
        {
            "name": "DexTools", 
            "chains": ["Ethereum", "BSC", "Polygon"],
            "strengths": ["Advanced metrics", "Trending algorithms", "Social data"],
            "focus": "Trending analysis"
        },
        {
            "name": "CoinGecko",
            "chains": ["Multi-chain"],
            "strengths": ["Market data", "Historical prices", "Trending coins"],
            "focus": "Market intelligence"
        },
        {
            "name": "Birdeye",
            "chains": ["Solana"],
            "strengths": ["Solana expertise", "Real-time data", "Volume analysis"],
            "focus": "Solana ecosystem"
        },
        {
            "name": "CoinMarketCap",
            "chains": ["Multi-chain"],
            "strengths": ["New listings", "Market cap data", "Volume tracking"],
            "focus": "Market overview"
        },
        {
            "name": "DeFiLlama",
            "chains": ["Multi-chain"],
            "strengths": ["DeFi protocols", "TVL data", "Yield farming"],
            "focus": "DeFi ecosystem"
        }
    ]
    
    for source in sources_info:
        print(f"\n🔍 {source['name']}")
        print(f"   Chains: {', '.join(source['chains'])}")
        print(f"   Focus: {source['focus']}")
        print(f"   Strengths: {', '.join(source['strengths'])}")


if __name__ == "__main__":
    print_source_comparison()
    asyncio.run(demo_multi_source_discovery())
