#!/usr/bin/env python3
"""
Live Token Discovery with Real API Results

This script demonstrates actual token discovery using real APIs:
- DexScreener: Live trending tokens from DEXs
- CoinGecko: Real trending coins with market data
- Real price data and market metrics
- Actual liquidity and volume analysis
- Live scam detection on discovered tokens

Usage:
    python scripts/live_token_discovery.py
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import time


class LiveTokenDiscovery:
    """Live token discovery with real API integration."""

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.discovered_tokens = []
        
    async def initialize(self):
        """Initialize HTTP session."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={"User-Agent": "TokenAnalyzer/1.0"}
        )

    async def shutdown(self):
        """Cleanup HTTP session."""
        if self.session:
            await self.session.close()

    async def discover_from_dexscreener(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Discover real trending tokens from DexScreener."""
        print("🔍 Fetching live data from DexScreener...")
        
        try:
            url = "https://api.dexscreener.com/latest/dex/tokens/trending"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    tokens = []
                    
                    for pair in data.get("pairs", [])[:limit]:
                        base_token = pair.get("baseToken", {})
                        
                        if base_token.get("address"):
                            token = {
                                "source": "dexscreener",
                                "symbol": base_token.get("symbol", ""),
                                "name": base_token.get("name", ""),
                                "address": base_token["address"],
                                "chain": pair.get("chainId", "ethereum").lower(),
                                "price_usd": float(pair.get("priceUsd", 0)),
                                "price_change_24h": float(pair.get("priceChange", {}).get("h24", 0)),
                                "volume_24h_usd": float(pair.get("volume", {}).get("h24", 0)),
                                "liquidity_usd": float(pair.get("liquidity", {}).get("usd", 0)),
                                "market_cap_usd": float(pair.get("marketCap", 0)),
                                "dex": pair.get("dexId", ""),
                                "pair_address": pair.get("pairAddress", ""),
                                "discovered_at": datetime.now().isoformat()
                            }
                            tokens.append(token)
                    
                    print(f"✅ DexScreener: Found {len(tokens)} trending tokens")
                    return tokens
                else:
                    print(f"❌ DexScreener API error: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"❌ DexScreener error: {e}")
            return []

    async def discover_from_coingecko(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Discover real trending tokens from CoinGecko."""
        print("🔍 Fetching live data from CoinGecko...")
        
        try:
            # Get trending coins
            trending_url = "https://api.coingecko.com/api/v3/search/trending"
            
            async with self.session.get(trending_url) as response:
                if response.status == 200:
                    data = await response.json()
                    tokens = []
                    
                    for coin_data in data.get("coins", [])[:limit]:
                        coin = coin_data.get("item", {})
                        coin_id = coin.get("id")
                        
                        if coin_id:
                            # Get detailed coin data
                            await asyncio.sleep(0.2)  # Rate limiting
                            detail_url = f"https://api.coingecko.com/api/v3/coins/{coin_id}"
                            
                            async with self.session.get(detail_url) as detail_response:
                                if detail_response.status == 200:
                                    detail_data = await detail_response.json()
                                    market_data = detail_data.get("market_data", {})
                                    
                                    # Extract contract addresses
                                    platforms = detail_data.get("platforms", {})
                                    for platform, address in platforms.items():
                                        if address and platform in ["ethereum", "polygon-pos", "binance-smart-chain"]:
                                            chain = platform.replace("-pos", "").replace("binance-smart-chain", "bsc")
                                            
                                            token = {
                                                "source": "coingecko",
                                                "symbol": detail_data.get("symbol", "").upper(),
                                                "name": detail_data.get("name", ""),
                                                "address": address,
                                                "chain": chain,
                                                "price_usd": float(market_data.get("current_price", {}).get("usd", 0)),
                                                "price_change_24h": float(market_data.get("price_change_percentage_24h", 0)),
                                                "volume_24h_usd": float(market_data.get("total_volume", {}).get("usd", 0)),
                                                "market_cap_usd": float(market_data.get("market_cap", {}).get("usd", 0)),
                                                "market_cap_rank": market_data.get("market_cap_rank"),
                                                "ath_usd": float(market_data.get("ath", {}).get("usd", 0)),
                                                "ath_change_percentage": float(market_data.get("ath_change_percentage", {}).get("usd", 0)),
                                                "discovered_at": datetime.now().isoformat()
                                            }
                                            tokens.append(token)
                                            break  # Only take first valid contract
                    
                    print(f"✅ CoinGecko: Found {len(tokens)} trending tokens")
                    return tokens
                else:
                    print(f"❌ CoinGecko API error: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"❌ CoinGecko error: {e}")
            return []

    async def get_token_security_info(self, token_address: str, chain: str) -> Dict[str, Any]:
        """Get real security information for a token."""
        try:
            # Use GoPlus Security API for real security data
            url = f"https://api.gopluslabs.io/api/v1/token_security/{chain}?contract_addresses={token_address}"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    token_data = data.get("result", {}).get(token_address.lower(), {})
                    
                    if token_data:
                        return {
                            "is_honeypot": token_data.get("is_honeypot") == "1",
                            "buy_tax": float(token_data.get("buy_tax", 0)),
                            "sell_tax": float(token_data.get("sell_tax", 0)),
                            "is_mintable": token_data.get("is_mintable") == "1",
                            "can_take_back_ownership": token_data.get("can_take_back_ownership") == "1",
                            "owner_change_balance": token_data.get("owner_change_balance") == "1",
                            "hidden_owner": token_data.get("hidden_owner") == "1",
                            "selfdestruct": token_data.get("selfdestruct") == "1",
                            "is_proxy": token_data.get("is_proxy") == "1",
                            "holder_count": int(token_data.get("holder_count", 0)),
                            "total_supply": token_data.get("total_supply", "0"),
                            "creator_address": token_data.get("creator_address", ""),
                            "creator_balance": token_data.get("creator_balance", "0"),
                            "creator_percent": float(token_data.get("creator_percent", 0))
                        }
                        
        except Exception as e:
            print(f"⚠️ Security check failed for {token_address}: {e}")
            
        return {}

    def analyze_token_risk(self, token: Dict[str, Any], security_info: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze real token risk based on actual data."""
        risk_score = 0.0
        risk_factors = []
        
        # Price and market analysis
        price_change_24h = token.get("price_change_24h", 0)
        volume_24h = token.get("volume_24h_usd", 0)
        liquidity = token.get("liquidity_usd", 0)
        market_cap = token.get("market_cap_usd", 0)
        
        # Volume to market cap ratio
        if market_cap > 0:
            volume_ratio = volume_24h / market_cap
            if volume_ratio > 1.0:
                risk_score += 20
                risk_factors.append(f"High volume/mcap ratio: {volume_ratio:.2f}")
        
        # Liquidity analysis
        if liquidity < 10000:
            risk_score += 30
            risk_factors.append(f"Low liquidity: ${liquidity:,.0f}")
        elif liquidity < 50000:
            risk_score += 15
            risk_factors.append(f"Medium liquidity: ${liquidity:,.0f}")
        
        # Security analysis
        if security_info:
            if security_info.get("is_honeypot"):
                risk_score += 100
                risk_factors.append("Identified as honeypot")
            
            buy_tax = security_info.get("buy_tax", 0)
            sell_tax = security_info.get("sell_tax", 0)
            
            if buy_tax > 10:
                risk_score += 25
                risk_factors.append(f"High buy tax: {buy_tax}%")
            
            if sell_tax > 10:
                risk_score += 25
                risk_factors.append(f"High sell tax: {sell_tax}%")
            
            if security_info.get("is_mintable"):
                risk_score += 20
                risk_factors.append("Token is mintable")
            
            if security_info.get("can_take_back_ownership"):
                risk_score += 30
                risk_factors.append("Owner can take back ownership")
            
            if security_info.get("hidden_owner"):
                risk_score += 25
                risk_factors.append("Hidden owner detected")
            
            creator_percent = security_info.get("creator_percent", 0)
            if creator_percent > 20:
                risk_score += 20
                risk_factors.append(f"High creator holdings: {creator_percent}%")
        
        # Determine risk level
        if risk_score >= 80:
            risk_level = "CRITICAL"
        elif risk_score >= 60:
            risk_level = "HIGH"
        elif risk_score >= 40:
            risk_level = "MEDIUM"
        elif risk_score >= 20:
            risk_level = "LOW"
        else:
            risk_level = "SAFE"
        
        return {
            "risk_score": min(100, risk_score),
            "risk_level": risk_level,
            "risk_factors": risk_factors,
            "security_analysis": security_info
        }

    async def run_live_discovery(self):
        """Run live token discovery with real results."""
        print("🚀 Starting Live Token Discovery with Real APIs")
        print("=" * 60)
        
        start_time = time.time()
        
        # Discover from multiple sources
        print("\n📡 Discovering tokens from live APIs...")
        
        dexscreener_tokens = await self.discover_from_dexscreener(15)
        await asyncio.sleep(1)  # Rate limiting
        
        coingecko_tokens = await self.discover_from_coingecko(8)
        
        # Combine and deduplicate
        all_tokens = dexscreener_tokens + coingecko_tokens
        unique_tokens = {}
        
        for token in all_tokens:
            key = f"{token['address']}_{token['chain']}"
            if key not in unique_tokens:
                unique_tokens[key] = token
        
        discovered_tokens = list(unique_tokens.values())
        
        print(f"\n📊 Discovery Summary:")
        print(f"   🔍 DexScreener: {len(dexscreener_tokens)} tokens")
        print(f"   🔍 CoinGecko: {len(coingecko_tokens)} tokens")
        print(f"   🎯 Unique tokens: {len(discovered_tokens)}")
        
        # Analyze top tokens
        print(f"\n🔬 Analyzing top {min(10, len(discovered_tokens))} tokens...")
        
        analyzed_tokens = []
        for i, token in enumerate(discovered_tokens[:10]):
            print(f"\n{i+1}️⃣ Analyzing {token['symbol']} ({token['name']})")
            print(f"   📍 Address: {token['address']}")
            print(f"   ⛓️ Chain: {token['chain']}")
            print(f"   💰 Price: ${token['price_usd']:.6f}")
            print(f"   📈 24h Change: {token['price_change_24h']:.2f}%")
            print(f"   💧 Liquidity: ${token.get('liquidity_usd', 0):,.0f}")
            print(f"   📊 Volume 24h: ${token['volume_24h_usd']:,.0f}")
            
            # Get security info for Ethereum tokens
            security_info = {}
            if token['chain'] == 'ethereum' and token['address']:
                print("   🔒 Checking security...")
                security_info = await self.get_token_security_info(token['address'], '1')
                await asyncio.sleep(0.5)  # Rate limiting
            
            # Analyze risk
            risk_analysis = self.analyze_token_risk(token, security_info)
            
            print(f"   🚨 Risk Level: {risk_analysis['risk_level']}")
            print(f"   📊 Risk Score: {risk_analysis['risk_score']}/100")
            
            if risk_analysis['risk_factors']:
                print("   ⚠️ Risk Factors:")
                for factor in risk_analysis['risk_factors'][:3]:  # Show top 3
                    print(f"      - {factor}")
            
            # Add analysis to token
            token['risk_analysis'] = risk_analysis
            analyzed_tokens.append(token)
        
        # Summary statistics
        total_time = time.time() - start_time
        
        print(f"\n📈 Live Discovery Results Summary:")
        print("=" * 50)
        print(f"⏱️ Total Processing Time: {total_time:.1f} seconds")
        print(f"🎯 Tokens Discovered: {len(discovered_tokens)}")
        print(f"🔬 Tokens Analyzed: {len(analyzed_tokens)}")
        
        # Risk distribution
        risk_distribution = {}
        for token in analyzed_tokens:
            risk_level = token['risk_analysis']['risk_level']
            risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
        
        print(f"\n🚨 Risk Distribution:")
        for level, count in risk_distribution.items():
            print(f"   {level}: {count} tokens")
        
        # Top opportunities (low risk, good metrics)
        opportunities = [
            token for token in analyzed_tokens
            if token['risk_analysis']['risk_level'] in ['SAFE', 'LOW']
            and token.get('liquidity_usd', 0) > 50000
            and token['volume_24h_usd'] > 10000
        ]
        
        print(f"\n💡 Investment Opportunities ({len(opportunities)} found):")
        for token in opportunities[:5]:
            print(f"   🟢 {token['symbol']}: ${token['price_usd']:.6f}")
            print(f"      Risk: {token['risk_analysis']['risk_level']}, Liquidity: ${token.get('liquidity_usd', 0):,.0f}")
        
        # High risk warnings
        high_risk = [
            token for token in analyzed_tokens
            if token['risk_analysis']['risk_level'] in ['HIGH', 'CRITICAL']
        ]
        
        if high_risk:
            print(f"\n⚠️ High Risk Tokens ({len(high_risk)} found):")
            for token in high_risk[:3]:
                print(f"   🔴 {token['symbol']}: {token['risk_analysis']['risk_level']}")
                if token['risk_analysis']['risk_factors']:
                    print(f"      Main risk: {token['risk_analysis']['risk_factors'][0]}")
        
        return analyzed_tokens


async def main():
    """Main function to run live discovery."""
    discovery = LiveTokenDiscovery()
    
    try:
        await discovery.initialize()
        results = await discovery.run_live_discovery()
        
        print(f"\n✅ Live discovery completed successfully!")
        print(f"📊 Total tokens analyzed: {len(results)}")
        
    except Exception as e:
        print(f"❌ Error during live discovery: {e}")
        
    finally:
        await discovery.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
