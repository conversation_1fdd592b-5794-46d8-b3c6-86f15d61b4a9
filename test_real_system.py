#!/usr/bin/env python3
"""
Real-world system test for the AutoGen-based token analyzer.

This script tests the complete agentic system with real API data to validate
that we built a fully functional crypto analytics system, not just fluff.

Usage:
    python test_real_system.py
"""

import asyncio
import os
import sys
import json
import time
from datetime import datetime
from typing import Dict, Any, List
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our system components
from src.utils.fetch_helpers import (
    fetch_coingecko_data,
    fetch_etherscan_data,
    fetch_fear_greed_index,
    fetch_dexscreener_data
)
from src.utils.rate_limit import EtherscanRateLimiter, APIRateLimiter
from src.agents.autogen_coordinator import AutoGenCoordinator
from src.core.database import DatabaseManager
from src.core.cache import CacheManager
from src.core.metrics import MetricsCollector


class RealSystemTester:
    """Comprehensive real-world system tester."""
    
    def __init__(self):
        self.results = {
            "test_start": datetime.utcnow().isoformat(),
            "api_tests": {},
            "rate_limit_tests": {},
            "autogen_tests": {},
            "integration_tests": {},
            "overall_success": False,
            "errors": []
        }
        
        # Test tokens (well-known, legitimate tokens)
        self.test_tokens = [
            {
                "name": "USDC",
                "address": "******************************************",
                "chain_id": 1,
                "expected_symbol": "USDC"
            },
            {
                "name": "WETH", 
                "address": "******************************************",
                "chain_id": 1,
                "expected_symbol": "WETH"
            },
            {
                "name": "UNI",
                "address": "******************************************",
                "chain_id": 1,
                "expected_symbol": "UNI"
            }
        ]
    
    async def test_api_connectivity(self) -> bool:
        """Test all external API connections with real data."""
        logger.info("🔌 Testing API connectivity with real data...")
        
        success_count = 0
        total_tests = 0
        
        # Test CoinGecko API
        try:
            total_tests += 1
            logger.info("Testing CoinGecko API...")
            result = await fetch_coingecko_data(
                "simple/price",
                {
                    "ids": "bitcoin,ethereum,usd-coin",
                    "vs_currencies": "usd",
                    "include_24hr_change": "true"
                }
            )
            
            if result.success and result.data:
                logger.info(f"✅ CoinGecko: Got data for {len(result.data)} tokens")
                self.results["api_tests"]["coingecko"] = {
                    "success": True,
                    "data_points": len(result.data),
                    "sample_data": {k: v for k, v in list(result.data.items())[:1]}
                }
                success_count += 1
            else:
                logger.error(f"❌ CoinGecko failed: {result.error}")
                self.results["api_tests"]["coingecko"] = {"success": False, "error": result.error}
                
        except Exception as e:
            logger.error(f"❌ CoinGecko exception: {e}")
            self.results["api_tests"]["coingecko"] = {"success": False, "error": str(e)}
        
        # Test Etherscan API
        try:
            total_tests += 1
            logger.info("Testing Etherscan API...")
            result = await fetch_etherscan_data(
                "contract",
                "getsourcecode",
                {"address": self.test_tokens[0]["address"]}
            )
            
            if result.success and result.data:
                logger.info("✅ Etherscan: Got contract data")
                self.results["api_tests"]["etherscan"] = {
                    "success": True,
                    "contract_verified": len(result.data) > 0 and result.data[0].get("SourceCode", "") != ""
                }
                success_count += 1
            else:
                logger.error(f"❌ Etherscan failed: {result.error}")
                self.results["api_tests"]["etherscan"] = {"success": False, "error": result.error}
                
        except Exception as e:
            logger.error(f"❌ Etherscan exception: {e}")
            self.results["api_tests"]["etherscan"] = {"success": False, "error": str(e)}
        
        # Test DexScreener API
        try:
            total_tests += 1
            logger.info("Testing DexScreener API...")
            result = await fetch_dexscreener_data(f"dex/tokens/{self.test_tokens[1]['address']}")
            
            if result.success and result.data:
                pairs = result.data.get("pairs", [])
                logger.info(f"✅ DexScreener: Found {len(pairs)} trading pairs")
                self.results["api_tests"]["dexscreener"] = {
                    "success": True,
                    "pairs_found": len(pairs),
                    "has_price_data": len(pairs) > 0 and "priceUsd" in pairs[0]
                }
                success_count += 1
            else:
                logger.error(f"❌ DexScreener failed: {result.error}")
                self.results["api_tests"]["dexscreener"] = {"success": False, "error": result.error}
                
        except Exception as e:
            logger.error(f"❌ DexScreener exception: {e}")
            self.results["api_tests"]["dexscreener"] = {"success": False, "error": str(e)}
        
        # Test Fear & Greed Index
        try:
            total_tests += 1
            logger.info("Testing Fear & Greed Index...")
            result = await fetch_fear_greed_index(limit=1)
            
            if result.success and result.data:
                fng_data = result.data.get("data", [])
                if fng_data:
                    logger.info(f"✅ Fear & Greed: Current index = {fng_data[0].get('value')}")
                    self.results["api_tests"]["fear_greed"] = {
                        "success": True,
                        "current_value": fng_data[0].get("value"),
                        "classification": fng_data[0].get("value_classification")
                    }
                    success_count += 1
                else:
                    logger.error("❌ Fear & Greed: No data returned")
                    self.results["api_tests"]["fear_greed"] = {"success": False, "error": "No data"}
            else:
                logger.error(f"❌ Fear & Greed failed: {result.error}")
                self.results["api_tests"]["fear_greed"] = {"success": False, "error": result.error}
                
        except Exception as e:
            logger.error(f"❌ Fear & Greed exception: {e}")
            self.results["api_tests"]["fear_greed"] = {"success": False, "error": str(e)}
        
        api_success_rate = success_count / total_tests
        logger.info(f"📊 API Tests: {success_count}/{total_tests} passed ({api_success_rate:.1%})")
        
        return api_success_rate >= 0.75  # 75% success rate required
    
    async def test_rate_limiting(self) -> bool:
        """Test rate limiting functionality with real APIs."""
        logger.info("⏱️ Testing rate limiting with real APIs...")
        
        try:
            # Test Etherscan rate limiting
            etherscan_limiter = EtherscanRateLimiter(os.getenv("ETHERSCAN_API_KEY"))
            
            # Make rapid requests to test rate limiting
            start_time = time.time()
            tasks = []
            
            for i in range(8):  # More than free tier limit of 5/sec
                task = fetch_etherscan_data(
                    "account",
                    "balance", 
                    {"address": self.test_tokens[i % len(self.test_tokens)]["address"]}
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            elapsed = time.time() - start_time
            
            successful = [r for r in results if not isinstance(r, Exception) and r.success]
            rate_limited = [r for r in results if not isinstance(r, Exception) and r.rate_limited]
            
            logger.info(f"✅ Rate limiting test: {len(successful)} successful, {len(rate_limited)} rate limited in {elapsed:.2f}s")
            
            self.results["rate_limit_tests"]["etherscan"] = {
                "success": True,
                "total_requests": len(tasks),
                "successful": len(successful),
                "rate_limited": len(rate_limited),
                "elapsed_time": elapsed,
                "effective_rate": len(successful) / elapsed if elapsed > 0 else 0
            }
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Rate limiting test failed: {e}")
            self.results["rate_limit_tests"]["error"] = str(e)
            return False
    
    async def test_autogen_system(self) -> bool:
        """Test the AutoGen multi-agent system with real data."""
        logger.info("🤖 Testing AutoGen multi-agent system...")
        
        try:
            # Initialize core components
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            cache_manager = CacheManager()
            await cache_manager.initialize()
            
            metrics_collector = MetricsCollector()
            await metrics_collector.initialize()
            
            # Initialize AutoGen coordinator
            coordinator = AutoGenCoordinator(
                db_manager=db_manager,
                cache_manager=cache_manager,
                metrics_collector=metrics_collector
            )
            
            await coordinator.initialize()
            
            # Test with a real token
            test_token = self.test_tokens[0]  # USDC
            logger.info(f"🎯 Analyzing {test_token['name']} ({test_token['address']})")
            
            start_time = time.time()
            
            analysis_result = await coordinator.analyze_token(
                token_address=test_token["address"],
                chain_id=test_token["chain_id"],
                analysis_types=["full"],
                priority="high"
            )
            
            elapsed = time.time() - start_time
            
            if analysis_result.get("success", False):
                logger.info(f"✅ AutoGen analysis completed in {elapsed:.2f}s")
                
                # Validate analysis structure
                stages = analysis_result.get("analysis_stages", {})
                final_report = analysis_result.get("final_report", {})
                
                self.results["autogen_tests"]["token_analysis"] = {
                    "success": True,
                    "token": test_token["name"],
                    "elapsed_time": elapsed,
                    "stages_completed": list(stages.keys()),
                    "has_final_report": bool(final_report),
                    "conversation_length": len(analysis_result.get("conversation_log", []))
                }
                
                logger.info(f"📋 Analysis stages: {', '.join(stages.keys())}")
                logger.info(f"📊 Final report generated: {bool(final_report)}")
                
                return True
            else:
                error = analysis_result.get("error", "Unknown error")
                logger.error(f"❌ AutoGen analysis failed: {error}")
                self.results["autogen_tests"]["token_analysis"] = {
                    "success": False,
                    "error": error,
                    "elapsed_time": elapsed
                }
                return False
                
        except Exception as e:
            logger.error(f"❌ AutoGen system test failed: {e}")
            self.results["autogen_tests"]["error"] = str(e)
            return False
        finally:
            # Cleanup
            try:
                await coordinator.shutdown()
                await db_manager.shutdown()
                await cache_manager.shutdown()
                await metrics_collector.shutdown()
            except:
                pass
    
    async def test_integration_workflow(self) -> bool:
        """Test complete integration workflow with multiple tokens."""
        logger.info("🔄 Testing complete integration workflow...")
        
        try:
            workflow_results = []
            
            for token in self.test_tokens[:2]:  # Test first 2 tokens
                logger.info(f"🎯 Processing {token['name']}...")
                
                # Simulate complete workflow
                start_time = time.time()
                
                # 1. Fetch market data
                market_data = await fetch_coingecko_data(
                    f"simple/token_price/ethereum",
                    {"contract_addresses": token["address"], "vs_currencies": "usd"}
                )
                
                # 2. Fetch blockchain data
                contract_data = await fetch_etherscan_data(
                    "contract",
                    "getsourcecode",
                    {"address": token["address"]}
                )
                
                # 3. Fetch DEX data
                dex_data = await fetch_dexscreener_data(f"dex/tokens/{token['address']}")
                
                elapsed = time.time() - start_time
                
                workflow_result = {
                    "token": token["name"],
                    "address": token["address"],
                    "elapsed_time": elapsed,
                    "market_data_success": market_data.success,
                    "contract_data_success": contract_data.success,
                    "dex_data_success": dex_data.success,
                    "overall_success": all([market_data.success, contract_data.success, dex_data.success])
                }
                
                workflow_results.append(workflow_result)
                
                if workflow_result["overall_success"]:
                    logger.info(f"✅ {token['name']} workflow completed in {elapsed:.2f}s")
                else:
                    logger.warning(f"⚠️ {token['name']} workflow had some failures")
            
            self.results["integration_tests"]["workflow"] = {
                "tokens_processed": len(workflow_results),
                "successful_workflows": sum(1 for r in workflow_results if r["overall_success"]),
                "results": workflow_results
            }
            
            success_rate = sum(1 for r in workflow_results if r["overall_success"]) / len(workflow_results)
            logger.info(f"📊 Integration workflow: {success_rate:.1%} success rate")
            
            return success_rate >= 0.5  # 50% success rate required
            
        except Exception as e:
            logger.error(f"❌ Integration workflow test failed: {e}")
            self.results["integration_tests"]["error"] = str(e)
            return False
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive system test."""
        logger.info("🚀 Starting comprehensive real-world system test...")
        logger.info("=" * 60)
        
        test_results = []
        
        # Run all test phases
        test_phases = [
            ("API Connectivity", self.test_api_connectivity),
            ("Rate Limiting", self.test_rate_limiting),
            ("AutoGen System", self.test_autogen_system),
            ("Integration Workflow", self.test_integration_workflow)
        ]
        
        for phase_name, test_func in test_phases:
            logger.info(f"\n🔍 Phase: {phase_name}")
            logger.info("-" * 40)
            
            try:
                result = await test_func()
                test_results.append(result)
                
                if result:
                    logger.info(f"✅ {phase_name}: PASSED")
                else:
                    logger.error(f"❌ {phase_name}: FAILED")
                    
            except Exception as e:
                logger.error(f"💥 {phase_name}: CRASHED - {e}")
                test_results.append(False)
                self.results["errors"].append(f"{phase_name}: {str(e)}")
        
        # Calculate overall success
        overall_success = sum(test_results) >= len(test_results) * 0.75  # 75% pass rate
        self.results["overall_success"] = overall_success
        self.results["test_end"] = datetime.utcnow().isoformat()
        self.results["pass_rate"] = sum(test_results) / len(test_results)
        
        # Print final results
        logger.info("\n" + "=" * 60)
        logger.info("🎯 FINAL RESULTS")
        logger.info("=" * 60)
        
        if overall_success:
            logger.info("🎉 SYSTEM TEST: PASSED")
            logger.info("✅ The agentic crypto analytics system is FULLY FUNCTIONAL!")
        else:
            logger.error("💥 SYSTEM TEST: FAILED")
            logger.error("❌ The system has significant issues that need addressing.")
        
        logger.info(f"📊 Overall pass rate: {self.results['pass_rate']:.1%}")
        logger.info(f"⏱️ Total test duration: {(datetime.fromisoformat(self.results['test_end']) - datetime.fromisoformat(self.results['test_start'])).total_seconds():.1f}s")
        
        return self.results


async def main():
    """Main test runner."""
    print("🚀 Real-World Agentic Crypto Analytics System Test")
    print("=" * 60)
    print("Testing with REAL APIs and REAL data...")
    print("Model: deepseek/deepseek-r1-0528-qwen3-8b:free")
    print("=" * 60)
    
    # Load environment variables from .env file
    from dotenv import load_dotenv
    load_dotenv()

    # Verify environment
    required_keys = ["ETHERSCAN_API_KEY", "OPENROUTER_API_KEY", "COINGECKO_API_KEY"]
    missing_keys = [key for key in required_keys if not os.getenv(key)]
    
    if missing_keys:
        print(f"❌ Missing required environment variables: {', '.join(missing_keys)}")
        return
    
    print("✅ All required API keys found")
    print()
    
    # Run comprehensive test
    tester = RealSystemTester()
    results = await tester.run_comprehensive_test()
    
    # Save results
    with open("real_system_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: real_system_test_results.json")
    
    # Exit with appropriate code
    sys.exit(0 if results["overall_success"] else 1)


if __name__ == "__main__":
    asyncio.run(main())
