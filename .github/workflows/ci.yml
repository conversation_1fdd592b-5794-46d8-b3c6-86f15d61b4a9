name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.12'
  NODE_VERSION: '20'

jobs:
  lint:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"
        
    - name: Run ruff (linting)
      run: |
        ruff check src/ tests/ --output-format=github
        
    - name: Run ruff (formatting)
      run: |
        ruff format src/ tests/ --check
        
    - name: Run black (formatting check)
      run: |
        black --check src/ tests/
        
    - name: Run mypy (type checking)
      run: |
        mypy src/ --strict --show-error-codes
        
    - name: Run bandit (security)
      run: |
        bandit -r src/ -f json -o bandit-report.json
        
    - name: Upload bandit results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: bandit-report
        path: bandit-report.json

  test:
    name: Test Suite
    runs-on: ubuntu-latest
    needs: lint
    
    strategy:
      matrix:
        python-version: ['3.11', '3.12']
        test-type: ['unit', 'integration']
        
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-${{ matrix.python-version }}-pip-${{ hashFiles('**/pyproject.toml') }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev,test]"
        
    - name: Set up test environment
      run: |
        cp .env.example .env.test
        echo "ENVIRONMENT=test" >> .env.test
        echo "DATABASE_URL=sqlite:///:memory:" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/15" >> .env.test
        
    - name: Run unit tests
      if: matrix.test-type == 'unit'
      run: |
        pytest tests/unit/ \
          -v \
          --cov=src \
          --cov-report=xml \
          --cov-report=html \
          --cov-fail-under=80 \
          --junit-xml=test-results-unit.xml \
          -m "unit and not external_api"
          
    - name: Run integration tests
      if: matrix.test-type == 'integration'
      run: |
        pytest tests/integration/ \
          -v \
          --cov=src \
          --cov-report=xml \
          --cov-append \
          --junit-xml=test-results-integration.xml \
          -m "integration and not external_api"
          
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: ${{ matrix.test-type }}
        name: codecov-${{ matrix.python-version }}-${{ matrix.test-type }}
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}-${{ matrix.test-type }}
        path: test-results-*.xml
        
    - name: Upload coverage report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: coverage-${{ matrix.python-version }}-${{ matrix.test-type }}
        path: htmlcov/

  data-validation:
    name: Data Quality Validation
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev,test]"
        pip install great-expectations
        
    - name: Run Great Expectations validation
      run: |
        pytest tests/validation/ \
          -v \
          --junit-xml=test-results-validation.xml \
          -m "not external_api"
          
    - name: Upload validation results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: validation-results
        path: test-results-validation.xml

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  build:
    name: Build Artifacts
    runs-on: ubuntu-latest
    needs: [test, data-validation]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine
        
    - name: Build Python package
      run: |
        python -m build
        
    - name: Check package
      run: |
        twine check dist/*
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: python-package
        path: dist/
        
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: ui/package-lock.json
        
    - name: Install UI dependencies
      working-directory: ui
      run: npm ci
      
    - name: Build UI
      working-directory: ui
      run: |
        npm run build
        npm run export
        
    - name: Upload UI artifacts
      uses: actions/upload-artifact@v3
      with:
        name: ui-build
        path: ui/out/

  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev,test]"
        pip install pytest-benchmark
        
    - name: Run performance tests
      run: |
        pytest tests/ \
          -v \
          --benchmark-only \
          --benchmark-json=benchmark-results.json \
          -m "not external_api"
          
    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: benchmark-results.json

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: python-package
        path: dist/
        
    - name: Download UI artifacts
      uses: actions/download-artifact@v3
      with:
        name: ui-build
        path: ui-build/
        
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add actual deployment commands here
        
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, security, performance]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: python-package
        path: dist/
        
    - name: Download UI artifacts
      uses: actions/download-artifact@v3
      with:
        name: ui-build
        path: ui-build/
        
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add actual deployment commands here
        
  notify:
    name: Notifications
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.deploy-production.result == 'success' || needs.deploy-staging.result == 'success'
      run: |
        echo "Deployment successful!"
        # Add notification logic (Slack, email, etc.)
        
    - name: Notify on failure
      if: failure()
      run: |
        echo "Pipeline failed!"
        # Add failure notification logic
