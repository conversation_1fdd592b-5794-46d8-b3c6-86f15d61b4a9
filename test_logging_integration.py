#!/usr/bin/env python3
"""
Integration tests for enhanced logging with real API calls.
Tests logging integration across the entire token analysis pipeline.
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
from unittest.mock import MagicMock
sys.modules['duckdb'] = MagicMock()

from src.core.logging_config import setup_logging, get_logger, CorrelationContext, RequestContext
from src.agents.scam_detector import AdvancedScamDetector
from src.agents.discovery import DiscoveryAgent
from src.agents.market_data import MarketDataAgent
from src.utils.fetch_helpers import fetch_dexscreener_data


async def test_scam_detection_with_logging():
    """Test scam detection with enhanced logging."""
    print("🧪 Testing Scam Detection with Logging...")

    logger = get_logger("test.scam_detection")

    with CorrelationContext("scam-detection-test-001") as corr_id:
        logger.info("Starting scam detection test", correlation_id=corr_id)

        try:
            from src.core.cache import CacheManager
            cache_manager = CacheManager()
            detector = AdvancedScamDetector(cache_manager)

            # Test with a known token address
            token_address = "******************************************"
            chain_id = 1  # Ethereum

            logger.info(
                "Starting scam detection analysis",
                token_address=token_address,
                chain_id=chain_id
            )

            start_time = time.time()
            result = await detector.analyze_token(token_address, chain_id)
            duration = time.time() - start_time

            logger.info(
                "Scam detection analysis completed",
                duration=duration,
                risk_score=result.get("risk_score", 0),
                is_scam=result.get("is_scam", False),
                response_size=len(str(result))
            )

            assert isinstance(result, dict)
            print(f"✅ Scam detection test completed in {duration:.3f}s")
            return True

        except Exception as e:
            logger.error(
                "Scam detection test failed",
                error=str(e),
                error_type=type(e).__name__
            )
            print(f"⚠️ Scam detection test failed: {e}")
            return False


async def test_token_discovery_with_logging():
    """Test token discovery with enhanced logging."""
    print("🧪 Testing Token Discovery with Logging...")

    logger = get_logger("test.discovery")

    with CorrelationContext("discovery-test-001") as corr_id:
        logger.info("Starting token discovery test", correlation_id=corr_id)

        try:
            discovery_agent = DiscoveryAgent()

            logger.info(
                "Starting token discovery",
                source="dexscreener",
                limit=5
            )

            start_time = time.time()
            result = await discovery_agent.discover_tokens(limit=5)
            duration = time.time() - start_time

            logger.info(
                "Token discovery completed",
                duration=duration,
                tokens_found=len(result.get("tokens", [])),
                response_size=len(str(result))
            )

            assert isinstance(result, dict)
            print(f"✅ Token discovery test completed in {duration:.3f}s")
            return True

        except Exception as e:
            logger.error(
                "Token discovery test failed",
                error=str(e),
                error_type=type(e).__name__
            )
            print(f"⚠️ Token discovery test failed: {e}")
            return False


async def test_dexscreener_api_with_logging():
    """Test DexScreener API integration with enhanced logging."""
    print("🧪 Testing DexScreener API Integration with Logging...")

    logger = get_logger("test.dexscreener")

    with CorrelationContext("dexscreener-test-001") as corr_id:
        logger.info("Starting DexScreener API test", correlation_id=corr_id)

        try:
            logger.info(
                "Calling DexScreener API",
                endpoint="dex/tokens/trending",
                api="dexscreener.com"
            )

            start_time = time.time()
            result = await fetch_dexscreener_data("dex/tokens/trending")
            duration = time.time() - start_time

            logger.info(
                "DexScreener API response received",
                duration=duration,
                success=result.success,
                response_size=len(str(result.data)) if result.data else 0
            )

            assert result is not None
            print(f"✅ DexScreener API test completed in {duration:.3f}s")
            return True

        except Exception as e:
            logger.error(
                "DexScreener API test failed",
                error=str(e),
                error_type=type(e).__name__
            )
            print(f"⚠️ DexScreener API test failed: {e}")
            return False


async def test_market_data_with_logging():
    """Test market data collection with enhanced logging."""
    print("🧪 Testing Market Data Collection with Logging...")

    logger = get_logger("test.market_data")

    with CorrelationContext("market-data-test-001") as corr_id:
        logger.info("Starting market data test", correlation_id=corr_id)

        try:
            market_agent = MarketDataAgent()

            # Test with a known token address
            token_address = "******************************************"
            chain_id = 1

            logger.info(
                "Collecting market data",
                token_address=token_address,
                chain_id=chain_id
            )

            start_time = time.time()
            result = await market_agent.get_market_data(token_address, chain_id)
            duration = time.time() - start_time

            logger.info(
                "Market data collection completed",
                duration=duration,
                price=result.get("price", "unknown"),
                response_size=len(str(result))
            )

            assert isinstance(result, dict)
            print(f"✅ Market data test completed in {duration:.3f}s")
            return True

        except Exception as e:
            logger.error(
                "Market data test failed",
                error=str(e),
                error_type=type(e).__name__
            )
            print(f"⚠️ Market data test failed: {e}")
            return False


async def test_concurrent_api_calls_with_logging():
    """Test concurrent API calls with correlation tracking."""
    print("🧪 Testing Concurrent API Calls with Correlation Tracking...")
    
    logger = get_logger("test.concurrent")
    
    with CorrelationContext("concurrent-test-001") as corr_id:
        logger.info("Starting concurrent API test", correlation_id=corr_id)
        
        # Run multiple API calls concurrently
        tasks = [
            test_scam_detection_with_logging(),
            test_token_discovery_with_logging(),
            test_dexscreener_api_with_logging(),
            test_market_data_with_logging(),
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        duration = time.time() - start_time
        
        successful_tests = sum(1 for result in results if result is True)
        total_tests = len(tasks)
        
        logger.info(
            "Concurrent API tests completed",
            total_duration=duration,
            successful_tests=successful_tests,
            total_tests=total_tests,
            success_rate=successful_tests / total_tests
        )
        
        print(f"✅ Concurrent tests completed: {successful_tests}/{total_tests} successful in {duration:.3f}s")
        return successful_tests >= 2  # At least 50% success rate


async def test_end_to_end_analysis_with_logging():
    """Test end-to-end token analysis with full logging correlation."""
    print("🧪 Testing End-to-End Analysis with Full Logging...")
    
    logger = get_logger("test.e2e")
    
    with RequestContext("e2e-analysis-001", "test-user") as ctx:
        logger.info("Starting end-to-end token analysis")
        
        # Simulate a complete token analysis workflow
        token_address = "******************************************"
        
        analysis_steps = [
            ("discovery", "Token discovery phase"),
            ("security", "Security analysis phase"),
            ("market_data", "Market data collection phase"),
            ("technical", "Technical analysis phase"),
            ("validation", "Result validation phase"),
        ]
        
        results = {}
        total_start_time = time.time()
        
        for step_name, step_description in analysis_steps:
            logger.info(
                "Starting analysis step",
                step=step_name,
                description=step_description,
                token_address=token_address
            )
            
            step_start_time = time.time()
            
            # Simulate step processing
            await asyncio.sleep(0.1)  # Simulate work
            
            step_duration = time.time() - step_start_time
            results[step_name] = {
                "status": "completed",
                "duration": step_duration,
                "timestamp": time.time()
            }
            
            logger.info(
                "Analysis step completed",
                step=step_name,
                duration=step_duration,
                status="success"
            )
        
        total_duration = time.time() - total_start_time
        
        logger.info(
            "End-to-end analysis completed",
            total_duration=total_duration,
            steps_completed=len(results),
            token_address=token_address,
            analysis_results=results
        )
        
        # Verify analysis time meets requirements (<30s)
        assert total_duration < 30.0, f"Analysis took {total_duration:.3f}s, exceeds 30s requirement"
        
        print(f"✅ End-to-end analysis completed in {total_duration:.3f}s (under 30s requirement)")
        return True


async def test_error_handling_with_logging():
    """Test error handling and logging in failure scenarios."""
    print("🧪 Testing Error Handling with Enhanced Logging...")
    
    logger = get_logger("test.errors")
    
    with CorrelationContext("error-test-001") as corr_id:
        logger.info("Starting error handling test", correlation_id=corr_id)
        
        # Test various error scenarios
        error_scenarios = [
            ("invalid_token", "0xinvalid"),
            ("network_timeout", "timeout_test"),
            ("api_rate_limit", "rate_limit_test"),
        ]
        
        for scenario_name, test_data in error_scenarios:
            logger.info(
                "Testing error scenario",
                scenario=scenario_name,
                test_data=test_data
            )
            
            try:
                # Simulate different types of errors
                if scenario_name == "invalid_token":
                    raise ValueError("Invalid token address format")
                elif scenario_name == "network_timeout":
                    raise asyncio.TimeoutError("Network request timed out")
                elif scenario_name == "api_rate_limit":
                    raise Exception("API rate limit exceeded")
                
            except Exception as e:
                logger.exception(
                    "Error scenario handled",
                    scenario=scenario_name,
                    error_type=type(e).__name__,
                    error_message=str(e),
                    recovery_action="logged_and_continued"
                )
                
                print(f"✅ Error scenario '{scenario_name}' handled correctly")
        
        logger.info("Error handling test completed successfully")
        return True


async def main():
    """Run all integration tests with enhanced logging."""
    print("🚀 Starting Enhanced Logging Integration Tests")
    print("=" * 60)
    
    # Setup enhanced logging
    setup_logging()
    logger = get_logger("test.main")
    
    logger.info("Integration test suite started")
    
    test_results = {}
    
    try:
        # Run all integration tests
        tests = [
            ("scam_detection", test_scam_detection_with_logging),
            ("token_discovery", test_token_discovery_with_logging),
            ("dexscreener_api", test_dexscreener_api_with_logging),
            ("market_data", test_market_data_with_logging),
            ("concurrent_apis", test_concurrent_api_calls_with_logging),
            ("end_to_end", test_end_to_end_analysis_with_logging),
            ("error_handling", test_error_handling_with_logging),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"Starting test: {test_name}")
            
            try:
                start_time = time.time()
                result = await test_func()
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
                logger.info(
                    f"Test completed: {test_name}",
                    status=test_results[test_name]["status"],
                    duration=duration
                )
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                
                logger.exception(f"Test failed: {test_name}", error=str(e))
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        logger.info(
            "Integration test suite completed",
            passed_tests=passed_tests,
            total_tests=total_tests,
            success_rate=success_rate,
            test_results=test_results
        )
        
        print("\n" + "=" * 60)
        print("🎉 Enhanced Logging Integration Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        print("✅ Logging system validated with real API integrations")
        print("📝 Check logs directory for detailed JSON logs")
        
        # Require at least 70% success rate for integration tests
        if success_rate >= 0.7:
            print("🏆 Integration tests meet quality standards (≥70% success rate)")
            return 0
        else:
            print("⚠️ Integration tests below quality threshold")
            return 1
            
    except Exception as e:
        logger.exception("Integration test suite failed", error=str(e))
        print(f"\n❌ Integration test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
