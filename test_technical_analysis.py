#!/usr/bin/env python3
"""Test technical analysis with real market data."""

import asyncio
import sys
import os
import json
from datetime import datetime, timedelta
import random

sys.path.insert(0, 'src')

from utils.technical_analysis import analyze_token_technicals, TechnicalAnalyzer
from utils.fetch_helpers import fetch_coingecko_data

async def generate_sample_price_data():
    """Generate realistic sample price data for testing."""
    base_price = 1.0
    price_data = []
    
    # Generate 100 data points over 100 hours
    for i in range(100):
        timestamp = datetime.utcnow() - timedelta(hours=100-i)
        
        # Simulate price movement with trend and volatility
        trend = 0.001 * i  # Slight upward trend
        volatility = random.uniform(-0.05, 0.05)  # 5% volatility
        
        price = base_price * (1 + trend + volatility)
        volume = random.uniform(10000, 100000)
        
        price_data.append({
            "timestamp": timestamp.isoformat(),
            "price": price,
            "volume": volume
        })
        
        base_price = price  # Use previous price as base for next
    
    return price_data

async def test_with_real_coingecko_data():
    """Test technical analysis with real CoinGecko data."""
    print("📊 Testing with Real CoinGecko Data...")
    print("-" * 40)
    
    try:
        # Get Bitcoin price history from CoinGecko (daily data - free tier)
        result = await fetch_coingecko_data(
            "coins/bitcoin/market_chart",
            {
                "vs_currency": "usd",
                "days": "90"  # 90 days of daily data (free tier)
            }
        )
        
        if result.success and result.data:
            prices = result.data.get("prices", [])
            volumes = result.data.get("total_volumes", [])
            
            # Convert to our format
            price_history = []
            for i, (timestamp_ms, price) in enumerate(prices):
                volume = volumes[i][1] if i < len(volumes) else 0
                
                price_history.append({
                    "timestamp": datetime.fromtimestamp(timestamp_ms / 1000).isoformat(),
                    "price": price,
                    "volume": volume
                })
            
            current_price = prices[-1][1] if prices else 0
            
            print(f"✅ Retrieved {len(price_history)} Bitcoin price points")
            print(f"📈 Current BTC Price: ${current_price:,.2f}")
            print(f"📅 Data Range: {len(price_history)} hours")
            
            # Perform technical analysis
            analysis = analyze_token_technicals(price_history, current_price)
            
            if analysis.get("success"):
                print("\n🎯 Technical Analysis Results:")
                
                indicators = analysis["indicators"]
                signals = analysis["signals"]
                
                print(f"📊 Data Points Used: {analysis['data_points']}")
                print(f"🎯 Overall Signal: {signals['overall']} (Confidence: {signals['confidence']:.1%})")
                
                print("\n📈 Trend Indicators:")
                trend = indicators["trend"]
                print(f"  SMA 20: ${trend['sma_20']:,.2f}" if trend['sma_20'] else "  SMA 20: N/A")
                print(f"  SMA 50: ${trend['sma_50']:,.2f}" if trend['sma_50'] else "  SMA 50: N/A")
                print(f"  EMA 12: ${trend['ema_12']:,.2f}" if trend['ema_12'] else "  EMA 12: N/A")
                print(f"  Signal: {signals['trend']}")
                
                print("\n⚡ Momentum Indicators:")
                momentum = indicators["momentum"]
                print(f"  RSI (14): {momentum['rsi_14']:.1f}" if momentum['rsi_14'] else "  RSI (14): N/A")
                print(f"  MACD Line: {momentum['macd_line']:.2f}" if momentum['macd_line'] else "  MACD Line: N/A")
                print(f"  MACD Signal: {momentum['macd_signal']:.2f}" if momentum['macd_signal'] else "  MACD Signal: N/A")
                print(f"  Signal: {signals['momentum']}")
                
                print("\n📊 Volatility Indicators:")
                volatility = indicators["volatility"]
                print(f"  BB Upper: ${volatility['bb_upper']:,.2f}" if volatility['bb_upper'] else "  BB Upper: N/A")
                print(f"  BB Lower: ${volatility['bb_lower']:,.2f}" if volatility['bb_lower'] else "  BB Lower: N/A")
                print(f"  BB Width: {volatility['bb_width']:.3f}" if volatility['bb_width'] else "  BB Width: N/A")
                print(f"  Signal: {signals['volatility']}")
                
                return True
            else:
                print(f"❌ Analysis failed: {analysis.get('error')}")
                return False
        else:
            print(f"❌ Failed to fetch CoinGecko data: {result.error}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

async def test_with_sample_data():
    """Test technical analysis with generated sample data."""
    print("\n📊 Testing with Sample Data...")
    print("-" * 40)
    
    try:
        # Generate sample data
        price_history = await generate_sample_price_data()
        current_price = price_history[-1]["price"]
        
        print(f"✅ Generated {len(price_history)} sample price points")
        print(f"📈 Current Price: ${current_price:.6f}")
        
        # Perform technical analysis
        analysis = analyze_token_technicals(price_history, current_price)
        
        if analysis.get("success"):
            signals = analysis["signals"]
            print(f"🎯 Overall Signal: {signals['overall']} (Confidence: {signals['confidence']:.1%})")
            print(f"📈 Trend: {signals['trend']}")
            print(f"⚡ Momentum: {signals['momentum']}")
            print(f"📊 Volatility: {signals['volatility']}")
            return True
        else:
            print(f"❌ Analysis failed: {analysis.get('error')}")
            return False
            
    except Exception as e:
        print(f"💥 Exception: {e}")
        return False

async def test_ta_lib_indicators():
    """Test individual TA-Lib indicators."""
    print("\n🔧 Testing Individual TA-Lib Indicators...")
    print("-" * 40)
    
    try:
        import talib
        import numpy as np
        
        # Generate test data
        closes = np.array([1.0, 1.1, 1.05, 1.2, 1.15, 1.3, 1.25, 1.4, 1.35, 1.5, 
                          1.45, 1.6, 1.55, 1.7, 1.65, 1.8, 1.75, 1.9, 1.85, 2.0])
        
        # Test RSI
        rsi = talib.RSI(closes, timeperiod=14)
        print(f"✅ RSI (14): {rsi[-1]:.2f}")
        
        # Test MACD
        macd_line, macd_signal, macd_hist = talib.MACD(closes)
        print(f"✅ MACD Line: {macd_line[-1]:.4f}")
        print(f"✅ MACD Signal: {macd_signal[-1]:.4f}")
        
        # Test Bollinger Bands
        bb_upper, bb_middle, bb_lower = talib.BBANDS(closes)
        print(f"✅ BB Upper: {bb_upper[-1]:.4f}")
        print(f"✅ BB Lower: {bb_lower[-1]:.4f}")
        
        # Test SMA
        sma = talib.SMA(closes, timeperiod=10)
        print(f"✅ SMA (10): {sma[-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ TA-Lib test failed: {e}")
        return False

async def main():
    """Run all technical analysis tests."""
    print("🚀 Technical Analysis Testing Suite")
    print("=" * 50)
    
    results = []
    
    # Test 1: Individual TA-Lib indicators
    results.append(await test_ta_lib_indicators())
    
    # Test 2: Sample data analysis
    results.append(await test_with_sample_data())
    
    # Test 3: Real CoinGecko data analysis
    results.append(await test_with_real_coingecko_data())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Passed: {passed}/{total}")
    print(f"📈 Success Rate: {passed/total:.1%}")
    
    if passed == total:
        print("🎉 All technical analysis tests PASSED!")
        print("✅ TA-Lib integration is fully functional!")
    else:
        print("⚠️ Some tests failed - check implementation")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
