#!/usr/bin/env python3
"""
Comprehensive tests for the production monitoring dashboard.
Tests all monitoring components with real-world scenarios.
"""

import asyncio
import json
import sys
import time
from pathlib import Path
from unittest.mock import MagicMock
import threading

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()
sys.modules['uvicorn'] = MagicMock()

from src.monitoring.dashboard import (
    MetricsCollector,
    AlertManager,
    MonitoringDashboard,
    MetricType,
    AlertSeverity,
    Metric,
    Alert,
    timer_context
)


def test_metrics_collector():
    """Test MetricsCollector functionality."""
    print("🧪 Testing MetricsCollector...")
    
    collector = MetricsCollector(max_history=100)
    
    # Test counter
    collector.increment_counter("api_requests", 1.0, {"endpoint": "/analyze"})
    collector.increment_counter("api_requests", 2.0, {"endpoint": "/analyze"})
    
    # Test gauge
    collector.set_gauge("memory_usage", 0.75, {"instance": "worker-1"})
    collector.set_gauge("cpu_usage", 0.45, {"instance": "worker-1"})
    
    # Test histogram
    for i in range(10):
        collector.record_histogram("response_time", i * 0.1, {"service": "api"})
    
    # Test timer
    for i in range(5):
        collector.record_timer("processing_time", i * 100, {"operation": "analysis"})
    
    # Get metrics
    metrics = collector.get_metrics()
    assert len(metrics) > 0
    
    # Get summary stats
    stats = collector.get_summary_stats()
    assert "counters" in stats
    assert "gauges" in stats
    assert "histograms" in stats
    assert "timers" in stats
    
    # Verify counter values
    counter_key = "api_requests{endpoint=/analyze}"
    assert stats["counters"][counter_key] == 3.0
    
    # Verify gauge values
    gauge_key = "memory_usage{instance=worker-1}"
    assert stats["gauges"][gauge_key] == 0.75
    
    # Verify histogram stats
    histogram_key = "response_time{service=api}"
    assert histogram_key in stats["histograms"]
    hist_stats = stats["histograms"][histogram_key]
    assert hist_stats["count"] == 10
    assert hist_stats["min"] == 0.0
    assert hist_stats["max"] == 0.9
    
    print("✅ MetricsCollector tests passed")
    return True


def test_alert_manager():
    """Test AlertManager functionality."""
    print("🧪 Testing AlertManager...")
    
    collector = MetricsCollector()
    alert_manager = AlertManager(collector)
    
    # Set up test metrics that should trigger alerts
    collector.set_gauge("api_response_time", 10.0)  # Should trigger warning
    collector.set_gauge("error_rate", 0.15)  # Should trigger critical
    collector.set_gauge("memory_usage", 0.85)  # Should trigger warning
    collector.set_gauge("cache_hit_rate", 0.40)  # Should trigger critical (lower is worse)
    
    # Check thresholds
    alert_manager.check_thresholds()
    
    # Get active alerts
    active_alerts = alert_manager.get_active_alerts()
    assert len(active_alerts) > 0
    
    # Verify specific alerts
    alert_names = [alert["metric_name"] for alert in active_alerts]
    assert "api_response_time" in alert_names
    assert "error_rate" in alert_names
    assert "memory_usage" in alert_names
    assert "cache_hit_rate" in alert_names
    
    # Test alert resolution
    collector.set_gauge("api_response_time", 2.0)  # Back to normal
    alert_manager.check_thresholds()
    
    # Get alert history
    history = alert_manager.get_alert_history()
    assert len(history) > 0
    
    print("✅ AlertManager tests passed")
    return True


def test_timer_context():
    """Test timer context manager."""
    print("🧪 Testing timer context manager...")

    # Import timer_context with the collector
    from src.monitoring.dashboard import timer_context, metrics_collector

    # Test timer context
    with timer_context("test_operation", {"component": "test"}):
        time.sleep(0.1)  # Simulate work

    # Verify timer was recorded
    stats = metrics_collector.get_summary_stats()
    timer_key = "test_operation{component=test}"
    assert timer_key in stats["timers"]

    timer_stats = stats["timers"][timer_key]
    assert timer_stats["count"] == 1
    assert timer_stats["avg"] >= 100  # At least 100ms

    print("✅ Timer context tests passed")
    return True


def test_concurrent_metrics():
    """Test thread safety with concurrent metric collection."""
    print("🧪 Testing concurrent metrics collection...")
    
    collector = MetricsCollector()
    
    def worker(worker_id: int):
        """Worker function for concurrent testing."""
        for i in range(100):
            collector.increment_counter("concurrent_test", 1.0, {"worker": str(worker_id)})
            collector.set_gauge("worker_status", i, {"worker": str(worker_id)})
            collector.record_histogram("worker_latency", i * 0.01, {"worker": str(worker_id)})
    
    # Start multiple threads
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Verify results
    stats = collector.get_summary_stats()
    
    # Should have metrics from all workers
    counter_keys = [key for key in stats["counters"].keys() if "concurrent_test" in key]
    assert len(counter_keys) == 5  # One per worker
    
    # Total count should be 500 (5 workers * 100 increments)
    total_count = sum(stats["counters"][key] for key in counter_keys)
    assert total_count == 500
    
    print("✅ Concurrent metrics tests passed")
    return True


def test_metric_serialization():
    """Test metric and alert serialization."""
    print("🧪 Testing metric serialization...")
    
    from datetime import datetime, timezone
    
    # Test Metric serialization
    metric = Metric(
        name="test_metric",
        value=42.5,
        metric_type=MetricType.GAUGE,
        timestamp=datetime.now(timezone.utc),
        labels={"service": "test", "version": "1.0"}
    )
    
    metric_dict = metric.to_dict()
    assert metric_dict["name"] == "test_metric"
    assert metric_dict["value"] == 42.5
    assert metric_dict["type"] == "gauge"
    assert "timestamp" in metric_dict
    assert metric_dict["labels"]["service"] == "test"
    
    # Test Alert serialization
    alert = Alert(
        id="test_alert",
        message="Test alert message",
        severity=AlertSeverity.WARNING,
        timestamp=datetime.now(timezone.utc),
        metric_name="test_metric",
        current_value=75.0,
        threshold=50.0
    )
    
    alert_dict = alert.to_dict()
    assert alert_dict["id"] == "test_alert"
    assert alert_dict["severity"] == "warning"
    assert alert_dict["current_value"] == 75.0
    assert alert_dict["threshold"] == 50.0
    assert not alert_dict["resolved"]
    
    print("✅ Serialization tests passed")
    return True


def test_performance_under_load():
    """Test monitoring system performance under load."""
    print("🧪 Testing performance under load...")
    
    collector = MetricsCollector()
    
    # High-frequency metric collection
    start_time = time.time()
    
    for i in range(10000):
        collector.increment_counter("load_test", 1.0)
        collector.set_gauge("load_gauge", i % 100)
        collector.record_histogram("load_histogram", i % 50)
        collector.record_timer("load_timer", i % 1000)
    
    collection_time = time.time() - start_time
    
    # Verify performance
    assert collection_time < 5.0, f"Collection took too long: {collection_time:.3f}s"
    
    # Verify data integrity
    stats = collector.get_summary_stats()
    assert stats["counters"]["load_test"] == 10000
    assert stats["histograms"]["load_histogram"]["count"] > 0
    
    # Test stats calculation performance
    start_time = time.time()
    stats = collector.get_summary_stats()
    stats_time = time.time() - start_time
    
    assert stats_time < 1.0, f"Stats calculation took too long: {stats_time:.3f}s"
    
    print(f"✅ Performance tests passed (collection: {collection_time:.3f}s, stats: {stats_time:.3f}s)")
    return True


async def test_monitoring_dashboard():
    """Test MonitoringDashboard functionality."""
    print("🧪 Testing MonitoringDashboard...")

    # Create dashboard instance
    dashboard = MonitoringDashboard()

    # Test that routes are set up
    assert dashboard.app is not None

    # Test HTML generation
    html = dashboard._get_dashboard_html()
    assert "Token Analyzer Monitoring Dashboard" in html
    assert "System Status" in html
    assert "Key Metrics" in html
    assert "Active Alerts" in html

    # Test background task initialization
    await dashboard._start_background_tasks()
    assert dashboard._background_started

    print("✅ MonitoringDashboard tests passed")
    return True


async def test_real_world_scenario():
    """Test a realistic monitoring scenario."""
    print("🧪 Testing real-world monitoring scenario...")
    
    collector = MetricsCollector()
    alert_manager = AlertManager(collector)
    
    # Simulate a token analysis workflow
    scenarios = [
        ("normal_operation", {"api_response_time": 2.0, "error_rate": 0.01, "memory_usage": 0.60}),
        ("high_load", {"api_response_time": 8.0, "error_rate": 0.03, "memory_usage": 0.85}),
        ("critical_issues", {"api_response_time": 35.0, "error_rate": 0.15, "memory_usage": 0.98}),
        ("recovery", {"api_response_time": 3.0, "error_rate": 0.02, "memory_usage": 0.70}),
    ]
    
    for scenario_name, metrics in scenarios:
        print(f"   Simulating {scenario_name}...")
        
        # Set metrics
        for metric_name, value in metrics.items():
            collector.set_gauge(metric_name, value)
        
        # Check for alerts
        alert_manager.check_thresholds()
        active_alerts = alert_manager.get_active_alerts()
        
        if scenario_name == "normal_operation":
            assert len(active_alerts) == 0, "Should have no alerts during normal operation"
        elif scenario_name == "critical_issues":
            assert len(active_alerts) > 0, "Should have alerts during critical issues"
            critical_alerts = [a for a in active_alerts if a["severity"] == "critical"]
            assert len(critical_alerts) > 0, "Should have critical alerts"
        
        # Small delay between scenarios
        await asyncio.sleep(0.1)
    
    # Verify alert history
    history = alert_manager.get_alert_history()
    assert len(history) > 0, "Should have alert history"
    
    print("✅ Real-world scenario tests passed")
    return True


async def main():
    """Run all monitoring dashboard tests."""
    print("🚀 Starting Monitoring Dashboard Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Run all tests
        tests = [
            ("metrics_collector", test_metrics_collector),
            ("alert_manager", test_alert_manager),
            ("timer_context", test_timer_context),
            ("concurrent_metrics", test_concurrent_metrics),
            ("serialization", test_metric_serialization),
            ("performance", test_performance_under_load),
            ("dashboard", test_monitoring_dashboard),
            ("real_world", test_real_world_scenario),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 Monitoring Dashboard Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate == 1.0:
            print("🏆 All tests passed - Monitoring system ready for production!")
            return 0
        else:
            print("⚠️ Some tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
