#!/usr/bin/env python3
"""Test the new token discovery system."""

import asyncio
import sys
import os
sys.path.insert(0, 'src')

from utils.fetch_helpers import discover_newest_tokens

async def test_token_discovery():
    """Test token discovery with real API calls."""
    print("🔍 Testing Token Discovery System...")
    print("=" * 50)
    
    # Test with realistic parameters
    result = await discover_newest_tokens(
        min_liquidity_usd=5000,  # $5K minimum liquidity
        max_age_hours=48,        # 48 hours max age
        limit=10                 # Top 10 newest tokens
    )
    
    print(f"✅ SUCCESS: {result.success}")
    
    if result.success:
        tokens = result.data['tokens']
        total_found = result.data['total_found']
        filters = result.data['filters']
        
        print(f"📊 Found {total_found} new tokens matching criteria:")
        print(f"   - Min Liquidity: ${filters['min_liquidity_usd']:,}")
        print(f"   - Max Age: {filters['max_age_hours']} hours")
        print(f"   - Chains: {', '.join(filters['chains_searched'])}")
        print()
        
        if tokens:
            print("🎯 Top New Tokens:")
            for i, token in enumerate(tokens[:5], 1):
                print(f"{i}. {token['symbol']} ({token['name']})")
                print(f"   Chain: {token['chain_id']}")
                print(f"   Address: {token['address']}")
                print(f"   Age: {token['age_hours']:.1f} hours")
                print(f"   Price: ${float(token['price_usd'] or 0):.6f}")
                print(f"   Liquidity: ${token['liquidity_usd']:,.0f}")
                print(f"   Volume 24h: ${token['volume_24h']:,.0f}")
                print(f"   DEX: {token['dex_id']}")
                print()
        else:
            print("⚠️ No tokens found matching criteria")
    else:
        print(f"❌ ERROR: {result.error}")

if __name__ == "__main__":
    asyncio.run(test_token_discovery())
