#!/usr/bin/env python3
"""
Isolated logging tests that don't depend on database imports.
Tests the enhanced logging functionality directly.
"""

import asyncio
import json
import logging
import os
import tempfile
import uuid
from contextlib import contextmanager
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()
sys.modules['src.core.database'] = MagicMock()

# Now import our logging module
from src.core.logging_config import (
    add_correlation_id,
    add_system_context,
    filter_sensitive_data,
    add_performance_metrics,
    CorrelationContext,
    RequestContext,
    get_logger,
    set_correlation_id,
    get_correlation_id,
    correlation_id,
    request_id,
    user_id,
    session_id,
    trace_id,
)


def test_correlation_tracking():
    """Test correlation ID functionality."""
    print("🧪 Testing Correlation ID Tracking...")
    
    # Test setting and getting correlation ID
    test_id = "test-correlation-123"
    set_correlation_id(test_id)
    assert get_correlation_id() == test_id
    print("✅ Basic correlation ID set/get works")
    
    # Test correlation context manager
    with CorrelationContext("context-test-456") as corr_id:
        assert corr_id == "context-test-456"
        assert get_correlation_id() == "context-test-456"
    print("✅ Correlation context manager works")
    
    # Test request context
    with RequestContext("req-123", "user-456"):
        event_dict = {"message": "test"}
        result = add_correlation_id(None, "info", event_dict)
        assert result["request_id"] == "req-123"
        assert result["user_id"] == "user-456"
    print("✅ Request context works")


def test_sensitive_data_filtering():
    """Test sensitive data filtering."""
    print("🧪 Testing Sensitive Data Filtering...")
    
    # Test simple sensitive data
    event_dict = {
        "username": "testuser",
        "password": "secret123",
        "api_key": "sk-1234567890",
        "token": "bearer-xyz",
        "message": "login attempt"
    }
    
    result = filter_sensitive_data(None, "info", event_dict)
    
    assert result["username"] == "testuser"  # Not sensitive
    assert result["password"] == "[REDACTED]"
    assert result["api_key"] == "[REDACTED]"
    assert result["token"] == "[REDACTED]"
    assert result["message"] == "login attempt"
    print("✅ Simple sensitive data filtering works")
    
    # Test nested sensitive data
    nested_dict = {
        "user_data": {
            "username": "testuser",
            "credentials": {
                "password": "secret",
                "api_key": "key123"
            },
            "metadata": {
                "ip": "127.0.0.1",
                "private_key": "pk-secret"
            }
        }
    }
    
    result = filter_sensitive_data(None, "info", nested_dict)
    
    assert result["user_data"]["username"] == "testuser"
    assert result["user_data"]["credentials"] == "[REDACTED]"
    assert result["user_data"]["metadata"]["ip"] == "127.0.0.1"
    assert result["user_data"]["metadata"]["private_key"] == "[REDACTED]"
    print("✅ Nested sensitive data filtering works")


@patch('os.uname')
@patch('os.getpid')
def test_system_context(mock_getpid, mock_uname):
    """Test system context addition."""
    print("🧪 Testing System Context...")
    
    # Mock system info
    mock_uname.return_value = Mock(nodename="test-host")
    mock_getpid.return_value = 12345
    
    event_dict = {"message": "test"}
    result = add_system_context(None, "info", event_dict)
    
    assert result["service"] == "token-analyzer"
    assert result["hostname"] == "test-host"
    assert result["process_id"] == 12345
    assert "version" in result
    assert "environment" in result
    print("✅ System context addition works")


@patch('src.core.logging_config.datetime')
def test_performance_metrics(mock_datetime):
    """Test performance metrics addition."""
    print("🧪 Testing Performance Metrics...")
    
    # Mock datetime
    mock_datetime.utcnow.return_value.timestamp.return_value = 1234567890.123
    
    event_dict = {"message": "test"}
    result = add_performance_metrics(None, "info", event_dict)
    
    assert result["timestamp_ms"] == 1234567890123
    assert result["message"] == "test"
    print("✅ Performance metrics addition works")


async def test_async_logging():
    """Test async logging performance."""
    print("🧪 Testing Async Logging Performance...")
    
    # Mock setup_logging to avoid config dependencies
    with patch('src.core.logging_config.setup_logging'):
        logger = get_logger("test.async")
        
        start_time = asyncio.get_event_loop().time()
        
        # Log many messages concurrently
        async def log_worker(worker_id: int):
            with CorrelationContext(f"worker-{worker_id}"):
                for i in range(10):
                    await asyncio.sleep(0.001)  # Simulate async work
                    logger.info(
                        "Async log message",
                        worker_id=worker_id,
                        message_id=i,
                        action="async_test"
                    )
        
        # Run multiple workers concurrently
        tasks = [log_worker(i) for i in range(3)]
        await asyncio.gather(*tasks)
        
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time
        
        # Should complete quickly (less than 1 second for 30 messages)
        assert duration < 1.0
        print(f"✅ Async logging completed in {duration:.3f}s")


def test_edge_cases():
    """Test edge cases and error conditions."""
    print("🧪 Testing Edge Cases...")
    
    # Test with None values
    event_dict = {"message": None, "value": None}
    result = filter_sensitive_data(None, "info", event_dict)
    assert result["message"] is None
    assert result["value"] is None
    print("✅ None values handled correctly")
    
    # Test with empty dict
    result = filter_sensitive_data(None, "info", {})
    assert result == {}
    print("✅ Empty dict handled correctly")
    
    # Test correlation ID with None
    correlation_id.set(None)
    event_dict = {"message": "test"}
    result = add_correlation_id(None, "info", event_dict)
    assert "correlation_id" in result
    assert isinstance(result["correlation_id"], str)
    print("✅ Auto-generated correlation ID works")


async def main():
    """Run all isolated logging tests."""
    print("🚀 Starting Isolated Logging Tests")
    print("=" * 50)
    
    try:
        test_correlation_tracking()
        test_sensitive_data_filtering()
        test_system_context()
        test_performance_metrics()
        await test_async_logging()
        test_edge_cases()
        
        print("\n" + "=" * 50)
        print("🎉 All isolated logging tests passed!")
        print("✅ Enhanced logging system is working correctly")
        print("📊 Core functionality validated without dependencies")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
