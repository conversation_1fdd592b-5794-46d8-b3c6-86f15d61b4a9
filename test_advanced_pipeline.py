#!/usr/bin/env python3
"""
Comprehensive tests for the advanced data pipeline architecture.
Tests all pipeline components with real-world data processing scenarios.
"""

import asyncio
import json
import sys
import time
from pathlib import Path
from unittest.mock import MagicMock
from datetime import datetime, timezone

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()

from src.pipelines.advanced_pipeline import (
    PipelineStage,
    DataFormat,
    ProcessingMode,
    PipelineStatus,
    DataRecord,
    PipelineMetrics,
    PipelineProcessor,
    ValidationProcessor,
    TransformationProcessor,
    EnrichmentProcessor,
    AggregationProcessor,
    DataPipeline,
    StreamingPipeline,
    PipelineOrchestrator,
    get_pipeline_status
)


def test_data_record():
    """Test DataRecord functionality."""
    print("🧪 Testing DataRecord...")
    
    # Create test record
    record = DataRecord(
        id="test-123",
        data={"name": "<PERSON>", "age": 30, "city": "New York"},
        metadata={"source": "api", "version": "1.0"},
        source="user_input"
    )
    
    # Test basic properties
    assert record.id == "test-123"
    assert record.data["name"] == "Alice"
    assert record.source == "user_input"
    
    # Test processing history
    record.add_processing_step("validation")
    record.add_processing_step("transformation")
    
    assert len(record.processing_history) == 2
    assert "validation" in record.processing_history[0]
    assert "transformation" in record.processing_history[1]
    
    # Test serialization
    record_dict = record.to_dict()
    assert record_dict["id"] == "test-123"
    assert record_dict["data"]["name"] == "Alice"
    assert len(record_dict["processing_history"]) == 2
    
    print("✅ DataRecord tests passed")
    return True


def test_pipeline_metrics():
    """Test PipelineMetrics functionality."""
    print("🧪 Testing PipelineMetrics...")
    
    metrics = PipelineMetrics()
    
    # Test initial state
    assert metrics.records_processed == 0
    assert metrics.records_failed == 0
    assert metrics.error_rate == 0.0
    
    # Test metric updates
    metrics.update_metrics(10, 2, 5.0)  # 10 processed, 2 failed, 5 seconds
    
    assert metrics.records_processed == 10
    assert metrics.records_failed == 2
    assert metrics.processing_time == 5.0
    assert metrics.error_rate == 2 / 12  # 2 failed out of 12 total
    assert metrics.throughput == 10 / 5.0  # 10 records in 5 seconds
    
    # Test additional updates
    metrics.update_metrics(5, 1, 2.0)  # 5 more processed, 1 more failed, 2 more seconds
    
    assert metrics.records_processed == 15
    assert metrics.records_failed == 3
    assert metrics.processing_time == 7.0
    assert metrics.error_rate == 3 / 18  # 3 failed out of 18 total
    
    print("✅ PipelineMetrics tests passed")
    return True


async def test_validation_processor():
    """Test ValidationProcessor functionality."""
    print("🧪 Testing ValidationProcessor...")
    
    # Define validation schema
    schema = {
        "required": ["name", "age"],
        "types": {
            "name": str,
            "age": int,
            "email": str
        },
        "constraints": {
            "age": {"min": 0, "max": 150},
            "email": {"pattern": r"^[^@]+@[^@]+\.[^@]+$"}
        }
    }
    
    processor = ValidationProcessor("test_validator", schema)
    
    # Test valid record
    valid_record = DataRecord(
        id="valid-1",
        data={"name": "Alice", "age": 30, "email": "<EMAIL>"}
    )
    
    result = await processor.process(valid_record)
    assert result is not None
    assert result.id == "valid-1"
    assert "validation:test_validator" in result.processing_history[0]
    
    # Test missing required field
    invalid_record1 = DataRecord(
        id="invalid-1",
        data={"name": "Bob"}  # Missing age
    )
    
    result = await processor.process(invalid_record1)
    assert result is None  # Should be rejected
    
    # Test type validation
    invalid_record2 = DataRecord(
        id="invalid-2",
        data={"name": "Charlie", "age": "thirty"}  # Wrong type for age
    )
    
    result = await processor.process(invalid_record2)
    assert result is None  # Should be rejected
    
    # Test constraint validation
    invalid_record3 = DataRecord(
        id="invalid-3",
        data={"name": "David", "age": 200}  # Age exceeds max
    )
    
    result = await processor.process(invalid_record3)
    assert result is None  # Should be rejected
    
    # Test pattern validation
    invalid_record4 = DataRecord(
        id="invalid-4",
        data={"name": "Eve", "age": 25, "email": "invalid-email"}
    )
    
    result = await processor.process(invalid_record4)
    assert result is None  # Should be rejected
    
    # Check processor metrics
    metrics = processor.get_metrics()
    assert metrics.records_processed == 1  # Only one valid record
    assert metrics.records_failed == 4     # Four invalid records
    
    print("✅ ValidationProcessor tests passed")
    return True


async def test_transformation_processor():
    """Test TransformationProcessor functionality."""
    print("🧪 Testing TransformationProcessor...")
    
    # Define transformations
    transformations = [
        {
            "type": "field_mapping",
            "mapping": {"old_name": "new_name"}
        },
        {
            "type": "field_calculation",
            "calculations": [
                {"target": "full_name", "expression": "first_name + ' ' + last_name"}
            ]
        },
        {
            "type": "field_formatting",
            "formatting": {
                "age": {"type": "number", "precision": 0},
                "salary": {"type": "number", "precision": 2}
            }
        },
        {
            "type": "field_filtering",
            "filter": {"exclude": ["internal_id"]}
        }
    ]
    
    processor = TransformationProcessor("test_transformer", transformations)
    
    # Test record
    record = DataRecord(
        id="transform-1",
        data={
            "old_name": "Alice",
            "first_name": "Alice",
            "last_name": "Smith",
            "age": 30.7,
            "salary": 75000.123,
            "internal_id": "secret123"
        }
    )
    
    result = await processor.process(record)
    
    assert result is not None
    assert "new_name" in result.data
    assert "old_name" not in result.data  # Should be mapped
    assert result.data["new_name"] == "Alice"
    assert result.data["full_name"] == "Alice Smith"  # Calculated field
    assert result.data["age"] == 31  # Rounded
    assert result.data["salary"] == 75000.12  # Precision 2
    assert "internal_id" not in result.data  # Should be filtered out
    
    print("✅ TransformationProcessor tests passed")
    return True


async def test_enrichment_processor():
    """Test EnrichmentProcessor functionality."""
    print("🧪 Testing EnrichmentProcessor...")
    
    # Define enrichment sources
    enrichment_sources = [
        {
            "type": "lookup_table",
            "lookup_field": "city",
            "target_fields": ["country", "timezone"],
            "data": {
                "New York": {"country": "USA", "timezone": "EST"},
                "London": {"country": "UK", "timezone": "GMT"}
            }
        },
        {
            "type": "api_call",
            "endpoint": "https://api.example.com/enrich",
            "lookup_field": "user_id"
        },
        {
            "type": "calculation",
            "calculations": [
                {"target": "age_group", "formula": "'adult' if age >= 18 else 'minor'"}
            ]
        }
    ]
    
    processor = EnrichmentProcessor("test_enricher", enrichment_sources)
    
    # Test record
    record = DataRecord(
        id="enrich-1",
        data={"name": "Alice", "city": "New York", "age": 25, "user_id": "user123"}
    )
    
    result = await processor.process(record)
    
    assert result is not None
    assert result.data["enriched_country"] == "USA"
    assert result.data["enriched_timezone"] == "EST"
    assert result.data["api_enriched"] == True  # From simulated API call
    assert result.data["age_group"] == "adult"  # From calculation
    
    print("✅ EnrichmentProcessor tests passed")
    return True


async def test_aggregation_processor():
    """Test AggregationProcessor functionality."""
    print("🧪 Testing AggregationProcessor...")
    
    # Define aggregation configuration
    aggregation_config = {
        "window_size": 3,
        "group_by": ["category"],
        "functions": {
            "amount": ["sum", "avg", "count"],
            "quantity": ["max", "min"]
        }
    }
    
    processor = AggregationProcessor("test_aggregator", aggregation_config)
    
    # Test records
    records = [
        DataRecord(id="agg-1", data={"category": "A", "amount": 100, "quantity": 5}),
        DataRecord(id="agg-2", data={"category": "A", "amount": 200, "quantity": 3}),
        DataRecord(id="agg-3", data={"category": "A", "amount": 150, "quantity": 7}),
        DataRecord(id="agg-4", data={"category": "A", "amount": 300, "quantity": 2}),  # Should trigger new aggregation
    ]
    
    results = []
    for record in records:
        result = await processor.process(record)
        if result:
            results.append(result)
    
    # Should have one aggregated result after processing 3 records
    assert len(results) == 1
    
    aggregated = results[0]
    assert aggregated.data["category"] == "A"
    assert aggregated.data["amount_sum"] == 450  # 100 + 200 + 150
    assert aggregated.data["amount_avg"] == 150  # 450 / 3
    assert aggregated.data["amount_count"] == 3
    assert aggregated.data["quantity_max"] == 7
    assert aggregated.data["quantity_min"] == 3
    
    print("✅ AggregationProcessor tests passed")
    return True


async def test_data_pipeline():
    """Test DataPipeline functionality."""
    print("🧪 Testing DataPipeline...")
    
    # Create processors
    validation_schema = {
        "required": ["name", "age"],
        "types": {"name": str, "age": int}
    }
    
    transformations = [
        {
            "type": "field_calculation",
            "calculations": [
                {"target": "age_category", "expression": "'adult' if age >= 18 else 'minor'"}
            ]
        }
    ]
    
    processors = [
        ValidationProcessor("validator", validation_schema),
        TransformationProcessor("transformer", transformations)
    ]
    
    # Create pipeline
    pipeline = DataPipeline("test_pipeline", processors)
    
    # Test records
    test_records = [
        DataRecord(id="pipe-1", data={"name": "Alice", "age": 25}),
        DataRecord(id="pipe-2", data={"name": "Bob", "age": 16}),
        DataRecord(id="pipe-3", data={"name": "Charlie"}),  # Missing age - should fail validation
    ]
    
    # Process records
    results = await pipeline.process_batch(test_records)
    
    # Check results
    assert len(results) == 3
    assert results[0] is not None  # Alice - valid
    assert results[1] is not None  # Bob - valid
    assert results[2] is None      # Charlie - invalid
    
    # Check transformed data
    assert results[0].data["age_category"] == "adult"
    assert results[1].data["age_category"] == "minor"
    
    # Check pipeline status
    status = pipeline.get_status()
    assert status["name"] == "test_pipeline"
    assert status["status"] == "running"
    assert status["metrics"]["records_processed"] == 2
    assert status["metrics"]["records_failed"] == 1
    
    print("✅ DataPipeline tests passed")
    return True


async def test_streaming_pipeline():
    """Test StreamingPipeline functionality."""
    print("🧪 Testing StreamingPipeline...")
    
    # Create simple processor
    validation_schema = {"required": ["id"], "types": {"id": str}}
    processors = [ValidationProcessor("stream_validator", validation_schema)]
    
    # Create streaming pipeline
    config = {"buffer_size": 100, "batch_size": 2, "batch_timeout": 0.1}
    pipeline = StreamingPipeline("test_stream", processors, config)
    
    # Start streaming
    await pipeline.start_streaming()
    
    # Add records to stream
    test_records = [
        DataRecord(id="stream-1", data={"id": "1", "value": "A"}),
        DataRecord(id="stream-2", data={"id": "2", "value": "B"}),
        DataRecord(id="stream-3", data={"id": "3", "value": "C"}),
    ]
    
    for record in test_records:
        await pipeline.add_record(record)
    
    # Wait for processing
    await asyncio.sleep(0.5)
    
    # Stop streaming
    await pipeline.stop_streaming()
    
    # Check metrics
    metrics = pipeline.get_metrics()
    assert metrics.records_processed > 0
    
    print("✅ StreamingPipeline tests passed")
    return True


async def test_pipeline_orchestrator():
    """Test PipelineOrchestrator functionality."""
    print("🧪 Testing PipelineOrchestrator...")
    
    orchestrator = PipelineOrchestrator()
    
    # Create test pipelines
    schema1 = {"required": ["id"], "types": {"id": str}}
    schema2 = {"required": ["processed_id"], "types": {"processed_id": str}}
    
    pipeline1 = DataPipeline("pipeline_a", [ValidationProcessor("val1", schema1)])
    pipeline2 = DataPipeline("pipeline_b", [ValidationProcessor("val2", schema2)])
    
    # Register pipelines with dependencies
    orchestrator.register_pipeline(pipeline1)
    orchestrator.register_pipeline(pipeline2, dependencies=["pipeline_a"])
    
    # Test dependency resolution
    execution_order = orchestrator._topological_sort()
    assert execution_order.index("pipeline_a") < execution_order.index("pipeline_b")
    
    # Test pipeline execution
    input_data = {
        "pipeline_a": [DataRecord(id="orch-1", data={"id": "test1"})]
    }
    
    results = await orchestrator.execute_all_pipelines(input_data)
    
    assert "pipeline_a" in results
    assert len(results["pipeline_a"]) == 1
    assert results["pipeline_a"][0] is not None
    
    # Test orchestrator status
    status = orchestrator.get_orchestrator_status()
    assert status["registered_pipelines"] == 2
    assert "pipeline_a" in status["pipeline_statuses"]
    assert "pipeline_b" in status["pipeline_statuses"]
    
    print("✅ PipelineOrchestrator tests passed")
    return True


def test_pipeline_status():
    """Test pipeline status reporting."""
    print("🧪 Testing pipeline status...")
    
    status = get_pipeline_status()
    
    assert "timestamp" in status
    assert "system_info" in status
    assert "available_processors" in status["system_info"]
    assert "supported_formats" in status["system_info"]
    assert "processing_modes" in status["system_info"]
    
    # Check that expected processors are listed
    processors = status["system_info"]["available_processors"]
    assert "ValidationProcessor" in processors
    assert "TransformationProcessor" in processors
    assert "EnrichmentProcessor" in processors
    assert "AggregationProcessor" in processors
    
    print("✅ Pipeline status tests passed")
    return True


async def test_real_world_pipeline_scenario():
    """Test a comprehensive real-world pipeline scenario."""
    print("🧪 Testing real-world pipeline scenario...")
    
    # Scenario: Token analysis data pipeline
    
    # Stage 1: Validation
    validation_schema = {
        "required": ["token_address", "timestamp", "price"],
        "types": {
            "token_address": str,
            "timestamp": str,
            "price": float
        },
        "constraints": {
            "price": {"min": 0}
        }
    }
    
    # Stage 2: Transformation
    transformations = [
        {
            "type": "field_formatting",
            "formatting": {
                "price": {"type": "number", "precision": 8}
            }
        },
        {
            "type": "field_calculation",
            "calculations": [
                {"target": "price_category", "expression": "'high' if price > 1.0 else 'low'"}
            ]
        }
    ]
    
    # Stage 3: Enrichment
    enrichment_sources = [
        {
            "type": "lookup_table",
            "lookup_field": "token_address",
            "target_fields": ["symbol", "name"],
            "data": {
                "0x123": {"symbol": "TOKEN1", "name": "Test Token 1"},
                "0x456": {"symbol": "TOKEN2", "name": "Test Token 2"}
            }
        },
        {
            "type": "calculation",
            "calculations": [
                {"target": "market_cap_estimate", "formula": "price * 1000000"}  # Simplified
            ]
        }
    ]
    
    # Stage 4: Aggregation
    aggregation_config = {
        "window_size": 2,
        "group_by": ["symbol"],
        "functions": {
            "price": ["avg", "min", "max"],
            "market_cap_estimate": ["sum"]
        }
    }
    
    # Create processors
    processors = [
        ValidationProcessor("token_validator", validation_schema),
        TransformationProcessor("token_transformer", transformations),
        EnrichmentProcessor("token_enricher", enrichment_sources),
        AggregationProcessor("token_aggregator", aggregation_config)
    ]
    
    # Create pipeline
    pipeline = DataPipeline("token_analysis_pipeline", processors)
    
    # Test data
    test_records = [
        DataRecord(id="token-1", data={
            "token_address": "0x123",
            "timestamp": "2025-01-01T00:00:00Z",
            "price": 1.23456789
        }),
        DataRecord(id="token-2", data={
            "token_address": "0x123",
            "timestamp": "2025-01-01T00:01:00Z",
            "price": 1.34567890
        }),
        DataRecord(id="token-3", data={
            "token_address": "0x456",
            "timestamp": "2025-01-01T00:00:00Z",
            "price": 0.56789012
        }),
        DataRecord(id="token-4", data={
            "token_address": "0x456",
            "timestamp": "2025-01-01T00:01:00Z",
            "price": 0.67890123
        }),
        DataRecord(id="invalid", data={
            "token_address": "0x789",
            "timestamp": "2025-01-01T00:00:00Z"
            # Missing price - should fail validation
        })
    ]
    
    # Process through pipeline
    results = await pipeline.process_batch(test_records)
    
    # Filter out None results and aggregated results
    processed_results = [r for r in results if r is not None]
    aggregated_results = [r for r in processed_results if r.source and "aggregation" in r.source]
    
    # Should have some aggregated results
    assert len(aggregated_results) > 0
    
    # Check aggregated data structure
    for agg_result in aggregated_results:
        assert "symbol" in agg_result.data
        assert "price_avg" in agg_result.data
        assert "market_cap_estimate_sum" in agg_result.data
        assert agg_result.metadata["record_count"] == 2  # Window size
    
    # Check pipeline metrics
    status = pipeline.get_status()
    assert status["metrics"]["records_processed"] > 0
    assert status["metrics"]["records_failed"] > 0  # Invalid record should fail
    
    print("✅ Real-world pipeline scenario passed")
    return True


async def main():
    """Run all advanced pipeline tests."""
    print("🚀 Starting Advanced Data Pipeline Architecture Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Run all tests
        tests = [
            ("data_record", test_data_record),
            ("pipeline_metrics", test_pipeline_metrics),
            ("validation_processor", test_validation_processor),
            ("transformation_processor", test_transformation_processor),
            ("enrichment_processor", test_enrichment_processor),
            ("aggregation_processor", test_aggregation_processor),
            ("data_pipeline", test_data_pipeline),
            ("streaming_pipeline", test_streaming_pipeline),
            ("pipeline_orchestrator", test_pipeline_orchestrator),
            ("pipeline_status", test_pipeline_status),
            ("real_world_scenario", test_real_world_pipeline_scenario),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 Advanced Data Pipeline Architecture Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate >= 0.9:  # 90% threshold for pipeline tests
            print("🏆 Pipeline tests meet high standards - System ready for production!")
            return 0
        else:
            print("⚠️ Some pipeline tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
