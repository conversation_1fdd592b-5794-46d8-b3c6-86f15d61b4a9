#!/usr/bin/env python3
"""
Test script for enhanced logging with 2025 best practices.
Validates structured logging, correlation IDs, and context tracking.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.logging_config import (
    setup_logging, 
    get_logger, 
    CorrelationContext, 
    RequestContext,
    set_correlation_id,
    get_correlation_id
)


async def test_basic_logging():
    """Test basic structured logging functionality."""
    print("\n🧪 Testing Basic Structured Logging...")
    
    # Setup logging
    setup_logging()
    logger = get_logger("test.basic")
    
    # Test different log levels
    logger.debug("Debug message with context", component="test", action="debug")
    logger.info("Info message with metrics", response_time=0.123, status="success")
    logger.warning("Warning message", risk_level="medium", token="TEST")
    logger.error("Error message", error_code="E001", details="Test error")
    
    print("✅ Basic logging test completed")


async def test_correlation_tracking():
    """Test correlation ID tracking."""
    print("\n🧪 Testing Correlation ID Tracking...")
    
    logger = get_logger("test.correlation")
    
    # Test manual correlation ID setting
    set_correlation_id("test-correlation-123")
    logger.info("Message with manual correlation ID", action="manual_test")
    
    current_id = get_correlation_id()
    print(f"Current correlation ID: {current_id}")
    
    # Test context manager
    with CorrelationContext("context-test-456") as corr_id:
        logger.info("Message within correlation context", action="context_test")
        print(f"Context correlation ID: {corr_id}")
    
    # Test nested contexts
    with CorrelationContext("outer-context") as outer_id:
        logger.info("Outer context message", level="outer")
        
        with CorrelationContext("inner-context") as inner_id:
            logger.info("Inner context message", level="inner")
            print(f"Outer ID: {outer_id}, Inner ID: {inner_id}")
    
    print("✅ Correlation tracking test completed")


async def test_request_context():
    """Test request context tracking."""
    print("\n🧪 Testing Request Context Tracking...")
    
    logger = get_logger("test.request")
    
    # Test request context
    with RequestContext("req-123", "user-456") as ctx:
        logger.info("Processing user request", action="login", ip="127.0.0.1")
        logger.info("Request completed", duration=0.234, status="success")
    
    # Test without user ID
    with RequestContext("req-789") as ctx:
        logger.info("Anonymous request", action="view", resource="token_list")
    
    print("✅ Request context test completed")


async def test_async_logging():
    """Test asynchronous logging performance."""
    print("\n🧪 Testing Async Logging Performance...")
    
    logger = get_logger("test.async")
    
    # Test concurrent logging
    async def log_worker(worker_id: int):
        with CorrelationContext(f"worker-{worker_id}"):
            for i in range(5):
                await asyncio.sleep(0.01)  # Simulate async work
                logger.info(
                    "Async log message",
                    worker_id=worker_id,
                    message_id=i,
                    action="async_test"
                )
    
    # Run multiple workers concurrently
    tasks = [log_worker(i) for i in range(3)]
    await asyncio.gather(*tasks)
    
    print("✅ Async logging test completed")


async def test_error_handling():
    """Test error logging and exception handling."""
    print("\n🧪 Testing Error Handling...")
    
    logger = get_logger("test.errors")
    
    try:
        # Simulate an error
        raise ValueError("Test error for logging")
    except Exception as e:
        logger.exception(
            "Caught test exception",
            error_type=type(e).__name__,
            error_message=str(e),
            component="test_suite"
        )
    
    # Test structured error logging
    logger.error(
        "Structured error log",
        error_code="TEST_001",
        error_category="validation",
        user_action="token_analysis",
        recovery_suggestion="Check input parameters"
    )
    
    print("✅ Error handling test completed")


async def test_sensitive_data_filtering():
    """Test sensitive data filtering."""
    print("\n🧪 Testing Sensitive Data Filtering...")
    
    logger = get_logger("test.security")
    
    # Test with sensitive data (should be filtered)
    logger.info(
        "User authentication",
        username="testuser",
        password="secret123",  # Should be redacted
        api_key="sk-1234567890",  # Should be redacted
        token="bearer-token-xyz",  # Should be redacted
        user_id="user-123",  # Should remain
        action="login"
    )
    
    # Test nested sensitive data
    logger.info(
        "API request",
        request_data={
            "user": "testuser",
            "credentials": {
                "password": "secret",  # Should be redacted
                "api_key": "key123"    # Should be redacted
            },
            "metadata": {
                "ip": "127.0.0.1",
                "user_agent": "test-agent"
            }
        }
    )
    
    print("✅ Sensitive data filtering test completed")


async def main():
    """Run all logging tests."""
    print("🚀 Starting Enhanced Logging Tests (2025 Best Practices)")
    print("=" * 60)
    
    try:
        await test_basic_logging()
        await test_correlation_tracking()
        await test_request_context()
        await test_async_logging()
        await test_error_handling()
        await test_sensitive_data_filtering()
        
        print("\n" + "=" * 60)
        print("🎉 All logging tests completed successfully!")
        print("📊 Check the logs directory for JSON formatted logs")
        print("🔍 Console output shows structured logging in action")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
