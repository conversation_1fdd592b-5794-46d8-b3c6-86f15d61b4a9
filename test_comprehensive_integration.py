#!/usr/bin/env python3
"""
Comprehensive Integration Tests for Crypto Analytics Platform
Tests all API endpoints, reliability benchmarking, and end-to-end workflows.
"""

import asyncio
import time
import sys
import os
from datetime import datetime, timezone
from typing import Dict, List, Any
import structlog

# Add src to path
sys.path.insert(0, 'src')

from src.core.database import DatabaseManager
from src.core.cache import CacheManager
from src.integrations.metrics import MetricsCollector
from src.agents.coordinator import AgentCoordinator, TaskPriority
from src.agents.discovery import DiscoveryAgent
from src.core.http_manager import http_manager

logger = structlog.get_logger(__name__)


class ComprehensiveIntegrationTester:
    """Comprehensive integration testing suite for the crypto analytics platform."""
    
    def __init__(self):
        self.db_manager = None
        self.cache_manager = None
        self.metrics_collector = None
        self.coordinator = None
        self.discovery_agent = None
        
        # Test results
        self.test_results = {
            "api_endpoints": {},
            "reliability_metrics": {},
            "performance_benchmarks": {},
            "error_rates": {},
            "overall_score": 0
        }
    
    async def initialize(self):
        """Initialize all system components for testing."""
        logger.info("🚀 Initializing Comprehensive Integration Test Suite")
        
        # Initialize core components
        self.db_manager = DatabaseManager()
        await self.db_manager.initialize()
        
        self.cache_manager = CacheManager()
        await self.cache_manager.initialize()
        
        self.metrics_collector = MetricsCollector()
        
        self.coordinator = AgentCoordinator(
            self.db_manager, 
            self.cache_manager, 
            self.metrics_collector
        )
        await self.coordinator.initialize()
        
        self.discovery_agent = DiscoveryAgent(
            self.db_manager,
            self.cache_manager, 
            self.metrics_collector,
            self.coordinator
        )
        await self.discovery_agent.initialize()
        
        logger.info("✅ All components initialized successfully")
    
    async def test_api_endpoints(self) -> Dict[str, Any]:
        """Test all API endpoints for availability and response quality."""
        logger.info("🔗 Testing API Endpoints")
        
        endpoints = {
            "dexscreener": "https://api.dexscreener.com/latest/dex/search?q=ETH",
            "coingecko": "https://api.coingecko.com/api/v3/search/trending",
            "birdeye": "https://public-api.birdeye.so/defi/tokenlist",
            "defillama": "https://api.llama.fi/protocols"
        }
        
        results = {}
        
        for name, url in endpoints.items():
            try:
                session = await http_manager.get_session(name)
                semaphore = await http_manager.acquire_semaphore(name)
                
                start_time = time.time()
                async with semaphore:
                    async with session.get(url) as response:
                        response_time = time.time() - start_time
                        
                        results[name] = {
                            "status": response.status,
                            "response_time": response_time,
                            "available": response.status == 200,
                            "data_quality": "unknown"
                        }
                        
                        if response.status == 200:
                            try:
                                data = await response.json()
                                data_size = len(str(data))
                                results[name]["data_quality"] = "good" if data_size > 100 else "poor"
                                results[name]["data_size"] = data_size
                            except Exception:
                                results[name]["data_quality"] = "invalid"
                        
                        http_manager.record_request_result(name, response.status == 200, response_time)
                        
            except Exception as e:
                results[name] = {
                    "status": 0,
                    "response_time": 0,
                    "available": False,
                    "error": str(e)
                }
        
        self.test_results["api_endpoints"] = results
        
        # Calculate API availability score
        available_count = sum(1 for r in results.values() if r.get("available", False))
        api_score = (available_count / len(endpoints)) * 100
        
        logger.info(f"📊 API Endpoints Test: {available_count}/{len(endpoints)} available ({api_score:.1f}%)")
        return results
    
    async def test_discovery_reliability(self) -> Dict[str, Any]:
        """Test token discovery reliability across multiple runs."""
        logger.info("🔍 Testing Discovery Reliability")
        
        test_runs = 5
        results = []
        
        for run in range(test_runs):
            try:
                start_time = time.time()
                
                # Test discovery with different parameters
                discovery_result = await self.discovery_agent.discover_tokens(
                    sources=["dexscreener"],
                    limit=3,
                    min_age_hours=1
                )
                
                duration = time.time() - start_time
                tokens_found = len(discovery_result.get("tokens", []))
                
                results.append({
                    "run": run + 1,
                    "tokens_found": tokens_found,
                    "duration": duration,
                    "success": tokens_found > 0,
                    "errors": len(discovery_result.get("errors", []))
                })
                
                logger.info(f"Discovery run {run + 1}: {tokens_found} tokens in {duration:.2f}s")
                
            except Exception as e:
                results.append({
                    "run": run + 1,
                    "tokens_found": 0,
                    "duration": 0,
                    "success": False,
                    "error": str(e)
                })
        
        # Calculate reliability metrics
        successful_runs = sum(1 for r in results if r["success"])
        avg_tokens = sum(r["tokens_found"] for r in results) / len(results)
        avg_duration = sum(r["duration"] for r in results) / len(results)
        reliability_score = (successful_runs / test_runs) * 100
        
        reliability_metrics = {
            "total_runs": test_runs,
            "successful_runs": successful_runs,
            "reliability_score": reliability_score,
            "avg_tokens_found": avg_tokens,
            "avg_duration": avg_duration,
            "results": results
        }
        
        self.test_results["reliability_metrics"] = reliability_metrics
        
        logger.info(f"🎯 Discovery Reliability: {reliability_score:.1f}% ({successful_runs}/{test_runs} successful)")
        return reliability_metrics
    
    async def test_performance_benchmarks(self) -> Dict[str, Any]:
        """Test system performance under various load conditions."""
        logger.info("⚡ Testing Performance Benchmarks")
        
        benchmarks = {}
        
        # Test 1: Concurrent discovery operations
        logger.info("Testing concurrent discovery operations...")
        start_time = time.time()
        
        concurrent_tasks = []
        for i in range(3):  # 3 concurrent operations
            task = self.discovery_agent.discover_tokens(
                sources=["dexscreener"],
                limit=2,
                min_age_hours=1
            )
            concurrent_tasks.append(task)
        
        concurrent_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        concurrent_duration = time.time() - start_time
        
        successful_concurrent = sum(1 for r in concurrent_results if not isinstance(r, Exception))
        
        benchmarks["concurrent_discovery"] = {
            "operations": len(concurrent_tasks),
            "successful": successful_concurrent,
            "duration": concurrent_duration,
            "ops_per_second": len(concurrent_tasks) / concurrent_duration
        }
        
        # Test 2: Cache performance
        logger.info("Testing cache performance...")
        cache_start = time.time()
        
        # Write operations
        for i in range(10):
            await self.cache_manager.set(f"test_key_{i}", {"data": f"value_{i}"}, "tokens")
        
        # Read operations
        cache_hits = 0
        for i in range(10):
            result = await self.cache_manager.get(f"test_key_{i}", "tokens")
            if result:
                cache_hits += 1
        
        cache_duration = time.time() - cache_start
        
        benchmarks["cache_performance"] = {
            "operations": 20,  # 10 writes + 10 reads
            "cache_hits": cache_hits,
            "duration": cache_duration,
            "ops_per_second": 20 / cache_duration
        }
        
        # Test 3: Agent coordination
        logger.info("Testing agent coordination...")
        coord_start = time.time()
        
        # Submit tasks with different priorities
        task_ids = []
        for priority in [TaskPriority.HIGH, TaskPriority.MEDIUM, TaskPriority.LOW]:
            task_id = await self.coordinator.submit_task(
                "discovery", "health_check", priority
            )
            task_ids.append(task_id)
        
        # Wait for task processing
        await asyncio.sleep(2)
        
        coord_duration = time.time() - coord_start
        
        benchmarks["agent_coordination"] = {
            "tasks_submitted": len(task_ids),
            "duration": coord_duration,
            "queue_size": await self.coordinator.task_queue.size()
        }
        
        self.test_results["performance_benchmarks"] = benchmarks
        
        logger.info(f"⚡ Performance Benchmarks completed in {coord_duration:.2f}s")
        return benchmarks
    
    async def calculate_overall_score(self) -> float:
        """Calculate overall system reliability score."""
        scores = []
        
        # API availability score (30% weight)
        api_results = self.test_results.get("api_endpoints", {})
        available_apis = sum(1 for r in api_results.values() if r.get("available", False))
        api_score = (available_apis / max(len(api_results), 1)) * 100
        scores.append(api_score * 0.3)
        
        # Discovery reliability score (40% weight)
        reliability = self.test_results.get("reliability_metrics", {})
        reliability_score = reliability.get("reliability_score", 0)
        scores.append(reliability_score * 0.4)
        
        # Performance score (30% weight)
        benchmarks = self.test_results.get("performance_benchmarks", {})
        concurrent = benchmarks.get("concurrent_discovery", {})
        concurrent_success_rate = (concurrent.get("successful", 0) / max(concurrent.get("operations", 1), 1)) * 100
        scores.append(concurrent_success_rate * 0.3)
        
        overall_score = sum(scores)
        self.test_results["overall_score"] = overall_score
        
        return overall_score
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all comprehensive integration tests."""
        logger.info("🧪 Starting Comprehensive Integration Tests")
        
        start_time = time.time()
        
        # Run all test suites
        await self.test_api_endpoints()
        await self.test_discovery_reliability()
        await self.test_performance_benchmarks()
        
        # Calculate overall score
        overall_score = await self.calculate_overall_score()
        
        total_duration = time.time() - start_time
        
        # Generate summary
        logger.info("=" * 80)
        logger.info("🏆 COMPREHENSIVE INTEGRATION TEST RESULTS")
        logger.info("=" * 80)
        
        api_results = self.test_results["api_endpoints"]
        available_apis = sum(1 for r in api_results.values() if r.get("available", False))
        logger.info(f"📡 API Endpoints: {available_apis}/{len(api_results)} available")
        
        reliability = self.test_results["reliability_metrics"]
        logger.info(f"🔍 Discovery Reliability: {reliability.get('reliability_score', 0):.1f}%")
        
        logger.info(f"⚡ Performance: {total_duration:.2f}s total test time")
        logger.info(f"🎯 Overall Score: {overall_score:.1f}%")
        
        # Determine system status
        if overall_score >= 85:
            status = "🟢 EXCELLENT - Production Ready"
        elif overall_score >= 70:
            status = "🟡 GOOD - Minor Issues"
        elif overall_score >= 50:
            status = "🟠 FAIR - Needs Improvement"
        else:
            status = "🔴 POOR - Major Issues"
        
        logger.info(f"📊 System Status: {status}")
        logger.info("=" * 80)
        
        return self.test_results
    
    async def shutdown(self):
        """Cleanup all resources."""
        if self.discovery_agent:
            await self.discovery_agent.shutdown()
        if self.coordinator:
            await self.coordinator.shutdown()
        if self.cache_manager:
            await self.cache_manager.shutdown()
        
        logger.info("🧹 Integration test cleanup completed")


async def main():
    """Main test execution function."""
    tester = ComprehensiveIntegrationTester()
    
    try:
        await tester.initialize()
        results = await tester.run_comprehensive_tests()
        return results
    finally:
        await tester.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
