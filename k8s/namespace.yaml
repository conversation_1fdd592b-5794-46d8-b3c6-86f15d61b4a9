apiVersion: v1
kind: Namespace
metadata:
  name: crypto-analytics
  labels:
    name: crypto-analytics
    app: token-analysis-platform
    environment: production
  annotations:
    description: "Crypto Analytics Platform - Token Analysis System"
    contact: "<EMAIL>"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: crypto-analytics-quota
  namespace: crypto-analytics
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: crypto-analytics-limits
  namespace: crypto-analytics
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - max:
      cpu: "2"
      memory: "4Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
    type: Container
