"""
Unit Tests for Error Handling System - Production Quality
Comprehensive unit tests for circuit breakers, retries, and fault tolerance
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, patch
from datetime import datetime, timedelta, timezone
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.error_handling import (
    CircuitBreaker, CircuitBreakerConfig, CircuitBreakerState, CircuitBreakerError,
    FaultTolerantExecutor, RetryConfig, fault_tolerant
)


class TestCircuitBreaker:
    """Test suite for CircuitBreaker functionality."""
    
    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initialization."""
        config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=60)
        cb = CircuitBreaker("test_service", config)
        
        assert cb.name == "test_service"
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.failure_count == 0
        assert cb.can_execute() == True
    
    def test_circuit_breaker_failure_threshold(self):
        """Test circuit breaker trips after failure threshold."""
        config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=60)
        cb = CircuitBreaker("test_service", config)
        
        # First failure
        cb.record_failure()
        assert cb.state == CircuitBreakerState.CLOSED
        assert cb.can_execute() == True
        
        # Second failure - should trip
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        assert cb.can_execute() == False
    
    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery after timeout."""
        config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=1, success_threshold=1)
        cb = CircuitBreaker("test_service", config)
        
        # Trip circuit breaker
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        assert cb.can_execute() == False
        
        # Simulate time passage
        cb.next_attempt_time = datetime.now(timezone.utc) - timedelta(seconds=1)
        
        # Should transition to HALF_OPEN
        assert cb.can_execute() == True
        assert cb.state == CircuitBreakerState.HALF_OPEN
        
        # Record success to reset
        cb.record_success()
        assert cb.state == CircuitBreakerState.CLOSED
    
    def test_circuit_breaker_half_open_failure(self):
        """Test circuit breaker trips again from HALF_OPEN on failure."""
        config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=1)
        cb = CircuitBreaker("test_service", config)
        
        # Trip and recover to HALF_OPEN
        cb.record_failure()
        cb.next_attempt_time = datetime.now(timezone.utc) - timedelta(seconds=1)
        cb.can_execute()  # Transitions to HALF_OPEN
        
        assert cb.state == CircuitBreakerState.HALF_OPEN
        
        # Failure in HALF_OPEN should trip again
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN


class TestFaultTolerantExecutor:
    """Test suite for FaultTolerantExecutor functionality."""
    
    @pytest.fixture
    def executor(self):
        """Create executor for testing."""
        return FaultTolerantExecutor()
    
    @pytest.mark.asyncio
    async def test_successful_execution(self, executor):
        """Test successful function execution."""
        
        async def success_function():
            return "success"
        
        result = await executor.execute_with_fault_tolerance(
            success_function, "test_service"
        )
        
        assert result == "success"
        assert executor.stats["successful_executions"] == 1
        assert executor.stats["failed_executions"] == 0
    
    @pytest.mark.asyncio
    async def test_retry_on_failure(self, executor):
        """Test retry mechanism on failures."""
        
        call_count = 0
        
        async def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ConnectionError(f"Attempt {call_count} failed")
            return f"Success on attempt {call_count}"
        
        retry_config = RetryConfig(max_attempts=3, min_wait=0.01, max_wait=0.1)
        
        result = await executor.execute_with_fault_tolerance(
            failing_function, "test_service", retry_config
        )
        
        assert result == "Success on attempt 3"
        assert call_count == 3
        assert executor.stats["retries_attempted"] == 2
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_integration(self, executor):
        """Test circuit breaker integration."""
        
        async def always_fail():
            raise ConnectionError("Always fails")
        
        circuit_config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=1)
        retry_config = RetryConfig(max_attempts=1)
        
        # First two calls should fail and trip circuit breaker
        with pytest.raises(ConnectionError):
            await executor.execute_with_fault_tolerance(
                always_fail, "test_service", retry_config, circuit_config
            )
        
        with pytest.raises(ConnectionError):
            await executor.execute_with_fault_tolerance(
                always_fail, "test_service", retry_config, circuit_config
            )
        
        # Third call should be blocked by circuit breaker
        with pytest.raises(CircuitBreakerError):
            await executor.execute_with_fault_tolerance(
                always_fail, "test_service", retry_config, circuit_config
            )
    
    @pytest.mark.asyncio
    async def test_retryable_error_detection(self, executor):
        """Test retryable error detection."""
        
        import aiohttp
        
        # Retryable errors
        retryable_errors = [
            ConnectionError("Connection failed"),
            aiohttp.ServerTimeoutError("Timeout"),
            aiohttp.ClientResponseError(None, None, status=500, message="Server Error")
        ]
        
        # Non-retryable errors
        non_retryable_errors = [
            aiohttp.ClientResponseError(None, None, status=401, message="Unauthorized"),
            ValueError("Invalid input"),
            KeyError("Missing key")
        ]
        
        for error in retryable_errors:
            assert executor._is_retryable_error(error) == True
        
        for error in non_retryable_errors:
            assert executor._is_retryable_error(error) == False
    
    def test_executor_statistics(self, executor):
        """Test executor statistics tracking."""
        
        # Initial stats
        stats = executor.get_stats()
        assert stats["total_executions"] == 0
        assert stats["success_rate"] == 0
        
        # Update stats manually for testing
        executor.stats["total_executions"] = 10
        executor.stats["successful_executions"] = 8
        executor.stats["failed_executions"] = 2
        
        stats = executor.get_stats()
        assert stats["total_executions"] == 10
        assert stats["success_rate"] == 0.8
        assert stats["failure_rate"] == 0.2


class TestFaultTolerantDecorator:
    """Test suite for fault_tolerant decorator."""
    
    @pytest.mark.asyncio
    async def test_decorator_basic_functionality(self):
        """Test basic decorator functionality."""
        
        call_count = 0
        
        @fault_tolerant("test_decorator")
        async def decorated_function():
            nonlocal call_count
            call_count += 1
            return f"Call {call_count}"
        
        result = await decorated_function()
        assert "Call 1" in result
        assert call_count == 1
    
    @pytest.mark.asyncio
    async def test_decorator_with_retry(self):
        """Test decorator with retry configuration."""
        
        call_count = 0
        
        retry_config = RetryConfig(max_attempts=3, min_wait=0.01, max_wait=0.1)
        
        @fault_tolerant("test_decorator", retry_config)
        async def decorated_function():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise ConnectionError("First call fails")
            return f"Success on call {call_count}"
        
        result = await decorated_function()
        assert "Success on call 2" in result
        assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_decorator_with_circuit_breaker(self):
        """Test decorator with circuit breaker configuration."""
        
        circuit_config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=1)
        
        @fault_tolerant("test_decorator", circuit_config=circuit_config)
        async def always_fail():
            raise ConnectionError("Always fails")
        
        # First call should fail
        with pytest.raises(ConnectionError):
            await always_fail()
        
        # Second call should be blocked by circuit breaker
        with pytest.raises(CircuitBreakerError):
            await always_fail()


class TestErrorHandlingIntegration:
    """Test suite for error handling integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_concurrent_execution_with_circuit_breaker(self):
        """Test concurrent execution with circuit breaker."""
        
        executor = FaultTolerantExecutor()
        call_count = 0
        
        async def sometimes_fail():
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                raise ConnectionError(f"Fail {call_count}")
            return f"Success {call_count}"
        
        circuit_config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=1)
        retry_config = RetryConfig(max_attempts=1)
        
        # Launch concurrent tasks
        tasks = []
        for i in range(5):
            task = executor.execute_with_fault_tolerance(
                sometimes_fail, "concurrent_test", retry_config, circuit_config
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Should have mix of successes and failures
        assert len(results) == 5
        
        # Some should succeed, some should fail
        successes = [r for r in results if isinstance(r, str) and "Success" in r]
        failures = [r for r in results if isinstance(r, Exception)]
        
        assert len(successes) + len(failures) == 5
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self):
        """Test performance under concurrent load."""
        
        executor = FaultTolerantExecutor()
        
        async def fast_function():
            await asyncio.sleep(0.01)  # Simulate fast operation
            return "fast_result"
        
        start_time = time.time()
        
        # Execute 50 concurrent operations
        tasks = []
        for i in range(50):
            task = executor.execute_with_fault_tolerance(
                fast_function, f"load_test_{i}"
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Should complete all operations
        assert len(results) == 50
        assert all(r == "fast_result" for r in results)
        
        # Should complete in reasonable time (under 5 seconds)
        assert execution_time < 5.0
    
    @pytest.mark.asyncio
    async def test_error_handling_edge_cases(self):
        """Test error handling edge cases."""
        
        executor = FaultTolerantExecutor()
        
        # Test with None function
        with pytest.raises((TypeError, AttributeError)):
            await executor.execute_with_fault_tolerance(
                None, "test_service"
            )
        
        # Test with invalid retry config
        invalid_retry = RetryConfig(max_attempts=0)
        
        async def simple_function():
            return "result"
        
        # Should handle gracefully
        result = await executor.execute_with_fault_tolerance(
            simple_function, "test_service", invalid_retry
        )
        assert result == "result"


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
