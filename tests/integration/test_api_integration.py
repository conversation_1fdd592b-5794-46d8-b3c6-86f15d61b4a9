"""
Integration tests with mock HTTP servers for external API interactions.

Tests the complete flow from API calls through data processing
with realistic mock servers that simulate actual API behavior.
"""

import asyncio
import json
from typing import Dict, Any
import pytest
from aiohttp import web, ClientSession
from aiohttp.test_utils import AioHTTPTestCase, unittest_run_loop
import aioresponses

from src.utils.fetch_helpers import (
    fetch_coingecko_data,
    fetch_dexscreener_data,
    fetch_etherscan_data,
    fetch_fear_greed_index
)
from src.agents.market_data import MarketDataAgent


@pytest.mark.integration
class TestMockAPIServers:
    """Test with mock HTTP servers that simulate real API behavior."""
    
    @pytest.fixture
    async def mock_coingecko_server(self, aiohttp_client):
        """Create a mock CoinGecko server."""
        
        async def price_endpoint(request):
            """Mock price endpoint with realistic responses."""
            contract_addresses = request.query.get('contract_addresses', '')
            
            if not contract_addresses:
                return web.json_response(
                    {"error": "Missing contract_addresses parameter"},
                    status=400
                )
            
            # Simulate rate limiting
            if hasattr(request.app, '_request_count'):
                request.app._request_count += 1
                if request.app._request_count > 10:
                    return web.json_response(
                        {"error": "Rate limit exceeded"},
                        status=429
                    )
            else:
                request.app._request_count = 1
            
            # Mock response data
            mock_data = {}
            for address in contract_addresses.split(','):
                address = address.strip().lower()
                mock_data[address] = {
                    "usd": 1.25,
                    "usd_24h_change": 5.2,
                    "usd_7d_change": -2.1,
                    "usd_30d_change": 15.7
                }
            
            return web.json_response(mock_data)
        
        async def coins_list_endpoint(request):
            """Mock coins list endpoint."""
            return web.json_response([
                {
                    "id": "bitcoin",
                    "symbol": "btc",
                    "name": "Bitcoin",
                    "platforms": {
                        "ethereum": "******************************************"
                    }
                }
            ])
        
        app = web.Application()
        app.router.add_get('/api/v3/simple/token_price/ethereum', price_endpoint)
        app.router.add_get('/api/v3/coins/list', coins_list_endpoint)
        
        client = await aiohttp_client(app)
        return client
    
    @pytest.fixture
    async def mock_dexscreener_server(self, aiohttp_client):
        """Create a mock DexScreener server."""
        
        async def token_endpoint(request):
            """Mock token endpoint."""
            token_address = request.match_info.get('address', '')
            
            if not token_address:
                return web.json_response(
                    {"error": "Invalid token address"},
                    status=400
                )
            
            mock_data = {
                "pairs": [
                    {
                        "chainId": "ethereum",
                        "dexId": "uniswap",
                        "url": f"https://dexscreener.com/ethereum/{token_address}",
                        "pairAddress": "0x" + "1" * 40,
                        "baseToken": {
                            "address": token_address,
                            "name": "Test Token",
                            "symbol": "TEST"
                        },
                        "quoteToken": {
                            "address": "******************************************",
                            "name": "USD Coin",
                            "symbol": "USDC"
                        },
                        "priceNative": "1.25",
                        "priceUsd": "1.25",
                        "txns": {
                            "m5": {"buys": 5, "sells": 3},
                            "h1": {"buys": 45, "sells": 32},
                            "h6": {"buys": 234, "sells": 198},
                            "h24": {"buys": 1024, "sells": 876}
                        },
                        "volume": {
                            "h24": 125000,
                            "h6": 32000,
                            "h1": 8500,
                            "m5": 1200
                        },
                        "priceChange": {
                            "m5": 0.5,
                            "h1": 2.1,
                            "h6": 4.8,
                            "h24": 5.2
                        },
                        "liquidity": {
                            "usd": 850000,
                            "base": 680000,
                            "quote": 170000
                        },
                        "fdv": 1250000,
                        "marketCap": 1250000
                    }
                ]
            }
            
            return web.json_response(mock_data)
        
        app = web.Application()
        app.router.add_get('/latest/dex/tokens/{address}', token_endpoint)
        
        client = await aiohttp_client(app)
        return client
    
    @pytest.fixture
    async def mock_etherscan_server(self, aiohttp_client):
        """Create a mock Etherscan server."""
        
        async def api_endpoint(request):
            """Mock Etherscan API endpoint."""
            module = request.query.get('module')
            action = request.query.get('action')
            
            # Simulate rate limiting for free tier
            if hasattr(request.app, '_request_times'):
                import time
                current_time = time.time()
                request.app._request_times.append(current_time)
                
                # Keep only requests from last second
                request.app._request_times = [
                    t for t in request.app._request_times 
                    if current_time - t < 1.0
                ]
                
                # Check rate limit (5 requests per second)
                if len(request.app._request_times) > 5:
                    return web.json_response(
                        {
                            "status": "0",
                            "message": "NOTOK",
                            "result": "Max rate limit reached"
                        },
                        status=429
                    )
            else:
                request.app._request_times = []
            
            # Mock responses based on module and action
            if module == "contract" and action == "getsourcecode":
                return web.json_response({
                    "status": "1",
                    "message": "OK",
                    "result": [{
                        "SourceCode": "contract TestToken { ... }",
                        "ABI": "[{\"type\":\"function\",\"name\":\"transfer\"}]",
                        "ContractName": "TestToken",
                        "CompilerVersion": "v0.8.19+commit.7dd6d404",
                        "OptimizationUsed": "1",
                        "Runs": "200",
                        "ConstructorArguments": "",
                        "EVMVersion": "Default",
                        "Library": "",
                        "LicenseType": "MIT",
                        "Proxy": "0",
                        "Implementation": "",
                        "SwarmSource": ""
                    }]
                })
            
            elif module == "account" and action == "balance":
                return web.json_response({
                    "status": "1",
                    "message": "OK",
                    "result": "1000000000000000000"  # 1 ETH in wei
                })
            
            elif module == "stats" and action == "tokensupply":
                return web.json_response({
                    "status": "1",
                    "message": "OK",
                    "result": "1000000000000000000000000"  # 1M tokens
                })
            
            else:
                return web.json_response({
                    "status": "0",
                    "message": "NOTOK",
                    "result": "Invalid module/action combination"
                })
        
        app = web.Application()
        app.router.add_get('/api', api_endpoint)
        
        client = await aiohttp_client(app)
        return client
    
    @pytest.mark.asyncio
    async def test_coingecko_integration(self, mock_coingecko_server):
        """Test CoinGecko integration with mock server."""
        # Override the base URL to use mock server
        import src.utils.fetch_helpers
        original_fetch = src.utils.fetch_helpers.fetch_coingecko_data
        
        async def mock_fetch(endpoint, params=None, session=None):
            # Use mock server URL
            url = f"{mock_coingecko_server.make_url('')}api/v3/{endpoint.lstrip('/')}"
            
            if session is None:
                async with ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        data = await response.json()
                        return src.utils.fetch_helpers.APIResponse(
                            success=response.status == 200,
                            data=data,
                            status_code=response.status
                        )
            else:
                async with session.get(url, params=params) as response:
                    data = await response.json()
                    return src.utils.fetch_helpers.APIResponse(
                        success=response.status == 200,
                        data=data,
                        status_code=response.status
                    )
        
        # Temporarily replace the function
        src.utils.fetch_helpers.fetch_coingecko_data = mock_fetch
        
        try:
            result = await fetch_coingecko_data(
                "simple/token_price/ethereum",
                {"contract_addresses": "******************************************"}
            )
            
            assert result.success is True
            assert "******************************************" in result.data
            assert result.data["******************************************"]["usd"] == 1.25
        
        finally:
            # Restore original function
            src.utils.fetch_helpers.fetch_coingecko_data = original_fetch
    
    @pytest.mark.asyncio
    async def test_rate_limiting_behavior(self, mock_coingecko_server):
        """Test rate limiting behavior with mock server."""
        import src.utils.fetch_helpers
        
        # Make many requests to trigger rate limiting
        tasks = []
        for i in range(15):  # More than the mock server's limit of 10
            task = fetch_coingecko_data(
                "simple/token_price/ethereum",
                {"contract_addresses": f"0x{i:040x}"}
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Some requests should succeed, some should be rate limited
        successful = [r for r in results if not isinstance(r, Exception) and r.success]
        rate_limited = [r for r in results if not isinstance(r, Exception) and r.status_code == 429]
        
        assert len(successful) <= 10  # Mock server limit
        assert len(rate_limited) > 0   # Some should be rate limited
    
    @pytest.mark.asyncio
    async def test_market_data_agent_integration(
        self, 
        mock_coingecko_server,
        mock_dexscreener_server,
        db_manager,
        cache_manager
    ):
        """Test MarketDataAgent with mock servers."""
        # Create market data agent
        async with ClientSession() as session:
            agent = MarketDataAgent(
                db_manager=db_manager,
                cache_manager=cache_manager,
                session=session
            )
            
            # Override endpoints to use mock servers
            agent.endpoints = {
                'coingecko': str(mock_coingecko_server.make_url('api/v3')),
                'dexscreener': str(mock_dexscreener_server.make_url('latest'))
            }
            
            # Test getting market data
            result = await agent.get_market_data(
                token_address="******************************************",
                chain_id=1
            )
            
            assert result is not None
            assert "sources" in result
            # Should have data from both sources
            assert len(result["sources"]) >= 1


@pytest.mark.integration
class TestRealAPIIntegration:
    """Integration tests with real APIs (marked as external_api)."""
    
    @pytest.mark.external_api
    @pytest.mark.asyncio
    async def test_fear_greed_real_api(self):
        """Test Fear & Greed Index with real API."""
        result = await fetch_fear_greed_index(limit=1)
        
        assert result.success is True
        assert "data" in result.data
        assert len(result.data["data"]) == 1
        assert "value" in result.data["data"][0]
        assert "value_classification" in result.data["data"][0]
    
    @pytest.mark.external_api
    @pytest.mark.asyncio
    async def test_coingecko_real_api(self):
        """Test CoinGecko with real API (if API key available)."""
        import os
        
        if not os.getenv("COINGECKO_API_KEY"):
            pytest.skip("COINGECKO_API_KEY not available")
        
        # Test with Bitcoin (well-known token)
        result = await fetch_coingecko_data(
            "simple/price",
            {
                "ids": "bitcoin",
                "vs_currencies": "usd",
                "include_24hr_change": "true"
            }
        )
        
        assert result.success is True
        assert "bitcoin" in result.data
        assert "usd" in result.data["bitcoin"]
        assert result.data["bitcoin"]["usd"] > 0


@pytest.mark.integration
class TestErrorHandlingIntegration:
    """Integration tests for error handling scenarios."""
    
    @pytest.mark.asyncio
    async def test_network_timeout_handling(self):
        """Test handling of network timeouts."""
        with aioresponses() as m:
            # Simulate timeout
            m.get(
                "https://api.coingecko.com/api/v3/simple/price",
                exception=asyncio.TimeoutError("Request timeout")
            )
            
            result = await fetch_coingecko_data("simple/price")
            
            assert result.success is False
            assert "timeout" in result.error.lower()
    
    @pytest.mark.asyncio
    async def test_invalid_json_response(self):
        """Test handling of invalid JSON responses."""
        with aioresponses() as m:
            m.get(
                "https://api.coingecko.com/api/v3/simple/price",
                body="Invalid JSON response",
                content_type="text/html"
            )
            
            result = await fetch_coingecko_data("simple/price")
            
            assert result.success is False
            assert "json" in result.error.lower()
    
    @pytest.mark.asyncio
    async def test_http_error_codes(self):
        """Test handling of various HTTP error codes."""
        error_codes = [400, 401, 403, 404, 500, 502, 503]
        
        for status_code in error_codes:
            with aioresponses() as m:
                m.get(
                    "https://api.coingecko.com/api/v3/simple/price",
                    status=status_code,
                    payload={"error": f"HTTP {status_code} error"}
                )
                
                result = await fetch_coingecko_data("simple/price")
                
                assert result.success is False
                assert result.status_code == status_code
                assert str(status_code) in result.error
