"""
Data quality validation tests using Great Expectations.

Tests data quality at all system layers including:
- Raw API response validation
- Processed data validation  
- Database integrity checks
- Pipeline data flow validation
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List
import json
import os

# Import Great Expectations (install if needed)
try:
    from great_expectations import DataContext
    from great_expectations.core.batch import RuntimeBatchRequest
    from great_expectations.checkpoint import SimpleCheckpoint
    GE_AVAILABLE = True
except ImportError:
    GE_AVAILABLE = False

from src.utils.fetch_helpers import fetch_coingecko_data, fetch_defillama_data
from src.agents.market_data import MarketDataAgent


@pytest.mark.skipif(not GE_AVAILABLE, reason="Great Expectations not installed")
@pytest.mark.unit
class TestDataQualityValidation:
    """Test data quality using Great Expectations suites."""
    
    @pytest.fixture
    def ge_context(self):
        """Create Great Expectations context."""
        context_root_dir = os.path.join(os.path.dirname(__file__), "../great_expectations")
        return DataContext(context_root_dir=context_root_dir)
    
    @pytest.fixture
    def sample_price_data(self):
        """Generate sample price data for testing."""
        base_time = datetime.utcnow()
        data = []
        
        for i in range(24):  # 24 hours of data
            timestamp = (base_time - timedelta(hours=23-i)).isoformat() + "Z"
            price = 1.0 + (i * 0.01)  # Gradually increasing price
            
            data.append({
                "timestamp": timestamp,
                "price_usd": price,
                "volume_24h": 100000 + (i * 1000),
                "market_cap": price * 1000000,
                "price_change_24h": (i - 12) * 0.5,  # Varies around 0
                "price_change_7d": (i - 12) * 0.3,
                "price_change_30d": (i - 12) * 0.1
            })
        
        return data
    
    @pytest.fixture
    def sample_defillama_data(self):
        """Generate sample DeFiLlama data for testing."""
        return [
            {
                "id": "ethereum",
                "name": "Ethereum",
                "address": "******************************************",
                "symbol": "ETH",
                "decimals": 18,
                "chainId": 1,
                "logoURI": "https://example.com/eth.png",
                "tags": ["native"]
            },
            {
                "id": "usd-coin",
                "name": "USD Coin",
                "address": "******************************************",
                "symbol": "USDC",
                "decimals": 6,
                "chainId": 1,
                "logoURI": "https://example.com/usdc.png",
                "tags": ["stablecoin"]
            }
        ]
    
    def test_price_data_validation(self, ge_context, sample_price_data):
        """Test price data validation with Great Expectations."""
        # Convert to DataFrame
        df = pd.DataFrame(sample_price_data)
        
        # Create batch request
        batch_request = RuntimeBatchRequest(
            datasource_name="token_data_source",
            data_connector_name="default_runtime_data_connector",
            data_asset_name="price_data",
            runtime_parameters={"batch_data": df},
            batch_identifiers={"default_identifier_name": "price_data_test"}
        )
        
        # Get expectation suite
        suite_name = "price_data_suite"
        try:
            suite = ge_context.get_expectation_suite(suite_name)
        except:
            # Create basic suite if not found
            suite = ge_context.create_expectation_suite(suite_name)
            
            # Add basic expectations
            validator = ge_context.get_validator(
                batch_request=batch_request,
                expectation_suite=suite
            )
            
            validator.expect_table_columns_to_match_ordered_list([
                "timestamp", "price_usd", "volume_24h", "market_cap",
                "price_change_24h", "price_change_7d", "price_change_30d"
            ])
            validator.expect_column_values_to_not_be_null("timestamp")
            validator.expect_column_values_to_not_be_null("price_usd")
            validator.expect_column_values_to_be_between("price_usd", min_value=0, max_value=1000000)
            validator.expect_column_values_to_be_between("volume_24h", min_value=0)
            
            validator.save_expectation_suite()
        
        # Run validation
        validator = ge_context.get_validator(
            batch_request=batch_request,
            expectation_suite=suite
        )
        
        results = validator.validate()
        
        # Check results
        assert results.success is True, f"Validation failed: {results.statistics}"
        
        # Check specific expectations
        for result in results.results:
            if not result.success:
                print(f"Failed expectation: {result.expectation_config.expectation_type}")
                print(f"Details: {result.result}")
    
    def test_defillama_data_validation(self, ge_context, sample_defillama_data):
        """Test DeFiLlama data validation."""
        # Convert to DataFrame
        df = pd.DataFrame(sample_defillama_data)
        
        # Create batch request
        batch_request = RuntimeBatchRequest(
            datasource_name="token_data_source",
            data_connector_name="default_runtime_data_connector",
            data_asset_name="defillama_data",
            runtime_parameters={"batch_data": df},
            batch_identifiers={"default_identifier_name": "defillama_test"}
        )
        
        # Get or create expectation suite
        suite_name = "defillama_data_suite"
        try:
            suite = ge_context.get_expectation_suite(suite_name)
        except:
            suite = ge_context.create_expectation_suite(suite_name)
            
            validator = ge_context.get_validator(
                batch_request=batch_request,
                expectation_suite=suite
            )
            
            # Add expectations
            validator.expect_table_columns_to_match_ordered_list([
                "id", "name", "address", "symbol", "decimals", "chainId", "logoURI", "tags"
            ])
            validator.expect_column_values_to_not_be_null("id")
            validator.expect_column_values_to_not_be_null("name")
            validator.expect_column_values_to_not_be_null("symbol")
            validator.expect_column_values_to_match_regex("address", "^0x[a-fA-F0-9]{40}$")
            validator.expect_column_values_to_be_between("decimals", min_value=0, max_value=18)
            
            validator.save_expectation_suite()
        
        # Run validation
        validator = ge_context.get_validator(
            batch_request=batch_request,
            expectation_suite=suite
        )
        
        results = validator.validate()
        assert results.success is True
    
    def test_invalid_price_data_detection(self, ge_context):
        """Test that invalid price data is properly detected."""
        # Create invalid data
        invalid_data = [
            {
                "timestamp": "invalid-timestamp",
                "price_usd": -1.0,  # Negative price
                "volume_24h": -5000,  # Negative volume
                "market_cap": None,  # Null market cap
                "price_change_24h": 150,  # Unrealistic change
                "price_change_7d": 500,
                "price_change_30d": 2000
            }
        ]
        
        df = pd.DataFrame(invalid_data)
        
        batch_request = RuntimeBatchRequest(
            datasource_name="token_data_source",
            data_connector_name="default_runtime_data_connector",
            data_asset_name="invalid_price_data",
            runtime_parameters={"batch_data": df},
            batch_identifiers={"default_identifier_name": "invalid_test"}
        )
        
        # Create strict validation suite
        suite = ge_context.create_expectation_suite("strict_price_validation", overwrite_existing=True)
        
        validator = ge_context.get_validator(
            batch_request=batch_request,
            expectation_suite=suite
        )
        
        # Add strict expectations
        validator.expect_column_values_to_match_regex("timestamp", "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}")
        validator.expect_column_values_to_be_between("price_usd", min_value=0)
        validator.expect_column_values_to_be_between("volume_24h", min_value=0)
        validator.expect_column_values_to_not_be_null("market_cap")
        validator.expect_column_values_to_be_between("price_change_24h", min_value=-99, max_value=100)
        
        results = validator.validate()
        
        # Should fail validation
        assert results.success is False
        
        # Check that multiple expectations failed
        failed_expectations = [r for r in results.results if not r.success]
        assert len(failed_expectations) > 0
    
    @pytest.mark.asyncio
    async def test_real_api_data_validation(self, ge_context, mock_api_responses):
        """Test validation of real API response data."""
        # Fetch real data (mocked)
        coingecko_result = await fetch_coingecko_data(
            "simple/token_price/ethereum",
            {"contract_addresses": "******************************************"}
        )
        
        if coingecko_result.success:
            # Transform API response to expected format
            token_data = coingecko_result.data.get("******************************************", {})
            
            price_data = [{
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "price_usd": token_data.get("usd", 0),
                "volume_24h": 100000,  # Mock volume
                "market_cap": token_data.get("usd", 0) * 1000000,
                "price_change_24h": token_data.get("usd_24h_change", 0),
                "price_change_7d": token_data.get("usd_7d_change", 0),
                "price_change_30d": token_data.get("usd_30d_change", 0)
            }]
            
            # Validate the data
            df = pd.DataFrame(price_data)
            
            batch_request = RuntimeBatchRequest(
                datasource_name="token_data_source",
                data_connector_name="api_data_connector",
                data_asset_name="coingecko_price_data",
                runtime_parameters={"batch_data": df},
                batch_identifiers={
                    "api_name": "coingecko",
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
            suite = ge_context.get_expectation_suite("price_data_suite")
            validator = ge_context.get_validator(
                batch_request=batch_request,
                expectation_suite=suite
            )
            
            results = validator.validate()
            assert results.success is True


@pytest.mark.unit
class TestDataQualityHelpers:
    """Test helper functions for data quality validation."""
    
    def test_timestamp_validation(self):
        """Test timestamp format validation."""
        valid_timestamps = [
            "2025-07-09T12:00:00Z",
            "2025-07-09T12:00:00.123Z",
            "2025-07-09T12:00:00",
        ]
        
        invalid_timestamps = [
            "2025-13-09T12:00:00Z",  # Invalid month
            "2025-07-32T12:00:00Z",  # Invalid day
            "2025-07-09T25:00:00Z",  # Invalid hour
            "invalid-timestamp",
            None
        ]
        
        import re
        timestamp_pattern = r"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$"
        
        for ts in valid_timestamps:
            assert re.match(timestamp_pattern, ts), f"Valid timestamp failed: {ts}"
        
        for ts in invalid_timestamps:
            if ts is not None:
                assert not re.match(timestamp_pattern, ts), f"Invalid timestamp passed: {ts}"
    
    def test_price_bounds_validation(self):
        """Test price bounds validation logic."""
        valid_prices = [0.000001, 1.0, 100.0, 50000.0]
        invalid_prices = [-1.0, -0.001, 0, 10000000.0]  # 0 and negative, or too high
        
        def is_valid_price(price):
            return price is not None and 0 < price < 1000000
        
        for price in valid_prices:
            assert is_valid_price(price), f"Valid price failed: {price}"
        
        for price in invalid_prices:
            assert not is_valid_price(price), f"Invalid price passed: {price}"
    
    def test_ethereum_address_validation(self):
        """Test Ethereum address format validation."""
        valid_addresses = [
            "******************************************",
            "******************************************",
            "******************************************"
        ]
        
        invalid_addresses = [
            "1234567890123456789012345678901234567890",  # Missing 0x
            "0x123456789012345678901234567890123456789",   # Too short
            "******************************************1", # Too long
            "0xGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG",   # Invalid chars
            None,
            ""
        ]
        
        import re
        address_pattern = r"^0x[a-fA-F0-9]{40}$"
        
        for addr in valid_addresses:
            assert re.match(address_pattern, addr), f"Valid address failed: {addr}"
        
        for addr in invalid_addresses:
            if addr is not None:
                assert not re.match(address_pattern, addr), f"Invalid address passed: {addr}"


@pytest.mark.integration
class TestPipelineDataQuality:
    """Test data quality throughout the entire pipeline."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_data_quality(
        self,
        db_manager,
        cache_manager,
        mock_api_responses
    ):
        """Test data quality from API fetch through database storage."""
        # This would test the complete pipeline
        # For now, we'll test the market data agent
        
        async with aiohttp.ClientSession() as session:
            agent = MarketDataAgent(
                db_manager=db_manager,
                cache_manager=cache_manager,
                session=session
            )
            
            result = await agent.get_market_data(
                token_address="******************************************",
                chain_id=1
            )
            
            # Validate result structure
            assert isinstance(result, dict)
            assert "sources" in result
            
            # Validate data quality
            for source_data in result.get("sources", []):
                if "price_usd" in source_data:
                    assert source_data["price_usd"] >= 0
                if "volume_24h" in source_data:
                    assert source_data["volume_24h"] >= 0
