"""
Unit tests for fetch helper functions.

Tests all API fetch helpers with mock responses and error handling.
"""

import pytest
import json
from unittest.mock import patch, AsyncMock, MagicMock
from aioresponses import aioresponses
import aiohttp

from src.utils.fetch_helpers import (
    fetch_coingecko_data,
    fetch_dexscreener_data,
    fetch_defillama_data,
    fetch_etherscan_data,
    fetch_fear_greed_index,
    fetch_google_trends,
    APIResponse
)


@pytest.mark.unit
class TestAPIResponse:
    """Test the APIResponse dataclass."""
    
    def test_successful_response(self):
        """Test creating successful API response."""
        response = APIResponse(
            success=True,
            data={"test": "data"},
            status_code=200
        )
        
        assert response.success is True
        assert response.data == {"test": "data"}
        assert response.error is None
        assert response.status_code == 200
        assert response.rate_limited is False
        assert response.cached is False
    
    def test_error_response(self):
        """Test creating error API response."""
        response = APIResponse(
            success=False,
            error="API error",
            status_code=500
        )
        
        assert response.success is False
        assert response.error == "API error"
        assert response.data is None
        assert response.status_code == 500


@pytest.mark.unit
class TestCoinGeckoFetch:
    """Test CoinGecko API fetch helper."""
    
    @pytest.mark.asyncio
    async def test_successful_fetch(self):
        """Test successful CoinGecko API call."""
        with aioresponses() as m:
            expected_data = {
                "0x1234": {
                    "usd": 1.25,
                    "usd_24h_change": 5.2
                }
            }
            
            m.get(
                "https://api.coingecko.com/api/v3/simple/price",
                payload=expected_data
            )
            
            result = await fetch_coingecko_data(
                "simple/price",
                {"ids": "bitcoin", "vs_currencies": "usd"}
            )
            
            assert result.success is True
            assert result.data == expected_data
            assert result.status_code == 200
    
    @pytest.mark.asyncio
    async def test_rate_limited_response(self):
        """Test handling of rate limited response."""
        with aioresponses() as m:
            m.get(
                "https://api.coingecko.com/api/v3/simple/price",
                status=429,
                payload={"error": "Rate limited"}
            )
            
            result = await fetch_coingecko_data("simple/price")
            
            assert result.success is False
            assert result.rate_limited is True
            assert result.status_code == 429
            assert "Rate limited" in result.error
    
    @pytest.mark.asyncio
    async def test_api_key_header(self):
        """Test that API key is included in headers when available."""
        with patch.dict('os.environ', {'COINGECKO_API_KEY': 'test_key'}):
            with aioresponses() as m:
                m.get(
                    "https://api.coingecko.com/api/v3/simple/price",
                    payload={"test": "data"}
                )
                
                result = await fetch_coingecko_data("simple/price")
                
                # Check that the request was made with API key header
                request = m.requests[('GET', 'https://api.coingecko.com/api/v3/simple/price')][0]
                assert 'x-cg-demo-api-key' in request.kwargs['headers']
                assert request.kwargs['headers']['x-cg-demo-api-key'] == 'test_key'
    
    @pytest.mark.asyncio
    async def test_network_error_handling(self):
        """Test handling of network errors."""
        with aioresponses() as m:
            m.get(
                "https://api.coingecko.com/api/v3/simple/price",
                exception=aiohttp.ClientError("Network error")
            )
            
            result = await fetch_coingecko_data("simple/price")
            
            assert result.success is False
            assert "Network error" in result.error


@pytest.mark.unit
class TestDexScreenerFetch:
    """Test DexScreener API fetch helper."""
    
    @pytest.mark.asyncio
    async def test_successful_fetch(self):
        """Test successful DexScreener API call."""
        with aioresponses() as m:
            expected_data = {
                "pairs": [
                    {
                        "priceUsd": "1.25",
                        "volume": {"h24": 125000},
                        "liquidity": {"usd": 850000}
                    }
                ]
            }
            
            m.get(
                "https://api.dexscreener.com/latest/dex/tokens/0x1234",
                payload=expected_data
            )
            
            result = await fetch_dexscreener_data("dex/tokens/0x1234")
            
            assert result.success is True
            assert result.data == expected_data
    
    @pytest.mark.asyncio
    async def test_user_agent_header(self):
        """Test that User-Agent header is set correctly."""
        with aioresponses() as m:
            m.get(
                "https://api.dexscreener.com/latest/test",
                payload={"test": "data"}
            )
            
            await fetch_dexscreener_data("test")
            
            request = m.requests[('GET', 'https://api.dexscreener.com/latest/test')][0]
            assert request.kwargs['headers']['User-Agent'] == 'TokenAnalyzer/1.0'


@pytest.mark.unit
class TestDeFiLlamaFetch:
    """Test DeFiLlama API fetch helper."""
    
    @pytest.mark.asyncio
    async def test_successful_fetch(self):
        """Test successful DeFiLlama API call."""
        with aioresponses() as m:
            expected_data = {
                "protocols": [
                    {
                        "name": "Uniswap",
                        "tvl": 5000000000
                    }
                ]
            }
            
            m.get(
                "https://api.llama.fi/protocols",
                payload=expected_data
            )
            
            result = await fetch_defillama_data("protocols")
            
            assert result.success is True
            assert result.data == expected_data


@pytest.mark.unit
class TestEtherscanFetch:
    """Test Etherscan API fetch helper."""
    
    @pytest.mark.asyncio
    async def test_successful_fetch(self):
        """Test successful Etherscan API call."""
        with aioresponses() as m:
            expected_data = {
                "status": "1",
                "message": "OK",
                "result": {
                    "contractName": "TestToken",
                    "symbol": "TEST"
                }
            }
            
            m.get(
                "https://api.etherscan.io/api",
                payload=expected_data
            )
            
            result = await fetch_etherscan_data(
                "contract",
                "getsourcecode",
                {"address": "0x1234"}
            )
            
            assert result.success is True
            assert result.data == expected_data["result"]
    
    @pytest.mark.asyncio
    async def test_etherscan_error_response(self):
        """Test handling of Etherscan error responses."""
        with aioresponses() as m:
            error_data = {
                "status": "0",
                "message": "NOTOK",
                "result": "Invalid address"
            }
            
            m.get(
                "https://api.etherscan.io/api",
                payload=error_data
            )
            
            result = await fetch_etherscan_data(
                "contract",
                "getsourcecode",
                {"address": "invalid"}
            )
            
            assert result.success is False
            assert "NOTOK" in result.error
    
    @pytest.mark.asyncio
    async def test_rate_limiting_applied(self):
        """Test that rate limiting is applied to Etherscan calls."""
        with patch('src.utils.fetch_helpers._get_etherscan_limiter') as mock_limiter:
            mock_rate_limiter = AsyncMock()
            mock_limiter.return_value = mock_rate_limiter
            
            with aioresponses() as m:
                m.get(
                    "https://api.etherscan.io/api",
                    payload={"status": "1", "result": "test"}
                )
                
                await fetch_etherscan_data("account", "balance")
                
                # Verify rate limiter was called
                mock_rate_limiter.acquire.assert_called_once()


@pytest.mark.unit
class TestFearGreedFetch:
    """Test Fear & Greed Index fetch helper."""
    
    @pytest.mark.asyncio
    async def test_successful_fetch(self):
        """Test successful Fear & Greed Index API call."""
        with aioresponses() as m:
            expected_data = {
                "data": [
                    {
                        "value": "45",
                        "value_classification": "Fear",
                        "timestamp": "**********"
                    }
                ]
            }
            
            m.get(
                "https://api.alternative.me/fng/",
                payload=expected_data
            )
            
            result = await fetch_fear_greed_index(limit=1)
            
            assert result.success is True
            assert result.data == expected_data
    
    @pytest.mark.asyncio
    async def test_custom_limit_parameter(self):
        """Test custom limit parameter."""
        with aioresponses() as m:
            m.get(
                "https://api.alternative.me/fng/",
                payload={"data": []}
            )
            
            await fetch_fear_greed_index(limit=7)
            
            request = m.requests[('GET', 'https://api.alternative.me/fng/')][0]
            assert request.kwargs['params']['limit'] == 7


@pytest.mark.unit
class TestGoogleTrendsFetch:
    """Test Google Trends fetch helper."""
    
    @pytest.mark.asyncio
    async def test_successful_fetch(self):
        """Test successful Google Trends fetch."""
        with patch('src.utils.fetch_helpers.TrendReq') as mock_trends:
            mock_pytrends = MagicMock()
            mock_trends.return_value = mock_pytrends
            
            # Mock pandas DataFrame
            mock_df = MagicMock()
            mock_df.empty = False
            mock_df.to_dict.return_value = {"bitcoin": [50, 60, 70]}
            mock_pytrends.interest_over_time.return_value = mock_df
            
            mock_pytrends.related_queries = {"bitcoin": {"top": [], "rising": []}}
            mock_pytrends.related_topics = {"bitcoin": {"top": [], "rising": []}}
            
            result = await fetch_google_trends(["bitcoin"], "today 7-d")
            
            assert result.success is True
            assert "keywords" in result.data
            assert result.data["keywords"] == ["bitcoin"]
            assert "interest_over_time" in result.data
    
    @pytest.mark.asyncio
    async def test_pytrends_not_installed(self):
        """Test handling when PyTrends is not installed."""
        with patch('src.utils.fetch_helpers.TrendReq', side_effect=ImportError):
            result = await fetch_google_trends(["bitcoin"])
            
            assert result.success is False
            assert "PyTrends library not installed" in result.error
    
    @pytest.mark.asyncio
    async def test_pytrends_error_handling(self):
        """Test handling of PyTrends errors."""
        with patch('src.utils.fetch_helpers.TrendReq') as mock_trends:
            mock_trends.side_effect = Exception("PyTrends error")
            
            result = await fetch_google_trends(["bitcoin"])
            
            assert result.success is False
            assert "PyTrends error" in result.error


@pytest.mark.unit
class TestFetchHelpersIntegration:
    """Integration tests for fetch helpers."""
    
    @pytest.mark.asyncio
    async def test_session_reuse(self):
        """Test that external session can be reused."""
        async with aiohttp.ClientSession() as session:
            with aioresponses() as m:
                m.get(
                    "https://api.coingecko.com/api/v3/simple/price",
                    payload={"test": "data"}
                )
                
                result = await fetch_coingecko_data(
                    "simple/price",
                    session=session
                )
                
                assert result.success is True
    
    @pytest.mark.asyncio
    async def test_concurrent_api_calls(self):
        """Test concurrent API calls with rate limiting."""
        import asyncio
        
        with aioresponses() as m:
            # Mock multiple endpoints
            m.get(
                "https://api.coingecko.com/api/v3/simple/price",
                payload={"coingecko": "data"}
            )
            m.get(
                "https://api.dexscreener.com/latest/dex/tokens/test",
                payload={"dexscreener": "data"}
            )
            m.get(
                "https://api.llama.fi/protocols",
                payload={"defillama": "data"}
            )
            
            # Make concurrent calls
            tasks = [
                fetch_coingecko_data("simple/price"),
                fetch_dexscreener_data("dex/tokens/test"),
                fetch_defillama_data("protocols")
            ]
            
            results = await asyncio.gather(*tasks)
            
            # All should succeed
            assert all(result.success for result in results)
            assert len(results) == 3
