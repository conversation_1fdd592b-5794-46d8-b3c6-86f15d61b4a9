"""
Unit tests for rate limiting utilities.

Tests all rate limiting strategies, API-specific limiters,
and the rate limiting decorator functionality.
"""

import asyncio
import time
import pytest
from unittest.mock import patch, AsyncMock

from src.utils.rate_limit import (
    RateLimiter,
    RateLimitConfig,
    RateLimitStrategy,
    EtherscanRateLimiter,
    APIRateLimiter,
    rate_limited
)


@pytest.mark.unit
class TestRateLimiter:
    """Test the core RateLimiter class."""
    
    @pytest.mark.asyncio
    async def test_token_bucket_basic(self):
        """Test basic token bucket functionality."""
        config = RateLimitConfig(
            requests_per_second=2.0,
            burst_size=3,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        )
        limiter = RateLimiter(config)
        
        # Should allow burst requests
        start_time = time.time()
        for _ in range(3):
            await limiter.acquire()
        elapsed = time.time() - start_time
        
        # Burst should be nearly instantaneous
        assert elapsed < 0.1
    
    @pytest.mark.asyncio
    async def test_token_bucket_rate_limiting(self):
        """Test token bucket rate limiting behavior."""
        config = RateLimitConfig(
            requests_per_second=5.0,  # 5 requests per second
            burst_size=2,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        )
        limiter = RateLimiter(config)
        
        # Use up burst capacity
        await limiter.acquire()
        await limiter.acquire()
        
        # Next request should be rate limited
        start_time = time.time()
        await limiter.acquire()
        elapsed = time.time() - start_time
        
        # Should wait approximately 0.2 seconds (1/5 second)
        assert 0.15 < elapsed < 0.35
    
    @pytest.mark.asyncio
    async def test_sliding_window_strategy(self):
        """Test sliding window rate limiting."""
        config = RateLimitConfig(
            requests_per_second=3.0,
            burst_size=5,
            strategy=RateLimitStrategy.SLIDING_WINDOW
        )
        limiter = RateLimiter(config)
        
        # Make requests within the limit
        for _ in range(3):
            await limiter.acquire()
        
        # Next request should be delayed
        start_time = time.time()
        await limiter.acquire()
        elapsed = time.time() - start_time
        
        # Should wait for the sliding window
        assert elapsed > 0.8  # Should wait for oldest request to expire
    
    @pytest.mark.asyncio
    async def test_fixed_window_strategy(self):
        """Test fixed window rate limiting."""
        config = RateLimitConfig(
            requests_per_second=2.0,
            burst_size=5,
            strategy=RateLimitStrategy.FIXED_WINDOW
        )
        limiter = RateLimiter(config)
        
        # Use up window capacity
        await limiter.acquire()
        await limiter.acquire()
        
        # Next request should wait for new window
        start_time = time.time()
        await limiter.acquire()
        elapsed = time.time() - start_time
        
        # Should wait for next window (up to 1 second)
        assert elapsed > 0.5


@pytest.mark.unit
class TestEtherscanRateLimiter:
    """Test Etherscan-specific rate limiter."""
    
    def test_free_tier_limits(self):
        """Test free tier rate limits."""
        limiter = EtherscanRateLimiter(api_key=None)
        assert limiter.config.requests_per_second == 5
        assert limiter.config.burst_size == 10
    
    def test_pro_tier_limits(self):
        """Test pro tier rate limits."""
        limiter = EtherscanRateLimiter(api_key="valid_api_key")
        assert limiter.config.requests_per_second == 100
        assert limiter.config.burst_size == 200
    
    def test_invalid_api_key_uses_free_tier(self):
        """Test that invalid API key defaults to free tier."""
        limiter = EtherscanRateLimiter(api_key="your_etherscan_api_key_here")
        assert limiter.config.requests_per_second == 5


@pytest.mark.unit
class TestAPIRateLimiter:
    """Test the multi-API rate limiter manager."""
    
    def test_default_limiters_created(self):
        """Test that default API limiters are created."""
        manager = APIRateLimiter()
        
        expected_apis = [
            "coingecko", "dexscreener", "defillama", 
            "fear_greed", "google_trends"
        ]
        
        for api in expected_apis:
            assert api in manager.limiters
    
    def test_add_custom_limiter(self):
        """Test adding custom rate limiter."""
        manager = APIRateLimiter()
        
        config = RateLimitConfig(
            requests_per_second=10.0,
            burst_size=20
        )
        
        manager.add_limiter("custom_api", config)
        assert "custom_api" in manager.limiters
        assert manager.limiters["custom_api"].config.requests_per_second == 10.0
    
    @pytest.mark.asyncio
    async def test_acquire_for_known_api(self):
        """Test acquiring rate limit for known API."""
        manager = APIRateLimiter()
        
        # Should not raise exception
        await manager.acquire("coingecko")
    
    @pytest.mark.asyncio
    async def test_acquire_for_unknown_api(self):
        """Test acquiring rate limit for unknown API."""
        manager = APIRateLimiter()
        
        # Should handle gracefully (no exception)
        await manager.acquire("unknown_api")


@pytest.mark.unit
class TestRateLimitedDecorator:
    """Test the rate_limited decorator."""
    
    @pytest.mark.asyncio
    async def test_decorator_applies_rate_limiting(self):
        """Test that decorator applies rate limiting."""
        call_times = []
        
        @rate_limited("test_api", max_retries=1)
        async def test_function():
            call_times.append(time.time())
            return "success"
        
        # Mock the rate limiter to have strict limits
        with patch('src.utils.rate_limit.APIRateLimiter') as mock_manager_class:
            mock_manager = AsyncMock()
            mock_manager_class.return_value = mock_manager
            
            # Make multiple calls
            result1 = await test_function()
            result2 = await test_function()
            
            assert result1 == "success"
            assert result2 == "success"
            assert mock_manager.acquire.call_count == 2
    
    @pytest.mark.asyncio
    async def test_decorator_handles_exceptions(self):
        """Test that decorator handles and retries on exceptions."""
        call_count = 0
        
        @rate_limited("test_api", max_retries=2)
        async def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise ConnectionError("Network error")
            return "success"
        
        result = await failing_function()
        assert result == "success"
        assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_decorator_max_retries_exceeded(self):
        """Test decorator behavior when max retries exceeded."""
        @rate_limited("test_api", max_retries=1)
        async def always_failing_function():
            raise ConnectionError("Persistent error")
        
        with pytest.raises(ConnectionError):
            await always_failing_function()


@pytest.mark.unit
class TestRateLimitIntegration:
    """Integration tests for rate limiting components."""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests_with_rate_limiting(self):
        """Test concurrent requests with rate limiting."""
        config = RateLimitConfig(
            requests_per_second=5.0,
            burst_size=3,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        )
        limiter = RateLimiter(config)
        
        async def make_request(request_id):
            await limiter.acquire()
            return f"request_{request_id}"
        
        # Make 10 concurrent requests
        start_time = time.time()
        tasks = [make_request(i) for i in range(10)]
        results = await asyncio.gather(*tasks)
        elapsed = time.time() - start_time
        
        # All requests should complete
        assert len(results) == 10
        
        # Should take time due to rate limiting
        # 3 burst + 7 rate-limited at 5/sec = ~1.4 seconds minimum
        assert elapsed > 1.0
    
    @pytest.mark.asyncio
    async def test_rate_limiter_recovery_after_burst(self):
        """Test that rate limiter recovers after burst usage."""
        config = RateLimitConfig(
            requests_per_second=10.0,
            burst_size=5,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        )
        limiter = RateLimiter(config)
        
        # Use up burst capacity
        for _ in range(5):
            await limiter.acquire()
        
        # Wait for token refill
        await asyncio.sleep(0.6)  # Should refill ~6 tokens
        
        # Should allow burst again
        start_time = time.time()
        for _ in range(5):
            await limiter.acquire()
        elapsed = time.time() - start_time
        
        # Should be fast again due to token refill
        assert elapsed < 0.2
    
    @pytest.mark.asyncio
    async def test_etherscan_limiter_with_real_timing(self):
        """Test Etherscan limiter with realistic timing."""
        limiter = EtherscanRateLimiter(api_key=None)  # Free tier
        
        # Make requests at the limit
        start_time = time.time()
        for _ in range(6):  # One more than burst
            await limiter.acquire()
        elapsed = time.time() - start_time
        
        # Should take at least 0.2 seconds for the 6th request (1/5 second)
        assert elapsed > 0.15


@pytest.mark.unit
class TestRateLimitPerformance:
    """Performance tests for rate limiting."""

    @pytest.mark.asyncio
    async def test_rate_limiter_overhead(self):
        """Test that rate limiter has minimal overhead."""
        config = RateLimitConfig(
            requests_per_second=1000.0,  # Very high limit
            burst_size=1000,
            strategy=RateLimitStrategy.TOKEN_BUCKET
        )
        limiter = RateLimiter(config)

        # Measure overhead of acquire calls
        start_time = time.time()
        for _ in range(100):
            await limiter.acquire()
        elapsed = time.time() - start_time

        # Should be very fast with high limits
        assert elapsed < 0.1  # Less than 100ms for 100 calls

    @pytest.mark.asyncio
    async def test_memory_usage_stability(self):
        """Test that rate limiter doesn't leak memory."""
        config = RateLimitConfig(
            requests_per_second=100.0,
            burst_size=10,
            strategy=RateLimitStrategy.SLIDING_WINDOW
        )
        limiter = RateLimiter(config)

        # Make many requests to test memory stability
        for _ in range(1000):
            await limiter.acquire()
            await asyncio.sleep(0.001)  # Small delay

        # Sliding window should not grow indefinitely
        if hasattr(limiter, '_requests'):
            assert len(limiter._requests) <= 100  # Should clean up old requests
