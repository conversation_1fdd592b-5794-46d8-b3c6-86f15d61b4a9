"""
Comprehensive unit tests for enhanced logging configuration.
Tests all logging components with 100% code coverage and edge cases.
"""

import asyncio
import json
import logging
import os
import tempfile
import uuid
from contextlib import contextmanager
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import pytest
import structlog

# Add src to path for testing
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

# Mock the config import to avoid database dependencies
with patch.dict('sys.modules', {'src.core.database': MagicMock(), 'duckdb': MagicMock()}):
    from src.core.logging_config import (
    add_correlation_id,
    add_system_context,
    filter_sensitive_data,
    add_performance_metrics,
    AsyncStructlogHandler,
    configure_structlog,
    configure_standard_logging,
    setup_logging,
    CorrelationContext,
    RequestContext,
    get_logger,
    set_correlation_id,
    get_correlation_id,
    correlation_id,
    request_id,
    user_id,
    session_id,
    trace_id,
)


class TestCorrelationTracking:
    """Test correlation ID tracking functionality."""
    
    def test_add_correlation_id_new(self):
        """Test adding new correlation ID when none exists."""
        # Reset context
        correlation_id.set(None)
        
        event_dict = {"message": "test"}
        result = add_correlation_id(None, "info", event_dict)
        
        assert "correlation_id" in result
        assert isinstance(result["correlation_id"], str)
        assert len(result["correlation_id"]) == 36  # UUID length
    
    def test_add_correlation_id_existing(self):
        """Test using existing correlation ID."""
        test_id = "test-correlation-123"
        correlation_id.set(test_id)
        
        event_dict = {"message": "test"}
        result = add_correlation_id(None, "info", event_dict)
        
        assert result["correlation_id"] == test_id
    
    def test_add_correlation_id_with_all_context(self):
        """Test adding all context variables."""
        correlation_id.set("corr-123")
        request_id.set("req-456")
        user_id.set("user-789")
        session_id.set("sess-abc")
        trace_id.set("trace-def")
        
        event_dict = {"message": "test"}
        result = add_correlation_id(None, "info", event_dict)
        
        assert result["correlation_id"] == "corr-123"
        assert result["request_id"] == "req-456"
        assert result["user_id"] == "user-789"
        assert result["session_id"] == "sess-abc"
        assert result["trace_id"] == "trace-def"
    
    def test_correlation_context_manager(self):
        """Test CorrelationContext context manager."""
        test_id = "context-test-123"
        
        # Test with specific ID
        with CorrelationContext(test_id) as ctx_id:
            assert ctx_id == test_id
            assert get_correlation_id() == test_id
        
        # Context should be reset after exit
        assert get_correlation_id() != test_id
    
    def test_correlation_context_manager_auto_id(self):
        """Test CorrelationContext with auto-generated ID."""
        with CorrelationContext() as ctx_id:
            assert isinstance(ctx_id, str)
            assert len(ctx_id) == 36  # UUID length
            assert get_correlation_id() == ctx_id
    
    def test_nested_correlation_contexts(self):
        """Test nested correlation contexts."""
        with CorrelationContext("outer") as outer_id:
            assert get_correlation_id() == "outer"
            
            with CorrelationContext("inner") as inner_id:
                assert get_correlation_id() == "inner"
            
            # Should restore outer context
            assert get_correlation_id() == "outer"
    
    def test_request_context_manager(self):
        """Test RequestContext context manager."""
        with RequestContext("req-123", "user-456") as ctx:
            # Check context variables are set
            event_dict = {"message": "test"}
            result = add_correlation_id(None, "info", event_dict)
            
            assert result["request_id"] == "req-123"
            assert result["user_id"] == "user-456"
    
    def test_request_context_without_user(self):
        """Test RequestContext without user ID."""
        # Clear any existing context first
        correlation_id.set(None)
        request_id.set(None)
        user_id.set(None)
        session_id.set(None)
        trace_id.set(None)

        with RequestContext("req-only") as ctx:
            event_dict = {"message": "test"}
            result = add_correlation_id(None, "info", event_dict)

            assert result["request_id"] == "req-only"
            assert "user_id" not in result


class TestSystemContext:
    """Test system context addition."""
    
    @patch('os.uname')
    @patch('os.getpid')
    def test_add_system_context(self, mock_getpid, mock_uname):
        """Test adding system context information."""
        # Mock system info
        mock_uname.return_value = Mock(nodename="test-host")
        mock_getpid.return_value = 12345
        
        event_dict = {"message": "test"}
        result = add_system_context(None, "info", event_dict)
        
        assert result["service"] == "token-analyzer"
        assert result["hostname"] == "test-host"
        assert result["process_id"] == 12345
        assert "version" in result
        assert "environment" in result
    
    def test_add_system_context_preserves_existing(self):
        """Test that system context preserves existing fields."""
        event_dict = {"message": "test", "custom_field": "value"}
        result = add_system_context(None, "info", event_dict)
        
        assert result["custom_field"] == "value"
        assert result["message"] == "test"


class TestSensitiveDataFiltering:
    """Test sensitive data filtering functionality."""
    
    def test_filter_simple_sensitive_keys(self):
        """Test filtering simple sensitive keys."""
        event_dict = {
            "username": "testuser",
            "password": "secret123",
            "api_key": "sk-1234567890",
            "token": "bearer-xyz",
            "message": "login attempt"
        }
        
        result = filter_sensitive_data(None, "info", event_dict)
        
        assert result["username"] == "testuser"  # Not sensitive
        assert result["password"] == "[REDACTED]"
        assert result["api_key"] == "[REDACTED]"
        assert result["token"] == "[REDACTED]"
        assert result["message"] == "login attempt"
    
    def test_filter_nested_sensitive_data(self):
        """Test filtering nested sensitive data."""
        event_dict = {
            "user_data": {
                "username": "testuser",
                "credentials": {
                    "password": "secret",
                    "api_key": "key123"
                },
                "metadata": {
                    "ip": "127.0.0.1",
                    "private_key": "pk-secret"
                }
            }
        }
        
        result = filter_sensitive_data(None, "info", event_dict)
        
        assert result["user_data"]["username"] == "testuser"
        assert result["user_data"]["credentials"] == "[REDACTED]"
        assert result["user_data"]["metadata"]["ip"] == "127.0.0.1"
        assert result["user_data"]["metadata"]["private_key"] == "[REDACTED]"
    
    def test_filter_list_with_sensitive_data(self):
        """Test filtering lists containing sensitive data."""
        event_dict = {
            "requests": [
                {"url": "/api/login", "auth": "bearer-token"},
                {"url": "/api/data", "user": "testuser"}
            ]
        }
        
        result = filter_sensitive_data(None, "info", event_dict)
        
        assert result["requests"][0]["url"] == "/api/login"
        assert result["requests"][0]["auth"] == "[REDACTED]"
        assert result["requests"][1]["user"] == "testuser"
    
    def test_filter_case_insensitive(self):
        """Test case-insensitive filtering."""
        event_dict = {
            "PASSWORD": "secret",
            "Api_Key": "key123",
            "ACCESS_TOKEN": "token456"
        }
        
        result = filter_sensitive_data(None, "info", event_dict)
        
        assert result["PASSWORD"] == "[REDACTED]"
        assert result["Api_Key"] == "[REDACTED]"
        assert result["ACCESS_TOKEN"] == "[REDACTED]"
    
    def test_filter_non_dict_values(self):
        """Test filtering with non-dict values."""
        event_dict = {
            "string_value": "test",
            "number_value": 123,
            "bool_value": True,
            "none_value": None
        }
        
        result = filter_sensitive_data(None, "info", event_dict)
        
        # Should return unchanged for non-sensitive, non-dict values
        assert result == event_dict


class TestPerformanceMetrics:
    """Test performance metrics addition."""
    
    @patch('src.core.logging_config.datetime')
    def test_add_performance_metrics(self, mock_datetime):
        """Test adding performance metrics."""
        # Mock datetime
        mock_datetime.utcnow.return_value.timestamp.return_value = 1234567890.123
        
        event_dict = {"message": "test"}
        result = add_performance_metrics(None, "info", event_dict)
        
        assert result["timestamp_ms"] == 1234567890123
        assert result["message"] == "test"


class TestAsyncStructlogHandler:
    """Test AsyncStructlogHandler functionality."""
    
    def test_handler_initialization(self):
        """Test handler initialization."""
        handler = AsyncStructlogHandler(logging.INFO)
        assert handler.level == logging.INFO
        assert handler.structlog_logger is not None
    
    @patch('structlog.get_logger')
    def test_emit_info_level(self, mock_get_logger):
        """Test emitting INFO level log."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        handler = AsyncStructlogHandler()
        record = logging.LogRecord(
            name="test.logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        record.module = "test"
        record.funcName = "test_func"
        
        handler.emit(record)
        
        mock_logger.info.assert_called_once()
        call_kwargs = mock_logger.info.call_args[1]
        assert call_kwargs["message"] == "Test message"
        assert call_kwargs["level"] == "info"  # structlog uses lowercase
        assert call_kwargs["logger_name"] == "test.logger"
    
    @patch('structlog.get_logger')
    def test_emit_with_exception(self, mock_get_logger):
        """Test emitting log with exception info."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        handler = AsyncStructlogHandler()
        
        try:
            raise ValueError("Test exception")
        except ValueError:
            record = logging.LogRecord(
                name="test.logger",
                level=logging.ERROR,
                pathname="test.py",
                lineno=10,
                msg="Error occurred",
                args=(),
                exc_info=sys.exc_info()
            )
            record.module = "test"
            record.funcName = "test_func"
            
            handler.emit(record)
        
        mock_logger.error.assert_called_once()
        call_kwargs = mock_logger.error.call_args[1]
        assert "exception" in call_kwargs
    
    @patch('structlog.get_logger')
    def test_emit_different_levels(self, mock_get_logger):
        """Test emitting different log levels."""
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        
        handler = AsyncStructlogHandler()
        
        # Test different levels
        levels = [
            (logging.DEBUG, "debug"),
            (logging.INFO, "info"),
            (logging.WARNING, "warning"),
            (logging.ERROR, "error"),
            (logging.CRITICAL, "critical")
        ]
        
        for level, method_name in levels:
            record = logging.LogRecord(
                name="test.logger",
                level=level,
                pathname="test.py",
                lineno=10,
                msg="Test message",
                args=(),
                exc_info=None
            )
            record.module = "test"
            record.funcName = "test_func"
            
            handler.emit(record)
            
            # Check that the appropriate method was called
            method = getattr(mock_logger, method_name)
            method.assert_called()


class TestLoggingConfiguration:
    """Test logging configuration functions."""
    
    @patch('src.core.logging_config.config')
    def test_configure_structlog_development(self, mock_config):
        """Test structlog configuration for development."""
        mock_config.system.environment.value = "development"
        
        configure_structlog()
        
        # Should configure without errors
        logger = structlog.get_logger("test")
        assert logger is not None
    
    @patch('src.core.logging_config.config')
    def test_configure_structlog_production(self, mock_config):
        """Test structlog configuration for production."""
        mock_config.system.environment.value = "production"
        
        configure_structlog()
        
        # Should configure without errors
        logger = structlog.get_logger("test")
        assert logger is not None
    
    @patch('src.core.logging_config.config')
    @patch('logging.config.dictConfig')
    def test_configure_standard_logging(self, mock_dict_config, mock_config):
        """Test standard logging configuration."""
        mock_config.system.logs_dir = Path("/tmp/test_logs")
        mock_config.system.environment.value = "development"
        mock_config.system.log_level.value = "INFO"
        
        with patch('pathlib.Path.mkdir'):
            configure_standard_logging()
        
        mock_dict_config.assert_called_once()
        config_dict = mock_dict_config.call_args[0][0]
        
        assert config_dict["version"] == 1
        assert "handlers" in config_dict
        assert "loggers" in config_dict
    
    @patch('src.core.logging_config.configure_structlog')
    @patch('src.core.logging_config.configure_standard_logging')
    def test_setup_logging(self, mock_configure_standard, mock_configure_structlog):
        """Test complete logging setup."""
        setup_logging()
        
        mock_configure_structlog.assert_called_once()
        mock_configure_standard.assert_called_once()


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_get_logger(self):
        """Test get_logger function."""
        logger = get_logger("test.logger")
        assert logger is not None
        
        # Test without name
        logger2 = get_logger()
        assert logger2 is not None
    
    def test_set_get_correlation_id(self):
        """Test setting and getting correlation ID."""
        test_id = "test-correlation-456"
        
        set_correlation_id(test_id)
        assert get_correlation_id() == test_id
    
    def test_get_correlation_id_none(self):
        """Test getting correlation ID when none is set."""
        correlation_id.set(None)
        assert get_correlation_id() is None


@pytest.mark.asyncio
class TestAsyncLogging:
    """Test asynchronous logging functionality."""
    
    async def test_async_logging_performance(self):
        """Test async logging doesn't block."""
        setup_logging()
        logger = get_logger("test.async")
        
        start_time = asyncio.get_event_loop().time()
        
        # Log many messages concurrently
        tasks = []
        for i in range(100):
            task = asyncio.create_task(self._log_message(logger, i))
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time
        
        # Should complete quickly (less than 1 second for 100 messages)
        assert duration < 1.0
    
    async def _log_message(self, logger, message_id):
        """Helper method to log a message."""
        logger.info("Async test message", message_id=message_id)
        await asyncio.sleep(0.001)  # Simulate some async work


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
