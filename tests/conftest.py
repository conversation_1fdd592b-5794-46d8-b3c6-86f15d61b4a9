"""
Pytest configuration and shared fixtures for the test suite.

Provides common fixtures for database connections, mock services,
and test data generation.
"""

import asyncio
import os
import tempfile
from typing import AsyncGenerator, Dict, Any
from unittest.mock import AsyncMock, MagicMock
import pytest
import aiohttp
from aioresponses import aioresponses

# Test environment setup
os.environ.update({
    "ENVIRONMENT": "test",
    "LOG_LEVEL": "DEBUG",
    "DATABASE_URL": "sqlite:///:memory:",
    "REDIS_URL": "redis://localhost:6379/15",  # Test database
    "ETHERSCAN_API_KEY": "test_etherscan_key",
    "COINGECKO_API_KEY": "test_coingecko_key",
    "OPENROUTER_API_KEY": "test_openrouter_key"
})


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def db_manager():
    """Create a test database manager with in-memory SQLite."""
    from src.core.database import DatabaseManager
    
    # Use in-memory SQLite for tests
    db_manager = DatabaseManager("sqlite:///:memory:")
    await db_manager.initialize()
    
    yield db_manager
    
    await db_manager.shutdown()


@pytest.fixture
async def cache_manager():
    """Create a test cache manager with mock Redis."""
    from src.core.cache import CacheManager
    
    # Mock Redis for tests
    mock_redis = AsyncMock()
    cache_manager = CacheManager()
    cache_manager.redis = mock_redis
    
    yield cache_manager


@pytest.fixture
async def metrics_collector():
    """Create a test metrics collector."""
    from src.core.metrics import MetricsCollector
    
    metrics_collector = MetricsCollector()
    await metrics_collector.initialize()
    
    yield metrics_collector
    
    await metrics_collector.shutdown()


@pytest.fixture
async def aiohttp_session():
    """Create an aiohttp session for tests."""
    async with aiohttp.ClientSession() as session:
        yield session


@pytest.fixture
def mock_api_responses():
    """Create mock API responses for external services."""
    with aioresponses() as m:
        # CoinGecko mock responses
        m.get(
            "https://api.coingecko.com/api/v3/simple/token_price/ethereum",
            payload={
                "******************************************": {
                    "usd": 1.25,
                    "usd_24h_change": 5.2,
                    "usd_7d_change": -2.1,
                    "usd_30d_change": 15.7
                }
            }
        )
        
        # DexScreener mock responses
        m.get(
            "https://api.dexscreener.com/latest/dex/tokens/******************************************",
            payload={
                "pairs": [
                    {
                        "priceUsd": "1.24",
                        "priceChange": {"h24": 4.8, "h7": -1.9},
                        "volume": {"h24": 125000},
                        "liquidity": {"usd": 850000}
                    }
                ]
            }
        )
        
        # Etherscan mock responses
        m.get(
            "https://api.etherscan.io/api",
            payload={
                "status": "1",
                "message": "OK",
                "result": {
                    "contractName": "TestToken",
                    "symbol": "TEST",
                    "decimals": "18"
                }
            }
        )
        
        # Fear & Greed Index mock response
        m.get(
            "https://api.alternative.me/fng/",
            payload={
                "data": [
                    {
                        "value": "45",
                        "value_classification": "Fear",
                        "timestamp": "1641024000"
                    }
                ]
            }
        )
        
        yield m


@pytest.fixture
def sample_token_data():
    """Sample token data for testing."""
    return {
        "address": "******************************************",
        "chain_id": 1,
        "name": "Test Token",
        "symbol": "TEST",
        "decimals": 18,
        "total_supply": "1000000000000000000000000",
        "price_usd": 1.25,
        "market_cap": 1250000,
        "volume_24h": 125000,
        "liquidity_usd": 850000
    }


@pytest.fixture
def sample_market_data():
    """Sample market data for testing."""
    return {
        "price_usd": 1.25,
        "price_change_24h": 5.2,
        "price_change_7d": -2.1,
        "price_change_30d": 15.7,
        "volume_24h": 125000,
        "market_cap": 1250000,
        "liquidity_usd": 850000,
        "timestamp": "2025-07-09T12:00:00Z"
    }


@pytest.fixture
def sample_technical_indicators():
    """Sample technical indicators for testing."""
    return {
        "rsi": 65.4,
        "macd": {
            "macd": 0.025,
            "signal": 0.018,
            "histogram": 0.007
        },
        "bollinger_bands": {
            "upper": 1.35,
            "middle": 1.25,
            "lower": 1.15
        },
        "moving_averages": {
            "sma_20": 1.22,
            "sma_50": 1.18,
            "ema_12": 1.24,
            "ema_26": 1.20
        },
        "support_resistance": {
            "support": [1.15, 1.10],
            "resistance": [1.35, 1.40]
        }
    }


@pytest.fixture
async def mock_autogen_agents():
    """Create mock AutoGen agents for testing."""
    from unittest.mock import AsyncMock
    
    agents = {}
    agent_names = [
        "scheduler", "discovery", "chain_info", "market_data",
        "trend", "technical", "validator", "analyst", "audit"
    ]
    
    for name in agent_names:
        mock_agent = AsyncMock()
        mock_agent.name = name
        mock_agent.system_message = f"Mock {name} agent"
        agents[name] = mock_agent
    
    return agents


@pytest.fixture
def temp_data_dir():
    """Create a temporary directory for test data."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
async def rate_limiter():
    """Create a test rate limiter with relaxed limits."""
    from src.utils.rate_limit import RateLimiter, RateLimitConfig, RateLimitStrategy
    
    config = RateLimitConfig(
        requests_per_second=100,  # High limit for tests
        burst_size=200,
        strategy=RateLimitStrategy.TOKEN_BUCKET
    )
    
    return RateLimiter(config)


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "external_api: mark test as requiring external API access"
    )


# Test data generators
def generate_price_history(days: int = 30, base_price: float = 1.0):
    """Generate sample price history data."""
    import random
    from datetime import datetime, timedelta
    
    prices = []
    current_price = base_price
    
    for i in range(days):
        # Simulate price movement
        change = random.uniform(-0.1, 0.1)  # ±10% daily change
        current_price *= (1 + change)
        
        prices.append({
            "timestamp": (datetime.utcnow() - timedelta(days=days-i)).isoformat(),
            "price": round(current_price, 6),
            "volume": random.randint(10000, 100000)
        })
    
    return prices


def generate_blockchain_data(token_address: str):
    """Generate sample blockchain data."""
    return {
        "address": token_address,
        "name": "Test Token",
        "symbol": "TEST",
        "decimals": 18,
        "total_supply": "1000000000000000000000000",
        "holders_count": 1250,
        "transfers_count": 5420,
        "contract_verified": True,
        "proxy_contract": False,
        "creation_block": 18500000,
        "creation_timestamp": "2023-11-15T10:30:00Z"
    }
