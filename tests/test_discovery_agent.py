"""
Unit Tests for Discovery Agent - Production Quality
Comprehensive unit tests with real API mocking and edge case coverage
"""

import pytest
import asyncio
import aiohttp
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from agents.discovery import DiscoveryAgent
from core.database import DatabaseManager
from core.cache import CacheManager
from integrations.metrics import MetricsCollector
from agents.coordinator import AgentCoordinator


class TestDiscoveryAgent:
    """Test suite for DiscoveryAgent with comprehensive coverage."""
    
    @pytest.fixture
    async def cache_manager(self):
        """Create cache manager for testing."""
        cache = CacheManager()
        await cache.initialize()
        yield cache
        await cache.shutdown()
    
    @pytest.fixture
    async def discovery_agent(self, cache_manager):
        """Create discovery agent for testing."""
        db_manager = DatabaseManager()
        metrics_collector = MetricsCollector()
        coordinator = AgentCoordinator(db_manager, cache_manager, metrics_collector)
        
        agent = DiscoveryAgent(db_manager, cache_manager, metrics_collector, coordinator)
        await agent.initialize()
        yield agent
        await agent.shutdown()
    
    @pytest.mark.asyncio
    async def test_dexscreener_discovery_success(self, discovery_agent):
        """Test successful DexScreener token discovery."""
        
        # Mock DexScreener API response
        dexscreener_response = {
            "pairs": [
                {
                    "baseToken": {
                        "address": "0xtest1",
                        "symbol": "TEST1",
                        "name": "Test Token 1"
                    },
                    "chainId": "ethereum",
                    "priceUsd": "0.001234",
                    "marketCap": 1000000,
                    "volume": {"h24": 50000},
                    "liquidity": {"usd": 100000},
                    "priceChange": {"h24": 5.5},
                    "dexId": "uniswap_v2",
                    "pairAddress": "0xpair1"
                },
                {
                    "baseToken": {
                        "address": "0xtest2",
                        "symbol": "TEST2",
                        "name": "Test Token 2"
                    },
                    "chainId": "ethereum",
                    "priceUsd": "0.005678",
                    "marketCap": 2000000,
                    "volume": {"h24": 75000},
                    "liquidity": {"usd": 150000}
                }
            ]
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = dexscreener_response
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await discovery_agent._discover_from_dexscreener(24, 10)
            
            assert result is not None
            assert result.get('source') == 'dexscreener'
            assert 'tokens' in result
            tokens = result['tokens']
            assert len(tokens) >= 1  # Should find at least one quality token
            
            # Validate token structure
            if tokens:
                token = tokens[0]
                assert 'address' in token
                assert 'symbol' in token
                assert 'name' in token
                assert 'price_usd' in token
                assert 'volume_24h_usd' in token
                assert 'liquidity_usd' in token
    
    @pytest.mark.asyncio
    async def test_birdeye_discovery_success(self, discovery_agent):
        """Test successful Birdeye token discovery."""
        
        # Mock Birdeye API response
        birdeye_response = {
            "data": {
                "tokens": [
                    {
                        "address": "So11111111111111111111111111111111111111112",
                        "symbol": "SOL",
                        "name": "Solana",
                        "decimals": 9,
                        "price": 156.78,
                        "marketCap": 75000000000,
                        "volume24h": 2500000000,
                        "liquidity": 500000000
                    },
                    {
                        "address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                        "symbol": "USDC",
                        "name": "USD Coin",
                        "decimals": 6,
                        "price": 1.0,
                        "marketCap": 35000000000,
                        "volume24h": 8000000000,
                        "liquidity": 1000000000
                    }
                ]
            },
            "success": True
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = birdeye_response
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await discovery_agent._discover_from_birdeye(24, 10)
            
            assert result is not None
            assert result.get('source') == 'birdeye'
            assert 'tokens' in result
            tokens = result['tokens']
            assert len(tokens) >= 1
            
            # Validate Solana token structure
            if tokens:
                token = tokens[0]
                assert 'address' in token
                assert 'symbol' in token
                assert 'chain' in token
                assert token['chain'] == 'solana'
                assert 'price_usd' in token
    
    @pytest.mark.asyncio
    async def test_discovery_api_failure(self, discovery_agent):
        """Test discovery with API failures."""
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock API failure
            mock_get.side_effect = aiohttp.ClientError("API unavailable")
            
            result = await discovery_agent._discover_from_dexscreener(24, 10)
            
            # Should handle failure gracefully
            assert result is not None
            assert result.get('tokens') == []  # Empty list on failure
            assert result.get('source') == 'dexscreener'
    
    @pytest.mark.asyncio
    async def test_token_filtering(self, discovery_agent):
        """Test token filtering logic."""
        
        # Mock tokens with various quality levels
        mock_tokens = [
            {
                "symbol": "GOOD",
                "name": "Good Token",
                "address": "0xgood",
                "chain": "ethereum",
                "price_usd": 1.0,
                "volume_24h_usd": 100000,  # High volume
                "liquidity_usd": 200000,   # High liquidity
                "market_cap_usd": 5000000,
                "discovered_at": datetime.now()
            },
            {
                "symbol": "BAD",
                "name": "Bad Token",
                "address": "0xbad",
                "chain": "ethereum",
                "price_usd": 0.001,
                "volume_24h_usd": 100,     # Low volume
                "liquidity_usd": 500,      # Low liquidity
                "market_cap_usd": 10000,
                "discovered_at": datetime.now()
            },
            {
                "symbol": "",  # Invalid symbol
                "name": "Invalid Token",
                "address": "",  # Invalid address
                "chain": "ethereum",
                "price_usd": 0,
                "volume_24h_usd": 0,
                "liquidity_usd": 0,
                "market_cap_usd": 0,
                "discovered_at": datetime.now()
            }
        ]
        
        filtered_tokens = discovery_agent._filter_tokens(mock_tokens, 24)
        
        # Should filter out low quality and invalid tokens
        assert len(filtered_tokens) == 1
        assert filtered_tokens[0]['symbol'] == 'GOOD'
    
    @pytest.mark.asyncio
    async def test_multi_source_discovery(self, discovery_agent):
        """Test discovery from multiple sources."""
        
        # Mock responses for different sources
        dex_response = {"pairs": []}
        birdeye_response = {"data": {"tokens": []}, "success": True}
        
        with patch.object(discovery_agent, '_discover_from_dexscreener') as mock_dex, \
             patch.object(discovery_agent, '_discover_from_birdeye') as mock_birdeye:
            
            mock_dex.return_value = {
                'tokens': [{'symbol': 'DEX1', 'address': '0xdex1', 'source': 'dexscreener'}],
                'source': 'dexscreener'
            }
            
            mock_birdeye.return_value = {
                'tokens': [{'symbol': 'BIRD1', 'address': 'bird1', 'source': 'birdeye'}],
                'source': 'birdeye'
            }
            
            result = await discovery_agent.discover_tokens(
                sources=['dexscreener', 'birdeye'],
                min_age_hours=24,
                limit=20
            )
            
            assert result is not None
            assert 'tokens' in result
            assert 'source_results' in result
            
            # Should have results from both sources
            assert 'dexscreener' in result['source_results']
            assert 'birdeye' in result['source_results']
            
            # Should combine tokens from all sources
            tokens = result['tokens']
            assert len(tokens) >= 1
    
    @pytest.mark.asyncio
    async def test_rate_limiting_integration(self, discovery_agent):
        """Test rate limiting integration."""
        
        with patch('src.utils.rate_limit._global_rate_limiter.acquire') as mock_acquire:
            mock_acquire.return_value = None
            
            # Mock successful API response
            with patch('aiohttp.ClientSession.get') as mock_get:
                mock_response = AsyncMock()
                mock_response.status = 200
                mock_response.json.return_value = {"pairs": []}
                mock_get.return_value.__aenter__.return_value = mock_response
                
                await discovery_agent._discover_from_dexscreener(24, 5)
                
                # Should acquire rate limit token
                assert mock_acquire.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_caching_behavior(self, discovery_agent):
        """Test caching behavior for discovery results."""
        
        with patch.object(discovery_agent, '_discover_from_dexscreener') as mock_discover:
            mock_discover.return_value = {
                'tokens': [{'symbol': 'TEST', 'address': '0xtest'}],
                'source': 'dexscreener'
            }
            
            # First call
            result1 = await discovery_agent.discover_tokens(['dexscreener'], 24, 5)
            
            # Second call (may use cache)
            result2 = await discovery_agent.discover_tokens(['dexscreener'], 24, 5)
            
            # Should have consistent results
            assert result1 is not None
            assert result2 is not None
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, discovery_agent):
        """Test error handling with circuit breakers."""
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Simulate service failure
            mock_get.side_effect = aiohttp.ServerTimeoutError("Timeout")
            
            result = await discovery_agent._discover_from_dexscreener(24, 5)
            
            # Should handle errors gracefully
            assert result is not None
            assert result.get('tokens') == []
            assert 'error' in result or result.get('source') == 'dexscreener'
    
    @pytest.mark.asyncio
    async def test_performance_requirements(self, discovery_agent):
        """Test performance requirements for discovery."""
        
        with patch.object(discovery_agent, '_discover_from_dexscreener') as mock_discover:
            # Mock fast response
            mock_discover.return_value = {
                'tokens': [{'symbol': 'FAST', 'address': '0xfast'}],
                'source': 'dexscreener'
            }
            
            start_time = datetime.now()
            result = await discovery_agent.discover_tokens(['dexscreener'], 24, 10)
            end_time = datetime.now()
            
            discovery_time = (end_time - start_time).total_seconds()
            
            # Should complete quickly (under 30 seconds)
            assert discovery_time < 30.0
            assert result is not None
    
    @pytest.mark.asyncio
    async def test_edge_cases(self, discovery_agent):
        """Test edge cases and boundary conditions."""
        
        # Test with empty sources list
        result = await discovery_agent.discover_tokens([], 24, 10)
        assert result is not None
        assert result.get('tokens') == []
        
        # Test with invalid source
        result = await discovery_agent.discover_tokens(['invalid_source'], 24, 10)
        assert result is not None
        
        # Test with zero limit
        result = await discovery_agent.discover_tokens(['dexscreener'], 24, 0)
        assert result is not None
        assert len(result.get('tokens', [])) == 0
    
    def test_token_validation(self, discovery_agent):
        """Test token validation logic."""
        
        # Valid token
        valid_token = {
            'symbol': 'VALID',
            'name': 'Valid Token',
            'address': '******************************************',
            'chain': 'ethereum',
            'price_usd': 1.0,
            'volume_24h_usd': 10000,
            'liquidity_usd': 50000
        }
        
        # Invalid tokens
        invalid_tokens = [
            {'symbol': '', 'address': '0x123'},  # Empty symbol
            {'symbol': 'TEST', 'address': ''},   # Empty address
            {'symbol': 'TEST', 'address': '0x123', 'price_usd': -1},  # Negative price
        ]
        
        # Test validation (assuming there's a validation method)
        # This would test internal validation logic
        assert valid_token.get('symbol') != ''
        assert valid_token.get('address') != ''
        assert valid_token.get('price_usd', 0) >= 0


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
