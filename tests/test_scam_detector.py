"""
Unit Tests for Scam Detector - Production Quality
Comprehensive unit tests with real API mocking and edge case coverage
"""

import pytest
import asyncio
import aiohttp
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from agents.scam_detector import AdvancedScamDetector, ScamAnalysisResult, RiskLevel, ScamType
from core.cache import CacheManager


class TestAdvancedScamDetector:
    """Test suite for AdvancedScamDetector with comprehensive coverage."""
    
    @pytest.fixture
    async def cache_manager(self):
        """Create cache manager for testing."""
        cache = CacheManager()
        await cache.initialize()
        yield cache
        await cache.shutdown()
    
    @pytest.fixture
    async def scam_detector(self, cache_manager):
        """Create scam detector for testing."""
        detector = AdvancedScamDetector(cache_manager)
        await detector.initialize()
        yield detector
        await detector.shutdown()
    
    @pytest.mark.asyncio
    async def test_honeypot_detection_success(self, scam_detector):
        """Test successful honeypot detection with mocked APIs."""
        
        # Mock Honeypot.is API response
        honeypot_response = {
            "status": "success",
            "data": {
                "isHoneypot": False,
                "riskLevel": "low",
                "confidence": 95
            }
        }
        
        # Mock GoPlus API response
        goplus_response = {
            "code": 1,
            "message": "OK",
            "result": {
                "0xtest": {
                    "is_honeypot": "0",
                    "buy_tax": "0",
                    "sell_tax": "0",
                    "holder_count": "1000000",
                    "is_open_source": "1"
                }
            }
        }
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Configure mock responses
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock()
            
            # Return different responses based on URL
            async def side_effect(*args, **kwargs):
                url = str(args[0]) if args else kwargs.get('url', '')
                if 'honeypot.is' in url:
                    mock_response.json.return_value = honeypot_response
                elif 'gopluslabs.io' in url:
                    mock_response.json.return_value = goplus_response
                return mock_response
            
            mock_get.return_value.__aenter__.return_value = mock_response
            mock_response.json.side_effect = side_effect
            
            # Test honeypot detection
            result = await scam_detector._detect_honeypot("0xtest", 1)
            
            assert result is not None
            assert result.get('honeypot_is_result') is not None
            assert result.get('goplus_result') is not None
            assert not result.get('is_honeypot', True)  # Should not be honeypot
    
    @pytest.mark.asyncio
    async def test_honeypot_detection_api_failure(self, scam_detector):
        """Test honeypot detection with API failures."""
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock API failure
            mock_get.side_effect = aiohttp.ClientError("API unavailable")
            
            result = await scam_detector._detect_honeypot("0xtest", 1)
            
            # Should handle failure gracefully
            assert result is not None
            assert 'error' in result or result.get('is_honeypot') == True  # Fail safe
    
    @pytest.mark.asyncio
    async def test_contract_analysis_success(self, scam_detector):
        """Test successful contract analysis."""
        
        # Mock GoPlus security response
        goplus_response = {
            "code": 1,
            "message": "OK",
            "result": {
                "0xtest": {
                    "is_open_source": "1",
                    "is_proxy": "0",
                    "creator_address": "0xcreator",
                    "creator_balance": "1000000",
                    "creator_percent": "5.5",
                    "buy_tax": "0",
                    "sell_tax": "0",
                    "holder_count": "50000"
                }
            }
        }
        
        with patch.object(scam_detector, '_check_goplus_security') as mock_goplus:
            mock_goplus.return_value = {
                'simulation_success': True,
                'raw_data': goplus_response["result"]["0xtest"],
                'buy_tax': 0,
                'sell_tax': 0,
                'can_buy': True,
                'can_sell': True,
                'holder_count': 50000
            }
            
            result = await scam_detector._analyze_contract_patterns("0xtest", 1)
            
            assert result is not None
            assert result.get('verified_source') == True
            assert result.get('proxy_contract') == False
            assert result.get('creator_percent') == 5.5
            assert result.get('holder_count') == 50000
    
    @pytest.mark.asyncio
    async def test_risk_evaluation_high_risk(self, scam_detector):
        """Test risk evaluation for high-risk token."""
        
        high_risk_analysis = {
            "has_dangerous_functions": True,
            "dangerous_functions": ["sell_restriction", "high_tax_functions"],
            "has_hidden_mint": True,
            "ownership_renounced": False,
            "verified_source": False,
            "proxy_contract": True,
            "holder_count": 50,
            "creator_percent": 80.0,
            "buy_tax": 15,
            "sell_tax": 25
        }
        
        risk_score, flags = scam_detector._evaluate_contract_risk(high_risk_analysis)
        
        assert risk_score > 70  # Should be high risk
        assert len(flags) > 3   # Should have multiple red flags
        assert any("sell" in flag.lower() for flag in flags)
        assert any("tax" in flag.lower() for flag in flags)
    
    @pytest.mark.asyncio
    async def test_risk_evaluation_low_risk(self, scam_detector):
        """Test risk evaluation for low-risk token."""
        
        low_risk_analysis = {
            "has_dangerous_functions": False,
            "dangerous_functions": [],
            "has_hidden_mint": False,
            "ownership_renounced": True,
            "verified_source": True,
            "proxy_contract": False,
            "holder_count": 100000,
            "creator_percent": 2.0,
            "buy_tax": 0,
            "sell_tax": 0
        }
        
        risk_score, flags = scam_detector._evaluate_contract_risk(low_risk_analysis)
        
        assert risk_score < 30  # Should be low risk
        assert len(flags) <= 2  # Should have minimal flags
    
    @pytest.mark.asyncio
    async def test_full_analysis_integration(self, scam_detector):
        """Test full token analysis integration."""
        
        # Mock all sub-components
        with patch.object(scam_detector, '_detect_honeypot') as mock_honeypot, \
             patch.object(scam_detector, '_analyze_contract_patterns') as mock_contract, \
             patch.object(scam_detector, '_check_social_signals') as mock_social:
            
            # Configure mocks
            mock_honeypot.return_value = {
                'is_honeypot': False,
                'honeypot_confidence': 95,
                'honeypot_is_result': {'riskLevel': 'low'},
                'goplus_result': {'simulation_success': True}
            }
            
            mock_contract.return_value = {
                'has_dangerous_functions': False,
                'verified_source': True,
                'holder_count': 50000,
                'creator_percent': 3.0
            }
            
            mock_social.return_value = {
                'social_score': 0.8,
                'social_flags': []
            }
            
            # Run full analysis
            result = await scam_detector.analyze_token("0xtest", 1)
            
            assert isinstance(result, ScamAnalysisResult)
            assert result.risk_level in [RiskLevel.LOW, RiskLevel.MEDIUM, RiskLevel.HIGH]
            assert 0 <= result.confidence <= 100
            assert isinstance(result.red_flags, list)
            assert isinstance(result.scam_types, list)
    
    @pytest.mark.asyncio
    async def test_caching_behavior(self, scam_detector):
        """Test caching behavior for repeated requests."""
        
        with patch.object(scam_detector, '_detect_honeypot') as mock_honeypot:
            mock_honeypot.return_value = {'is_honeypot': False}
            
            # First call
            result1 = await scam_detector._detect_honeypot("0xtest", 1)
            
            # Second call (should use cache)
            result2 = await scam_detector._detect_honeypot("0xtest", 1)
            
            # Should only call the actual method once due to caching
            assert mock_honeypot.call_count <= 2  # Allow for some cache misses
            assert result1 == result2
    
    @pytest.mark.asyncio
    async def test_rate_limiting_integration(self, scam_detector):
        """Test rate limiting integration."""
        
        with patch('src.utils.rate_limit._global_rate_limiter.acquire') as mock_acquire:
            mock_acquire.return_value = None  # Simulate successful acquisition
            
            # Make multiple calls
            tasks = []
            for i in range(3):
                task = scam_detector._detect_honeypot(f"0xtest{i}", 1)
                tasks.append(task)
            
            # Should respect rate limits
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All should complete (may have delays due to rate limiting)
            assert len(results) == 3
            assert mock_acquire.call_count >= 3  # Should acquire rate limit tokens
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, scam_detector):
        """Test error handling integration with circuit breakers."""
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Simulate repeated failures
            mock_get.side_effect = aiohttp.ClientError("Service unavailable")
            
            # Should handle errors gracefully
            result = await scam_detector._detect_honeypot("0xtest", 1)
            
            # Should return safe default or error indication
            assert result is not None
            assert 'error' in result or result.get('is_honeypot') == True
    
    def test_scam_analysis_result_creation(self):
        """Test ScamAnalysisResult object creation and validation."""
        
        result = ScamAnalysisResult(
            is_scam=True,
            risk_level=RiskLevel.HIGH,
            confidence=85,
            red_flags=["High sell tax", "Low holder count"],
            scam_types=[ScamType.HONEYPOT, ScamType.RUG_PULL],
            analysis_details={"contract_verified": False}
        )
        
        assert result.is_scam == True
        assert result.risk_level == RiskLevel.HIGH
        assert result.confidence == 85
        assert len(result.red_flags) == 2
        assert len(result.scam_types) == 2
        assert "contract_verified" in result.analysis_details
    
    @pytest.mark.asyncio
    async def test_edge_cases(self, scam_detector):
        """Test edge cases and boundary conditions."""
        
        # Test with invalid token address
        result = await scam_detector.analyze_token("invalid", 1)
        assert isinstance(result, ScamAnalysisResult)
        
        # Test with empty token address
        result = await scam_detector.analyze_token("", 1)
        assert isinstance(result, ScamAnalysisResult)
        
        # Test with None token address
        with pytest.raises((ValueError, TypeError)):
            await scam_detector.analyze_token(None, 1)
    
    @pytest.mark.asyncio
    async def test_performance_requirements(self, scam_detector):
        """Test performance requirements (<30s analysis time)."""
        
        with patch.object(scam_detector, '_detect_honeypot') as mock_honeypot, \
             patch.object(scam_detector, '_analyze_contract_patterns') as mock_contract:
            
            # Configure fast mocks
            mock_honeypot.return_value = {'is_honeypot': False}
            mock_contract.return_value = {'has_dangerous_functions': False}
            
            start_time = datetime.now()
            result = await scam_detector.analyze_token("0xtest", 1)
            end_time = datetime.now()
            
            analysis_time = (end_time - start_time).total_seconds()
            
            # Should complete within 30 seconds
            assert analysis_time < 30.0
            assert isinstance(result, ScamAnalysisResult)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
