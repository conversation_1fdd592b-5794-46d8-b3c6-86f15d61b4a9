# Great Expectations configuration for token analyzer data validation
config_version: 3.0

# Data context configuration
datasources:
  token_data_source:
    class_name: Datasource
    execution_engine:
      class_name: PandasExecutionEngine
    data_connectors:
      default_runtime_data_connector:
        class_name: RuntimeDataConnector
        batch_identifiers:
          - default_identifier_name
      api_data_connector:
        class_name: RuntimeDataConnector
        batch_identifiers:
          - api_name
          - timestamp

# Store configuration
stores:
  expectations_store:
    class_name: ExpectationsStore
    store_backend:
      class_name: TupleFilesystemStoreBackend
      base_directory: tests/great_expectations/expectations/

  validations_store:
    class_name: ValidationsStore
    store_backend:
      class_name: TupleFilesystemStoreBackend
      base_directory: tests/great_expectations/uncommitted/validations/

  evaluation_parameter_store:
    class_name: EvaluationParameterStore

  checkpoint_store:
    class_name: CheckpointStore
    store_backend:
      class_name: TupleFilesystemStoreBackend
      base_directory: tests/great_expectations/checkpoints/

# Data documentation
data_docs_sites:
  local_site:
    class_name: <PERSON><PERSON><PERSON>er
    show_how_to_buttons: true
    store_backend:
      class_name: TupleFilesystemStoreBackend
      base_directory: tests/great_expectations/uncommitted/data_docs/local_site/
    site_index_builder:
      class_name: DefaultSiteIndexBuilder

# Validation operators
validation_operators:
  action_list_operator:
    class_name: ActionListValidationOperator
    action_list:
      - name: store_validation_result
        action:
          class_name: StoreValidationResultAction
      - name: store_evaluation_params
        action:
          class_name: StoreEvaluationParametersAction
      - name: update_data_docs
        action:
          class_name: UpdateDataDocsAction

# Anonymous usage statistics
anonymous_usage_statistics:
  enabled: false
