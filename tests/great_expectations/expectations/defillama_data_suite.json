{"data_asset_type": null, "expectation_suite_name": "defillama_data_suite", "expectations": [{"expectation_type": "expect_table_columns_to_match_ordered_list", "kwargs": {"column_list": ["id", "name", "address", "symbol", "decimals", "chainId", "logoURI", "tags"]}, "meta": {"notes": {"format": "markdown", "content": "DeFiLlama token data should have consistent column structure"}}}, {"expectation_type": "expect_column_values_to_not_be_null", "kwargs": {"column": "id"}, "meta": {"notes": {"format": "markdown", "content": "Token ID should never be null"}}}, {"expectation_type": "expect_column_values_to_not_be_null", "kwargs": {"column": "name"}, "meta": {"notes": {"format": "markdown", "content": "Token name should never be null"}}}, {"expectation_type": "expect_column_values_to_not_be_null", "kwargs": {"column": "symbol"}, "meta": {"notes": {"format": "markdown", "content": "Token symbol should never be null"}}}, {"expectation_type": "expect_column_values_to_match_regex", "kwargs": {"column": "address", "regex": "^0x[a-fA-F0-9]{40}$"}, "meta": {"notes": {"format": "markdown", "content": "Token addresses should be valid Ethereum addresses"}}}, {"expectation_type": "expect_column_values_to_be_between", "kwargs": {"column": "decimals", "min_value": 0, "max_value": 18}, "meta": {"notes": {"format": "markdown", "content": "Token decimals should be between 0 and 18"}}}, {"expectation_type": "expect_column_values_to_be_in_set", "kwargs": {"column": "chainId", "value_set": [1, 56, 137, 250, 43114, 42161, 10]}, "meta": {"notes": {"format": "markdown", "content": "Chain ID should be from supported networks"}}}, {"expectation_type": "expect_column_values_to_match_regex", "kwargs": {"column": "symbol", "regex": "^[A-Z0-9]{1,10}$"}, "meta": {"notes": {"format": "markdown", "content": "Token symbols should be uppercase alphanumeric, 1-10 characters"}}}], "ge_cloud_id": null, "meta": {"great_expectations_version": "0.15.0", "notes": {"format": "markdown", "content": "# DeFiLlama Data Validation Suite\n\nThis suite validates the structure and content of DeFiLlama API responses to ensure data quality and consistency.\n\n## Validation Rules\n\n1. **Schema Validation**: Ensures consistent column structure\n2. **Required Fields**: Validates that critical fields are not null\n3. **Address Format**: Validates Ethereum address format\n4. **Decimal Range**: Ensures decimals are within valid range\n5. **Chain Support**: Validates supported blockchain networks\n6. **Symbol Format**: Ensures token symbols follow standard format\n\n## Usage\n\n```python\nfrom great_expectations import DataContext\n\ncontext = DataContext()\nsuite = context.get_expectation_suite(\"defillama_data_suite\")\n```"}}}