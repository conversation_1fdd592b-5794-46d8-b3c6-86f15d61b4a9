{"data_asset_type": null, "expectation_suite_name": "price_data_suite", "expectations": [{"expectation_type": "expect_table_columns_to_match_ordered_list", "kwargs": {"column_list": ["timestamp", "price_usd", "volume_24h", "market_cap", "price_change_24h", "price_change_7d", "price_change_30d"]}, "meta": {"notes": {"format": "markdown", "content": "Price data should have consistent column structure"}}}, {"expectation_type": "expect_column_values_to_not_be_null", "kwargs": {"column": "timestamp"}, "meta": {"notes": {"format": "markdown", "content": "Timestamp should never be null"}}}, {"expectation_type": "expect_column_values_to_not_be_null", "kwargs": {"column": "price_usd"}, "meta": {"notes": {"format": "markdown", "content": "Price in USD should never be null"}}}, {"expectation_type": "expect_column_values_to_be_between", "kwargs": {"column": "price_usd", "min_value": 0, "max_value": 1000000}, "meta": {"notes": {"format": "markdown", "content": "Price should be positive and within reasonable bounds"}}}, {"expectation_type": "expect_column_values_to_be_between", "kwargs": {"column": "volume_24h", "min_value": 0, "max_value": 10000000000}, "meta": {"notes": {"format": "markdown", "content": "24h volume should be non-negative and within reasonable bounds"}}}, {"expectation_type": "expect_column_values_to_be_between", "kwargs": {"column": "market_cap", "min_value": 0, "max_value": 100000000000000}, "meta": {"notes": {"format": "markdown", "content": "Market cap should be non-negative and within reasonable bounds"}}}, {"expectation_type": "expect_column_values_to_be_between", "kwargs": {"column": "price_change_24h", "min_value": -99, "max_value": 1000}, "meta": {"notes": {"format": "markdown", "content": "24h price change should be within reasonable bounds (-99% to +1000%)"}}}, {"expectation_type": "expect_column_values_to_be_between", "kwargs": {"column": "price_change_7d", "min_value": -99, "max_value": 5000}, "meta": {"notes": {"format": "markdown", "content": "7d price change should be within reasonable bounds"}}}, {"expectation_type": "expect_column_values_to_be_between", "kwargs": {"column": "price_change_30d", "min_value": -99, "max_value": 10000}, "meta": {"notes": {"format": "markdown", "content": "30d price change should be within reasonable bounds"}}}, {"expectation_type": "expect_column_values_to_match_regex", "kwargs": {"column": "timestamp", "regex": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?Z?$"}, "meta": {"notes": {"format": "markdown", "content": "Timestamp should be in ISO 8601 format"}}}, {"expectation_type": "expect_column_values_to_be_increasing", "kwargs": {"column": "timestamp", "strictly": false}, "meta": {"notes": {"format": "markdown", "content": "Timestamps should be monotonically increasing (or equal)"}}}], "ge_cloud_id": null, "meta": {"great_expectations_version": "0.15.0", "notes": {"format": "markdown", "content": "# Price Data Validation Suite\n\nThis suite validates price data from various sources (CoinGecko, DexScreener, etc.) to ensure data quality and consistency.\n\n## Validation Rules\n\n1. **Schema Validation**: Ensures consistent column structure\n2. **Required Fields**: Validates that critical fields are not null\n3. **Price Bounds**: Ensures prices are positive and within reasonable limits\n4. **Volume Bounds**: Validates trading volume is non-negative\n5. **Market Cap Bounds**: Ensures market cap is within reasonable limits\n6. **Price Change Bounds**: Validates price changes are within realistic ranges\n7. **Timestamp Format**: Ensures timestamps follow ISO 8601 format\n8. **Timestamp Monotonicity**: Ensures timestamps are in chronological order\n\n## Usage\n\n```python\nimport pandas as pd\nfrom great_expectations import DataContext\n\ncontext = DataContext()\nsuite = context.get_expectation_suite(\"price_data_suite\")\n\n# Validate price data\ndf = pd.DataFrame(price_data)\nbatch = context.get_batch(df, \"price_data_suite\")\nresults = context.run_validation_operator(\"action_list_operator\", [batch])\n```"}}}