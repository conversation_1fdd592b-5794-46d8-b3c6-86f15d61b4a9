#!/usr/bin/env python3
"""
Core security component tests without complex dependencies.
Tests the fundamental security algorithms and patterns.
"""

import asyncio
import hashlib
import hmac
import ipaddress
import json
import sys
import time
import threading
from collections import defaultdict, deque
from datetime import datetime, timezone, timedelta
from enum import Enum
from pathlib import Path


def test_request_signature_core():
    """Test core request signature functionality."""
    print("🧪 Testing Request Signature Core...")
    
    class RequestSignature:
        def __init__(self, secret_key: str):
            self.secret_key = secret_key.encode('utf-8')
        
        def generate_signature(self, method: str, path: str, body: str, 
                              timestamp: str, nonce: str) -> str:
            message = f"{method}|{path}|{body}|{timestamp}|{nonce}"
            signature = hmac.new(
                self.secret_key,
                message.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            return signature
        
        def validate_signature(self, method: str, path: str, body: str,
                              timestamp: str, nonce: str, signature: str) -> bool:
            expected_signature = self.generate_signature(method, path, body, timestamp, nonce)
            return hmac.compare_digest(signature, expected_signature)
        
        def validate_timestamp(self, timestamp: str, max_age_seconds: int = 300) -> bool:
            try:
                request_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                current_time = datetime.now(timezone.utc)
                age = (current_time - request_time).total_seconds()
                return 0 <= age <= max_age_seconds
            except (ValueError, TypeError):
                return False
    
    signer = RequestSignature("test-secret-key")
    
    # Test signature generation and validation
    method = "POST"
    path = "/api/analyze"
    body = '{"token": "0x123"}'
    timestamp = "2025-01-01T00:00:00Z"
    nonce = "test-nonce-123"
    
    signature = signer.generate_signature(method, path, body, timestamp, nonce)
    assert isinstance(signature, str)
    assert len(signature) == 64  # SHA256 hex digest length
    
    # Test valid signature
    is_valid = signer.validate_signature(method, path, body, timestamp, nonce, signature)
    assert is_valid == True
    
    # Test invalid signature
    is_valid = signer.validate_signature(method, path, body, timestamp, nonce, "invalid")
    assert is_valid == False
    
    # Test timestamp validation
    current_time = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')
    assert signer.validate_timestamp(current_time) == True
    
    old_time = "2020-01-01T00:00:00Z"
    assert signer.validate_timestamp(old_time) == False
    
    print("✅ Request Signature Core tests passed")
    return True


def test_ip_whitelist_core():
    """Test core IP whitelisting functionality."""
    print("🧪 Testing IP Whitelist Core...")
    
    class IPWhitelist:
        def __init__(self):
            self.whitelist = set()
            self.blacklist = set()
            self._add_default_whitelist()
        
        def _add_default_whitelist(self):
            default_whitelist = [
                "*********/8",    # Localhost
                "10.0.0.0/8",     # Private network
                "**********/12",  # Private network
                "***********/16", # Private network
            ]
            for network in default_whitelist:
                self.add_to_whitelist(network)
        
        def add_to_whitelist(self, network: str):
            try:
                network_obj = ipaddress.IPv4Network(network, strict=False)
                self.whitelist.add(network_obj)
            except ValueError:
                pass
        
        def add_to_blacklist(self, network: str):
            try:
                network_obj = ipaddress.IPv4Network(network, strict=False)
                self.blacklist.add(network_obj)
            except ValueError:
                pass
        
        def is_allowed(self, ip_address: str) -> bool:
            try:
                ip = ipaddress.IPv4Address(ip_address)
                
                # Check blacklist first
                for network in self.blacklist:
                    if ip in network:
                        return False
                
                # Check whitelist
                for network in self.whitelist:
                    if ip in network:
                        return True
                
                return False
            except ValueError:
                return False
    
    whitelist = IPWhitelist()
    
    # Test default whitelist
    assert whitelist.is_allowed("127.0.0.1") == True
    assert whitelist.is_allowed("***********") == True
    
    # Test adding to whitelist
    whitelist.add_to_whitelist("***********/24")
    assert whitelist.is_allowed("***********") == True
    assert whitelist.is_allowed("*************") == True
    
    # Test blacklist
    whitelist.add_to_blacklist("*************/32")
    assert whitelist.is_allowed("*************") == False
    assert whitelist.is_allowed("*************") == True
    
    # Test invalid IP
    assert whitelist.is_allowed("invalid-ip") == False
    
    print("✅ IP Whitelist Core tests passed")
    return True


def test_rate_limiter_core():
    """Test core rate limiting functionality."""
    print("🧪 Testing Rate Limiter Core...")
    
    class AdvancedRateLimiter:
        def __init__(self):
            self.token_buckets = defaultdict(dict)
            self.sliding_windows = defaultdict(lambda: deque(maxlen=1000))
            self.user_limits = defaultdict(dict)
            self.default_limits = {
                "requests_per_minute": 60,
                "requests_per_hour": 1000,
                "burst_limit": 10
            }
        
        def set_user_limits(self, user_id: str, limits: dict):
            self.user_limits[user_id] = limits
        
        def check_rate_limit(self, identifier: str, user_id: str = None) -> bool:
            current_time = time.time()
            limits = self.user_limits.get(user_id, self.default_limits)
            
            # Token bucket for burst control
            if not self._check_token_bucket(identifier, limits.get("burst_limit", 10), current_time):
                return False
            
            # Sliding window for sustained rate
            if not self._check_sliding_window(identifier, limits.get("requests_per_minute", 60), 60, current_time):
                return False
            
            return True
        
        def _check_token_bucket(self, identifier: str, limit: int, current_time: float) -> bool:
            bucket = self.token_buckets[identifier]
            
            if "tokens" not in bucket:
                bucket["tokens"] = limit
                bucket["last_refill"] = current_time
            
            # Refill tokens
            time_passed = current_time - bucket["last_refill"]
            tokens_to_add = time_passed * (limit / 60)
            bucket["tokens"] = min(limit, bucket["tokens"] + tokens_to_add)
            bucket["last_refill"] = current_time
            
            if bucket["tokens"] >= 1:
                bucket["tokens"] -= 1
                return True
            
            return False
        
        def _check_sliding_window(self, identifier: str, limit: int, window_seconds: int, current_time: float) -> bool:
            window = self.sliding_windows[identifier]
            
            # Remove old entries
            cutoff_time = current_time - window_seconds
            while window and window[0] < cutoff_time:
                window.popleft()
            
            if len(window) < limit:
                window.append(current_time)
                return True
            
            return False
    
    limiter = AdvancedRateLimiter()
    
    # Test basic rate limiting
    identifier = "test-user-1"
    
    # Should allow initial requests
    allowed_count = 0
    for i in range(15):
        if limiter.check_rate_limit(identifier):
            allowed_count += 1
        else:
            break
    
    assert allowed_count > 0 and allowed_count <= 10  # Should hit burst limit
    
    # Test custom user limits
    limiter.set_user_limits("premium-user", {"burst_limit": 20, "requests_per_minute": 120})
    
    premium_allowed = 0
    for i in range(25):
        if limiter.check_rate_limit("premium-user", "premium-user"):
            premium_allowed += 1
        else:
            break
    
    assert premium_allowed > allowed_count  # Premium user gets more requests
    
    print("✅ Rate Limiter Core tests passed")
    return True


def test_threat_detector_core():
    """Test core threat detection functionality."""
    print("🧪 Testing Threat Detector Core...")
    
    class ThreatLevel(Enum):
        LOW = "low"
        MEDIUM = "medium"
        HIGH = "high"
        CRITICAL = "critical"
    
    class ThreatDetector:
        def __init__(self):
            self.suspicious_patterns = {
                "sql_injection": [
                    r"union\s+select", r"drop\s+table", r"insert\s+into",
                    r"delete\s+from", r"update\s+set", r"exec\s*\("
                ],
                "xss": [
                    r"<script", r"javascript:", r"onerror=", r"onload=",
                    r"eval\s*\(", r"document\.cookie"
                ],
                "path_traversal": [
                    r"\.\./", r"\.\.\\", r"%2e%2e%2f", r"%2e%2e%5c"
                ]
            }
            self.threat_scores = defaultdict(float)
            self.request_history = defaultdict(lambda: deque(maxlen=100))
        
        def analyze_request(self, request_data: dict, ip_address: str = None) -> ThreatLevel:
            threat_score = 0.0
            detected_threats = []
            
            # Analyze request content
            content = json.dumps(request_data).lower()
            
            for threat_type, patterns in self.suspicious_patterns.items():
                import re
                for pattern in patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        threat_score += 10.0
                        detected_threats.append(threat_type)
            
            # Analyze request frequency
            if ip_address:
                history = self.request_history[ip_address]
                history.append(time.time())
                
                recent_requests = [t for t in history if time.time() - t < 60]
                if len(recent_requests) > 100:
                    threat_score += 20.0
                    detected_threats.append("rapid_requests")
            
            # Update threat score
            identifier = ip_address or "unknown"
            self.threat_scores[identifier] = max(self.threat_scores[identifier] * 0.9, threat_score)
            
            # Determine threat level
            total_score = self.threat_scores[identifier]
            if total_score >= 50:
                return ThreatLevel.CRITICAL
            elif total_score >= 30:
                return ThreatLevel.HIGH
            elif total_score >= 10:
                return ThreatLevel.MEDIUM
            else:
                return ThreatLevel.LOW
    
    detector = ThreatDetector()
    
    # Test clean request
    clean_request = {
        "method": "GET",
        "path": "/api/status",
        "query": "",
        "body": ""
    }
    
    threat_level = detector.analyze_request(clean_request, "*************")
    assert threat_level == ThreatLevel.LOW
    
    # Test SQL injection
    sql_injection_request = {
        "method": "POST",
        "path": "/api/search",
        "body": "query='; DROP TABLE users; --"
    }
    
    threat_level = detector.analyze_request(sql_injection_request, "*************")
    assert threat_level in [ThreatLevel.MEDIUM, ThreatLevel.HIGH, ThreatLevel.CRITICAL]
    
    # Test XSS
    xss_request = {
        "method": "POST",
        "path": "/api/comment",
        "body": "content=<script>alert('xss')</script>"
    }
    
    threat_level = detector.analyze_request(xss_request, "*************")
    assert threat_level in [ThreatLevel.MEDIUM, ThreatLevel.HIGH, ThreatLevel.CRITICAL]
    
    print("✅ Threat Detector Core tests passed")
    return True


def test_input_validator_core():
    """Test core input validation functionality."""
    print("🧪 Testing Input Validator Core...")
    
    class InputValidator:
        def __init__(self):
            self.max_string_length = 10000
            self.max_array_length = 1000
            self.max_object_depth = 10
        
        def validate_and_sanitize(self, data, depth: int = 0):
            if depth > self.max_object_depth:
                raise ValueError(f"Object depth exceeds maximum of {self.max_object_depth}")
            
            if isinstance(data, str):
                return self._sanitize_string(data)
            elif isinstance(data, dict):
                return self._sanitize_dict(data, depth)
            elif isinstance(data, list):
                return self._sanitize_list(data, depth)
            elif isinstance(data, (int, float, bool)) or data is None:
                return data
            else:
                return self._sanitize_string(str(data))
        
        def _sanitize_string(self, value: str) -> str:
            if len(value) > self.max_string_length:
                raise ValueError(f"String length exceeds maximum of {self.max_string_length}")
            
            # Remove null bytes and control characters
            sanitized = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
            
            # Basic HTML/script tag removal
            import re
            sanitized = re.sub(r'<script[^>]*>.*?</script>', '', sanitized, flags=re.IGNORECASE | re.DOTALL)
            sanitized = re.sub(r'<[^>]+>', '', sanitized)
            
            return sanitized
        
        def _sanitize_dict(self, data: dict, depth: int) -> dict:
            if len(data) > 100:
                raise ValueError("Dictionary has too many keys")
            
            sanitized = {}
            for key, value in data.items():
                sanitized_key = self._sanitize_string(str(key))
                sanitized_value = self.validate_and_sanitize(value, depth + 1)
                sanitized[sanitized_key] = sanitized_value
            
            return sanitized
        
        def _sanitize_list(self, data: list, depth: int) -> list:
            if len(data) > self.max_array_length:
                raise ValueError(f"Array length exceeds maximum of {self.max_array_length}")
            
            return [self.validate_and_sanitize(item, depth + 1) for item in data]
    
    validator = InputValidator()
    
    # Test string sanitization
    clean_string = "Hello, World!"
    sanitized = validator.validate_and_sanitize(clean_string)
    assert sanitized == clean_string
    
    # Test HTML removal
    html_string = "Hello <script>alert('xss')</script> World!"
    sanitized = validator.validate_and_sanitize(html_string)
    assert "<script>" not in sanitized
    assert "Hello" in sanitized and "World!" in sanitized
    
    # Test dictionary sanitization
    test_dict = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "comment": "<b>Bold text</b> with <script>alert('xss')</script>"
    }
    
    sanitized = validator.validate_and_sanitize(test_dict)
    assert sanitized["name"] == "John Doe"
    assert sanitized["email"] == "<EMAIL>"
    assert "<script>" not in sanitized["comment"]
    
    # Test depth limit
    validator.max_object_depth = 3
    deep_dict = {"level1": {"level2": {"level3": {"level4": {"level5": {}}}}}}
    try:
        validator.validate_and_sanitize(deep_dict)
        assert False, "Should have raised ValueError for deep nesting"
    except ValueError as e:
        assert "depth exceeds maximum" in str(e)
    
    print("✅ Input Validator Core tests passed")
    return True


def test_concurrent_security_core():
    """Test security components under concurrent load."""
    print("🧪 Testing concurrent security operations...")
    
    # Use the rate limiter from previous test
    class AdvancedRateLimiter:
        def __init__(self):
            self.token_buckets = defaultdict(dict)
            self.sliding_windows = defaultdict(lambda: deque(maxlen=1000))
            self.default_limits = {"burst_limit": 10}
        
        def check_rate_limit(self, identifier: str) -> bool:
            current_time = time.time()
            return self._check_token_bucket(identifier, 10, current_time)
        
        def _check_token_bucket(self, identifier: str, limit: int, current_time: float) -> bool:
            bucket = self.token_buckets[identifier]
            
            if "tokens" not in bucket:
                bucket["tokens"] = limit
                bucket["last_refill"] = current_time
            
            time_passed = current_time - bucket["last_refill"]
            tokens_to_add = time_passed * (limit / 60)
            bucket["tokens"] = min(limit, bucket["tokens"] + tokens_to_add)
            bucket["last_refill"] = current_time
            
            if bucket["tokens"] >= 1:
                bucket["tokens"] -= 1
                return True
            
            return False
    
    limiter = AdvancedRateLimiter()
    results = []
    
    def worker(worker_id: int):
        identifier = f"worker-{worker_id}"
        allowed_requests = 0
        
        for i in range(20):
            if limiter.check_rate_limit(identifier):
                allowed_requests += 1
        
        results.append(allowed_requests)
    
    # Run concurrent workers
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    # Verify results
    assert len(results) == 5
    for allowed_requests in results:
        assert allowed_requests > 0  # Each worker should get some requests
    
    print("✅ Concurrent security tests passed")
    return True


async def main():
    """Run all core security tests."""
    print("🚀 Starting Core Security System Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Run all tests
        tests = [
            ("request_signature", test_request_signature_core),
            ("ip_whitelist", test_ip_whitelist_core),
            ("rate_limiter", test_rate_limiter_core),
            ("threat_detector", test_threat_detector_core),
            ("input_validator", test_input_validator_core),
            ("concurrent_security", test_concurrent_security_core),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                result = test_func()
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 Core Security System Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate == 1.0:
            print("🏆 All core security tests passed - System ready for production!")
            return 0
        else:
            print("⚠️ Some tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
