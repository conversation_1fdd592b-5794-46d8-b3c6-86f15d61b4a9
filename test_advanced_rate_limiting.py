#!/usr/bin/env python3
"""
Tests for advanced rate limiting system.
"""

import asyncio
import sys
import time
from pathlib import Path
from unittest.mock import MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()

from src.security.advanced_rate_limiting import (
    RateLimitAlgorithm,
    UserTier,
    RateLimitConfig,
    RateLimitResult,
    AdvancedRateLimiter,
    advanced_rate_limiter,
    get_rate_limiter_status
)


def test_rate_limit_config():
    """Test rate limit configuration."""
    print("🧪 Testing RateLimitConfig...")
    
    config = RateLimitConfig(
        requests_per_minute=100,
        requests_per_hour=2000,
        burst_size=20,
        concurrent_requests=10,
        weight_factor=1.5
    )
    
    config_dict = config.to_dict()
    
    assert config_dict["requests_per_minute"] == 100
    assert config_dict["requests_per_hour"] == 2000
    assert config_dict["burst_size"] == 20
    assert config_dict["concurrent_requests"] == 10
    assert config_dict["weight_factor"] == 1.5
    
    print("✅ RateLimitConfig tests passed")
    return True


def test_rate_limit_result():
    """Test rate limit result."""
    print("🧪 Testing RateLimitResult...")
    
    result = RateLimitResult(
        allowed=True,
        tokens_remaining=15,
        requests_remaining_minute=45,
        tier="premium",
        system_load_factor=1.2
    )
    
    result_dict = result.to_dict()
    
    assert result_dict["allowed"] == True
    assert result_dict["tokens_remaining"] == 15
    assert result_dict["requests_remaining_minute"] == 45
    assert result_dict["tier"] == "premium"
    assert result_dict["system_load_factor"] == 1.2
    
    print("✅ RateLimitResult tests passed")
    return True


def test_basic_rate_limiting():
    """Test basic rate limiting functionality."""
    print("🧪 Testing basic rate limiting...")
    
    limiter = AdvancedRateLimiter()
    
    # Test free tier limits
    user_id = "test_user_1"
    
    # Should allow initial requests
    for i in range(10):  # Burst size for free tier
        result = limiter.check_rate_limit(user_id, UserTier.FREE)
        assert result.allowed == True, f"Request {i} should be allowed"
    
    # Should start rate limiting after burst
    result = limiter.check_rate_limit(user_id, UserTier.FREE)
    # Might be allowed or not depending on timing, but should have low tokens
    
    # Test premium tier has higher limits
    premium_user = "premium_user_1"
    
    # Should allow more requests for premium
    allowed_count = 0
    for i in range(60):  # Try many requests
        result = limiter.check_rate_limit(premium_user, UserTier.PREMIUM)
        if result.allowed:
            allowed_count += 1
        else:
            break
    
    assert allowed_count > 10, "Premium tier should allow more requests than free tier"
    
    print("✅ Basic rate limiting tests passed")
    return True


def test_token_bucket_algorithm():
    """Test token bucket algorithm."""
    print("🧪 Testing token bucket algorithm...")
    
    limiter = AdvancedRateLimiter()
    user_id = "token_bucket_user"
    
    # Consume all burst tokens
    burst_size = limiter.tier_configs[UserTier.FREE].burst_size
    
    for i in range(burst_size):
        result = limiter.check_rate_limit(user_id, UserTier.FREE)
        assert result.allowed == True, f"Burst request {i} should be allowed"
    
    # Next request should be rate limited
    result = limiter.check_rate_limit(user_id, UserTier.FREE)
    if not result.allowed:
        assert result.reason == "token_bucket_exhausted"
        assert result.retry_after is not None
        assert result.retry_after > 0
    
    # Wait for token refill (simulate)
    time.sleep(1.1)  # Wait for tokens to refill
    
    # Should allow request after refill
    result = limiter.check_rate_limit(user_id, UserTier.FREE)
    assert result.allowed == True, "Request should be allowed after token refill"
    
    print("✅ Token bucket algorithm tests passed")
    return True


def test_sliding_window_algorithm():
    """Test sliding window algorithm."""
    print("🧪 Testing sliding window algorithm...")
    
    limiter = AdvancedRateLimiter()
    user_id = "sliding_window_user"
    
    # Make requests up to the minute limit
    minute_limit = limiter.tier_configs[UserTier.FREE].requests_per_minute
    
    # Make requests with small delays to avoid token bucket issues
    allowed_count = 0
    for i in range(minute_limit + 10):  # Try more than the limit
        result = limiter.check_rate_limit(user_id, UserTier.FREE)
        if result.allowed:
            allowed_count += 1
            # Release the request to avoid concurrent limit issues
            limiter.release_request(user_id)
        else:
            if result.reason in ["minute_limit_exceeded", "hour_limit_exceeded"]:
                break
        
        time.sleep(0.01)  # Small delay
    
    # Should have hit the sliding window limit
    assert allowed_count <= minute_limit + limiter.tier_configs[UserTier.FREE].burst_size
    
    print("✅ Sliding window algorithm tests passed")
    return True


def test_endpoint_specific_limits():
    """Test endpoint-specific rate limits."""
    print("🧪 Testing endpoint-specific limits...")
    
    limiter = AdvancedRateLimiter()
    user_id = "endpoint_user"
    endpoint = "/api/v1/analyze"
    
    # Test endpoint-specific limits
    endpoint_limit = limiter.endpoint_configs[endpoint].requests_per_minute
    
    allowed_count = 0
    for i in range(endpoint_limit + 5):
        result = limiter.check_rate_limit(user_id, UserTier.PREMIUM, endpoint=endpoint)
        if result.allowed:
            allowed_count += 1
            limiter.release_request(user_id)
        else:
            if result.reason == "endpoint_limit_exceeded":
                break
        
        time.sleep(0.01)
    
    # Should respect endpoint limits
    assert allowed_count <= endpoint_limit + 5  # Allow some tolerance
    
    print("✅ Endpoint-specific limits tests passed")
    return True


def test_method_specific_limits():
    """Test method-specific rate limits."""
    print("🧪 Testing method-specific limits...")
    
    limiter = AdvancedRateLimiter()
    user_id = "method_user"
    
    # Test POST method limits (stricter than GET)
    post_limit = limiter.method_configs["POST"].requests_per_minute
    get_limit = limiter.method_configs["GET"].requests_per_minute
    
    assert post_limit < get_limit, "POST should have stricter limits than GET"
    
    # Test POST requests
    allowed_post_count = 0
    for i in range(post_limit + 10):
        result = limiter.check_rate_limit(user_id, UserTier.PREMIUM, method="POST")
        if result.allowed:
            allowed_post_count += 1
            limiter.release_request(user_id)
        else:
            if result.reason == "method_limit_exceeded":
                break
        
        time.sleep(0.01)
    
    # Should respect method limits
    assert allowed_post_count <= post_limit + 10  # Allow some tolerance
    
    print("✅ Method-specific limits tests passed")
    return True


def test_concurrent_request_limits():
    """Test concurrent request limits."""
    print("🧪 Testing concurrent request limits...")
    
    limiter = AdvancedRateLimiter()
    user_id = "concurrent_user"
    
    # Simulate concurrent requests without releasing them
    concurrent_limit = limiter.tier_configs[UserTier.FREE].concurrent_requests
    
    # Make concurrent requests up to the limit
    for i in range(concurrent_limit):
        result = limiter.check_rate_limit(user_id, UserTier.FREE)
        assert result.allowed == True, f"Concurrent request {i} should be allowed"
        # Don't release the request to simulate active processing
    
    # Next request should be blocked due to concurrent limit
    result = limiter.check_rate_limit(user_id, UserTier.FREE)
    if not result.allowed:
        assert result.reason == "concurrent_limit_exceeded"
    
    # Release some requests
    for i in range(concurrent_limit):
        limiter.release_request(user_id)
    
    # Should allow requests again
    result = limiter.check_rate_limit(user_id, UserTier.FREE)
    assert result.allowed == True, "Request should be allowed after releasing concurrent requests"
    
    print("✅ Concurrent request limits tests passed")
    return True


def test_adaptive_throttling():
    """Test adaptive throttling based on system load."""
    print("🧪 Testing adaptive throttling...")
    
    limiter = AdvancedRateLimiter()
    user_id = "adaptive_user"
    
    # Test normal load
    limiter.update_system_load(1.0)
    result_normal = limiter.check_rate_limit(user_id, UserTier.FREE)
    
    # Test high load
    limiter.update_system_load(2.0)
    
    # Get effective config under high load
    config_high_load = limiter._get_effective_config(UserTier.FREE, None, "GET")
    config_normal = limiter.tier_configs[UserTier.FREE]
    
    # Limits should be reduced under high load
    assert config_high_load.requests_per_minute < config_normal.requests_per_minute
    assert config_high_load.burst_size < config_normal.burst_size
    
    print("✅ Adaptive throttling tests passed")
    return True


def test_global_rate_limiting():
    """Test global rate limiting."""
    print("🧪 Testing global rate limiting...")
    
    limiter = AdvancedRateLimiter()
    
    # Enable global rate limiting with low limit
    limiter.enable_global_rate_limit(5)  # 5 requests per second globally
    
    # Make requests from multiple users rapidly
    users = [f"global_user_{i}" for i in range(3)]
    
    blocked_count = 0
    allowed_count = 0
    
    for i in range(20):  # Try many requests quickly
        user = users[i % len(users)]
        result = limiter.check_rate_limit(user, UserTier.FREE)
        
        if result.allowed:
            allowed_count += 1
            limiter.release_request(user)
        else:
            if result.reason == "global_rate_limit_exceeded":
                blocked_count += 1
    
    # Should have blocked some requests due to global limit
    assert blocked_count > 0, "Global rate limit should block some requests"
    
    # Disable global rate limiting
    limiter.disable_global_rate_limit()
    
    print("✅ Global rate limiting tests passed")
    return True


def test_user_tier_management():
    """Test user tier management."""
    print("🧪 Testing user tier management...")
    
    limiter = AdvancedRateLimiter()
    user_id = "tier_user"
    
    # Set user to premium tier
    limiter.set_user_tier(user_id, UserTier.PREMIUM)
    
    # Check that user gets premium limits
    result = limiter.check_rate_limit(user_id)  # No tier specified, should use stored tier
    assert result.tier == UserTier.PREMIUM.value
    
    # Change to enterprise tier
    limiter.set_user_tier(user_id, UserTier.ENTERPRISE)
    
    result = limiter.check_rate_limit(user_id)
    assert result.tier == UserTier.ENTERPRISE.value
    
    print("✅ User tier management tests passed")
    return True


def test_rate_limit_status():
    """Test rate limit status reporting."""
    print("🧪 Testing rate limit status...")
    
    limiter = AdvancedRateLimiter()
    user_id = "status_user"
    
    # Make some requests
    for i in range(5):
        result = limiter.check_rate_limit(user_id, UserTier.PREMIUM)
        if result.allowed:
            limiter.release_request(user_id)
    
    # Get status
    status = limiter.get_rate_limit_status(user_id)
    
    assert "identifier" in status
    assert "tier" in status
    assert "tokens_remaining" in status
    assert "requests_last_minute" in status
    assert "requests_last_hour" in status
    assert "config" in status
    
    assert status["identifier"] == user_id
    assert status["tier"] == UserTier.FREE.value  # Default tier
    
    print("✅ Rate limit status tests passed")
    return True


def test_system_stats():
    """Test system statistics."""
    print("🧪 Testing system statistics...")
    
    limiter = AdvancedRateLimiter()
    
    # Make requests from multiple users
    for i in range(3):
        user_id = f"stats_user_{i}"
        limiter.set_user_tier(user_id, UserTier.PREMIUM)
        result = limiter.check_rate_limit(user_id, UserTier.PREMIUM)
        if result.allowed:
            limiter.release_request(user_id)
    
    # Get system stats
    stats = limiter.get_system_stats()
    
    assert "active_buckets" in stats
    assert "total_windows" in stats
    assert "tier_distribution" in stats
    assert "system_load_factor" in stats
    assert "tier_configs" in stats
    
    assert stats["active_buckets"] > 0
    assert stats["tier_distribution"]["premium"] > 0
    
    print("✅ System statistics tests passed")
    return True


def test_rate_limiter_status():
    """Test rate limiter status function."""
    print("🧪 Testing rate limiter status function...")
    
    status = get_rate_limiter_status()
    
    assert "rate_limiter" in status
    assert "system_stats" in status
    assert "timestamp" in status
    
    assert status["rate_limiter"] == "initialized"
    
    system_stats = status["system_stats"]
    assert "active_buckets" in system_stats
    assert "tier_configs" in system_stats
    
    print("✅ Rate limiter status function tests passed")
    return True


async def main():
    """Run all advanced rate limiting tests."""
    print("🚀 Starting Advanced Rate Limiting Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Run all tests
        tests = [
            ("rate_limit_config", test_rate_limit_config),
            ("rate_limit_result", test_rate_limit_result),
            ("basic_rate_limiting", test_basic_rate_limiting),
            ("token_bucket_algorithm", test_token_bucket_algorithm),
            ("sliding_window_algorithm", test_sliding_window_algorithm),
            ("endpoint_specific_limits", test_endpoint_specific_limits),
            ("method_specific_limits", test_method_specific_limits),
            ("concurrent_request_limits", test_concurrent_request_limits),
            ("adaptive_throttling", test_adaptive_throttling),
            ("global_rate_limiting", test_global_rate_limiting),
            ("user_tier_management", test_user_tier_management),
            ("rate_limit_status", test_rate_limit_status),
            ("system_stats", test_system_stats),
            ("rate_limiter_status", test_rate_limiter_status),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                result = test_func()
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 Advanced Rate Limiting Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate >= 0.9:
            print("🏆 Rate limiting tests meet high standards - System ready!")
            return 0
        else:
            print("⚠️ Some rate limiting tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
