#!/usr/bin/env python3
"""
Simple API validation test to check what's actually working
"""

import asyncio
import aiohttp
import json
from datetime import datetime

async def test_dexscreener():
    """Test DexScreener API endpoints"""
    print("🔍 Testing DexScreener APIs...")
    
    async with aiohttp.ClientSession() as session:
        # Test different endpoints
        endpoints = [
            "https://api.dexscreener.com/latest/dex/tokens/trending",
            "https://api.dexscreener.com/latest/dex/search?q=PEPE",
            "https://api.dexscreener.com/latest/dex/pairs/bsc",
            "https://api.dexscreener.com/latest/dex/pairs/ethereum"
        ]
        
        for endpoint in endpoints:
            try:
                async with session.get(endpoint) as response:
                    print(f"   📡 {endpoint}")
                    print(f"   Status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        pairs = data.get("pairs", [])
                        if pairs:
                            print(f"   ✅ Found {len(pairs)} pairs")
                            # Show sample
                            sample = pairs[0]
                            base_token = sample.get("baseToken", {})
                            print(f"   📊 Sample: {base_token.get('symbol', 'N/A')} - {base_token.get('name', 'N/A')}")
                        else:
                            print(f"   ⚠️  No pairs found (pairs = {pairs})")
                    else:
                        text = await response.text()
                        print(f"   ❌ Error: {text[:200]}")
                        
            except Exception as e:
                print(f"   ❌ Exception: {e}")
            
            print()

async def test_coingecko():
    """Test CoinGecko API"""
    print("🔍 Testing CoinGecko API...")
    
    async with aiohttp.ClientSession() as session:
        try:
            url = "https://api.coingecko.com/api/v3/search/trending"
            async with session.get(url) as response:
                print(f"   📡 {url}")
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    coins = data.get("coins", [])
                    print(f"   ✅ Found {len(coins)} trending coins")
                    
                    if coins:
                        sample = coins[0].get("item", {})
                        print(f"   📊 Sample: {sample.get('symbol', 'N/A')} - {sample.get('name', 'N/A')}")
                else:
                    text = await response.text()
                    print(f"   ❌ Error: {text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        print()

async def test_birdeye():
    """Test Birdeye API"""
    print("🔍 Testing Birdeye API...")
    
    async with aiohttp.ClientSession() as session:
        try:
            url = "https://public-api.birdeye.so/defi/tokenlist"
            params = {"sort_by": "v24hUSD", "sort_type": "desc", "limit": 5}
            
            async with session.get(url, params=params) as response:
                print(f"   📡 {url}")
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    tokens = data.get("data", {}).get("tokens", [])
                    print(f"   ✅ Found {len(tokens)} tokens")
                    
                    if tokens:
                        sample = tokens[0]
                        print(f"   📊 Sample: {sample.get('symbol', 'N/A')} - {sample.get('name', 'N/A')}")
                else:
                    text = await response.text()
                    print(f"   ❌ Error: {text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        print()

async def test_dextools():
    """Test DexTools API"""
    print("🔍 Testing DexTools API...")
    
    async with aiohttp.ClientSession() as session:
        try:
            url = "https://www.dextools.io/shared/data/trending"
            headers = {
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                "Accept": "application/json",
            }
            
            async with session.get(url, headers=headers) as response:
                print(f"   📡 {url}")
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    tokens = data.get("data", [])
                    print(f"   ✅ Found {len(tokens)} tokens")
                    
                    if tokens:
                        sample = tokens[0]
                        print(f"   📊 Sample: {sample.get('symbol', 'N/A')} - {sample.get('name', 'N/A')}")
                else:
                    text = await response.text()
                    print(f"   ❌ Error: {text[:200]}")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")
        
        print()

async def main():
    print("🚀 API Validation Test")
    print("=" * 50)
    
    await test_dexscreener()
    await test_coingecko()
    await test_birdeye()
    await test_dextools()
    
    print("✅ API validation complete")

if __name__ == "__main__":
    asyncio.run(main())
