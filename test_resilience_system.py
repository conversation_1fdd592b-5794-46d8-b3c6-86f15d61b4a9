#!/usr/bin/env python3
"""
Comprehensive tests for the advanced resilience system.
Tests all error handling, recovery, and resilience patterns.
"""

import asyncio
import sys
import time
from pathlib import Path
from unittest.mock import MagicMock
import threading

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()

from src.core.resilience import (
    RetryConfig,
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitBreakerState,
    DeadLetterQueue,
    GracefulDegradationManager,
    ErrorContext,
    ErrorSeverity,
    resilient_operation,
    get_circuit_breaker,
    register_fallback,
    degrade_service,
    restore_service,
    get_system_health
)


def test_retry_config():
    """Test RetryConfig delay calculations."""
    print("🧪 Testing RetryConfig...")
    
    # Test exponential backoff
    config = RetryConfig(
        base_delay=1.0,
        exponential_base=2.0,
        max_delay=10.0,
        jitter=False
    )
    
    assert config.calculate_delay(1) == 1.0  # 1.0 * 2^0
    assert config.calculate_delay(2) == 2.0  # 1.0 * 2^1
    assert config.calculate_delay(3) == 4.0  # 1.0 * 2^2
    assert config.calculate_delay(5) == 10.0  # Capped at max_delay
    
    # Test linear backoff
    config.backoff_strategy = "linear"
    assert config.calculate_delay(1) == 1.0  # 1.0 * 1
    assert config.calculate_delay(2) == 2.0  # 1.0 * 2
    assert config.calculate_delay(3) == 3.0  # 1.0 * 3
    
    # Test fixed backoff
    config.backoff_strategy = "fixed"
    assert config.calculate_delay(1) == 1.0
    assert config.calculate_delay(5) == 1.0
    
    print("✅ RetryConfig tests passed")
    return True


def test_circuit_breaker():
    """Test CircuitBreaker functionality."""
    print("🧪 Testing CircuitBreaker...")
    
    config = CircuitBreakerConfig(
        failure_threshold=3,
        recovery_timeout=1.0,
        success_threshold=2
    )
    
    cb = CircuitBreaker("test_service", config)
    
    # Initially closed
    assert cb.state == CircuitBreakerState.CLOSED
    assert cb.can_execute() == True
    
    # Record failures to trigger opening
    for i in range(3):
        cb.record_failure()
    
    assert cb.state == CircuitBreakerState.OPEN
    assert cb.can_execute() == False
    
    # Wait for recovery timeout
    time.sleep(1.1)
    
    # Should transition to half-open
    assert cb.can_execute() == True
    assert cb.state == CircuitBreakerState.HALF_OPEN
    
    # Record successes to close
    cb.record_success()
    cb.record_success()
    
    assert cb.state == CircuitBreakerState.CLOSED
    
    print("✅ CircuitBreaker tests passed")
    return True


def test_dead_letter_queue():
    """Test DeadLetterQueue functionality."""
    print("🧪 Testing DeadLetterQueue...")
    
    dlq = DeadLetterQueue(max_size=100)
    
    # Create error context
    error_context = ErrorContext(
        error=ValueError("Test error"),
        operation="test_operation",
        attempt=3,
        max_attempts=3,
        severity=ErrorSeverity.HIGH,
        timestamp=datetime.now(timezone.utc)
    )
    
    # Add failed operation
    dlq.add_failed_operation("test_op", error_context, {"key": "value"})
    
    # Check queue
    failed_ops = dlq.get_failed_operations()
    assert len(failed_ops) == 1
    assert failed_ops[0]["operation"] == "test_op"
    
    # Check stats
    stats = dlq.get_failure_stats()
    assert stats["test_op"] == 1
    
    print("✅ DeadLetterQueue tests passed")
    return True


def test_graceful_degradation():
    """Test GracefulDegradationManager functionality."""
    print("🧪 Testing GracefulDegradationManager...")
    
    manager = GracefulDegradationManager()
    
    # Register fallback
    def fallback_handler(*args, **kwargs):
        return "fallback_result"
    
    manager.register_fallback("test_operation", fallback_handler)
    
    # Test degradation
    manager.degrade_service("api_service", 2)
    assert manager.get_degradation_level("api_service") == 2
    
    # Test restoration
    manager.restore_service("api_service")
    assert manager.get_degradation_level("api_service") == 0
    
    # Test fallback execution
    result = manager.execute_with_fallback("test_operation", "arg1", key="value")
    assert result == "fallback_result"
    
    print("✅ GracefulDegradationManager tests passed")
    return True


async def test_resilient_operation_success():
    """Test resilient operation with successful execution."""
    print("🧪 Testing resilient operation success...")
    
    call_count = 0
    
    @resilient_operation("test_success", RetryConfig(max_attempts=3))
    async def successful_operation():
        nonlocal call_count
        call_count += 1
        return "success"
    
    result = await successful_operation()
    assert result == "success"
    assert call_count == 1
    
    print("✅ Resilient operation success tests passed")
    return True


async def test_resilient_operation_retry():
    """Test resilient operation with retry logic."""
    print("🧪 Testing resilient operation retry...")
    
    call_count = 0
    
    @resilient_operation("test_retry", RetryConfig(max_attempts=3, base_delay=0.1))
    async def failing_then_success():
        nonlocal call_count
        call_count += 1
        if call_count < 3:
            raise ValueError(f"Attempt {call_count} failed")
        return "success_after_retry"
    
    result = await failing_then_success()
    assert result == "success_after_retry"
    assert call_count == 3
    
    print("✅ Resilient operation retry tests passed")
    return True


async def test_resilient_operation_fallback():
    """Test resilient operation with fallback."""
    print("🧪 Testing resilient operation fallback...")
    
    async def fallback_handler():
        return "fallback_result"
    
    @resilient_operation(
        "test_fallback",
        RetryConfig(max_attempts=2, base_delay=0.1),
        fallback_handler=fallback_handler
    )
    async def always_failing():
        raise RuntimeError("Always fails")
    
    result = await always_failing()
    assert result == "fallback_result"
    
    print("✅ Resilient operation fallback tests passed")
    return True


async def test_circuit_breaker_integration():
    """Test circuit breaker integration with resilient operations."""
    print("🧪 Testing circuit breaker integration...")
    
    call_count = 0
    
    @resilient_operation(
        "test_circuit",
        RetryConfig(max_attempts=1),  # No retries to trigger circuit breaker faster
        CircuitBreakerConfig(failure_threshold=2, recovery_timeout=0.5)
    )
    async def failing_operation():
        nonlocal call_count
        call_count += 1
        raise ConnectionError("Service unavailable")
    
    # First two calls should fail and open circuit breaker
    try:
        await failing_operation()
    except ConnectionError:
        pass
    
    try:
        await failing_operation()
    except ConnectionError:
        pass
    
    # Third call should be rejected by circuit breaker
    try:
        await failing_operation()
        assert False, "Should have been rejected by circuit breaker"
    except Exception as e:
        assert "Circuit breaker is OPEN" in str(e)
    
    # Call count should be 2 (third call was rejected)
    assert call_count == 2
    
    print("✅ Circuit breaker integration tests passed")
    return True


async def test_dead_letter_queue_integration():
    """Test dead letter queue integration."""
    print("🧪 Testing dead letter queue integration...")
    
    from src.core.resilience import dead_letter_queue
    
    initial_size = len(dead_letter_queue.queue)
    
    @resilient_operation(
        "test_dlq",
        RetryConfig(max_attempts=2, base_delay=0.1),
        enable_dead_letter=True
    )
    async def failing_operation(arg1, arg2=None):
        raise ValueError("Persistent failure")
    
    try:
        await failing_operation("test_arg", arg2="test_kwarg")
    except ValueError:
        pass
    
    # Check that operation was added to dead letter queue
    assert len(dead_letter_queue.queue) > initial_size
    
    # Check the dead letter item
    recent_items = dead_letter_queue.get_failed_operations(1)
    assert len(recent_items) > 0
    assert recent_items[-1]["operation"] == "test_dlq"
    
    print("✅ Dead letter queue integration tests passed")
    return True


async def test_concurrent_resilience():
    """Test resilience under concurrent load."""
    print("🧪 Testing concurrent resilience...")
    
    success_count = 0
    failure_count = 0
    
    @resilient_operation("test_concurrent", RetryConfig(max_attempts=2, base_delay=0.01))
    async def concurrent_operation(worker_id: int):
        nonlocal success_count, failure_count
        
        # Simulate some operations failing
        if worker_id % 3 == 0:
            failure_count += 1
            raise RuntimeError(f"Worker {worker_id} failed")
        else:
            success_count += 1
            return f"Worker {worker_id} success"
    
    # Run concurrent operations
    tasks = [concurrent_operation(i) for i in range(10)]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Count successful results
    successful_results = [r for r in results if isinstance(r, str)]
    failed_results = [r for r in results if isinstance(r, Exception)]
    
    assert len(successful_results) > 0
    assert len(failed_results) > 0
    
    print(f"✅ Concurrent resilience tests passed: {len(successful_results)} successes, {len(failed_results)} failures")
    return True


def test_system_health():
    """Test system health reporting."""
    print("🧪 Testing system health reporting...")
    
    # Create some circuit breakers and degraded services
    cb = get_circuit_breaker("health_test", CircuitBreakerConfig())
    cb.record_failure()
    
    degrade_service("test_service", 2)
    
    health = get_system_health()
    
    assert "circuit_breakers" in health
    assert "dead_letter_queue" in health
    assert "degraded_services" in health
    assert "timestamp" in health
    
    assert "health_test" in health["circuit_breakers"]
    assert health["degraded_services"]["test_service"] == 2
    
    # Restore service
    restore_service("test_service")
    
    print("✅ System health tests passed")
    return True


async def test_real_world_scenario():
    """Test a realistic failure and recovery scenario."""
    print("🧪 Testing real-world scenario...")
    
    # Simulate an API service with various failure modes
    api_call_count = 0
    
    async def unreliable_api_fallback():
        return {"status": "degraded", "data": "cached_result"}
    
    @resilient_operation(
        "external_api",
        RetryConfig(max_attempts=3, base_delay=0.1),
        CircuitBreakerConfig(failure_threshold=3, recovery_timeout=1.0),
        fallback_handler=unreliable_api_fallback
    )
    async def unreliable_api_call():
        nonlocal api_call_count
        api_call_count += 1
        
        # Simulate different failure patterns
        if api_call_count <= 5:
            raise ConnectionError("Service temporarily unavailable")
        elif api_call_count <= 8:
            raise TimeoutError("Request timeout")
        else:
            return {"status": "success", "data": "real_result"}
    
    results = []
    
    # Make multiple calls to trigger various resilience patterns
    for i in range(10):
        try:
            result = await unreliable_api_call()
            results.append(result)
        except Exception as e:
            results.append({"error": str(e)})
        
        # Small delay between calls
        await asyncio.sleep(0.05)
    
    # Verify we got some results (either real or fallback)
    successful_results = [r for r in results if "error" not in r]
    assert len(successful_results) > 0
    
    # Check that we have both fallback and real results
    fallback_results = [r for r in successful_results if r.get("status") == "degraded"]
    real_results = [r for r in successful_results if r.get("status") == "success"]
    
    print(f"✅ Real-world scenario passed: {len(fallback_results)} fallback, {len(real_results)} real results")
    return True


async def main():
    """Run all resilience system tests."""
    print("🚀 Starting Advanced Resilience System Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Import datetime here to avoid issues
        from datetime import datetime, timezone
        globals()['datetime'] = datetime
        globals()['timezone'] = timezone
        
        # Run all tests
        tests = [
            ("retry_config", test_retry_config),
            ("circuit_breaker", test_circuit_breaker),
            ("dead_letter_queue", test_dead_letter_queue),
            ("graceful_degradation", test_graceful_degradation),
            ("resilient_success", test_resilient_operation_success),
            ("resilient_retry", test_resilient_operation_retry),
            ("resilient_fallback", test_resilient_operation_fallback),
            ("circuit_integration", test_circuit_breaker_integration),
            ("dlq_integration", test_dead_letter_queue_integration),
            ("concurrent_resilience", test_concurrent_resilience),
            ("system_health", test_system_health),
            ("real_world", test_real_world_scenario),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 Advanced Resilience System Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate == 1.0:
            print("🏆 All tests passed - Resilience system ready for production!")
            return 0
        else:
            print("⚠️ Some tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
