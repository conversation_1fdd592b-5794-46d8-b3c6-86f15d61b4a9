{"test_start_time": "2025-07-10 10:24:27.304267", "test_end_time": "2025-07-10 10:24:39.023416", "total_duration": 11.719149, "agent_communication_tests": [{"test_name": "Agent Communication - Basic Status Check", "success": false, "duration": 1.811981201171875e-05, "details": {"agents_found": 0, "message_bus_status": {"total_queues": 0, "total_subscribers": 0, "delivery_guarantees": 0}, "event_store_status": {"total_events": 0, "total_snapshots": 0, "event_handlers": {"task_assigned": 1, "task_completed": 1}}}, "error": null, "timestamp": "2025-07-10 10:24:27.448904"}, {"test_name": "Agent Communication - Message Bus Reliability", "success": true, "duration": 0.00039696693420410156, "details": {"messages_sent": 10, "success_rate": 1.0, "failed_messages": 0}, "error": null, "timestamp": "2025-07-10 10:24:27.449304"}, {"test_name": "Agent Communication - Response Time Test", "success": true, "duration": 3.790855407714844e-05, "details": {"agents_tested": 5, "average_response_time": 0.001, "max_response_time": 0.001, "min_response_time": 0.001, "response_times": [0.001, 0.001, 0.001, 0.001, 0.001]}, "error": null, "timestamp": "2025-07-10 10:24:27.449344"}], "agent_coordination_tests": [{"test_name": "Agent Coordination - Sequential Pipeline", "success": true, "duration": 4.828945159912109, "details": {"tokens_discovered": 1, "agents_involved": 1, "pipeline_success": true, "agent_results": {"discovery": true}}, "error": null, "timestamp": "2025-07-10 10:24:32.278323"}, {"test_name": "Agent Coordination - Concurrent Operations", "success": true, "duration": 5.723604917526245, "details": {"concurrent_operations": 3, "successful_operations": 3, "success_rate": 1.0, "exceptions": 0}, "error": null, "timestamp": "2025-07-10 10:24:38.001938"}, {"test_name": "Agent Coordination - State Synchronization", "success": true, "duration": 1.0013580322265625e-05, "details": {"agents_checked": 9, "agent_states": {"discovery": {"status": "unknown"}, "validator": {"status": "unknown"}, "chain_info": {"status": "unknown"}, "market_data": {"status": "unknown"}, "technical": {"status": "unknown"}, "sentiment": {"status": "unknown"}, "analyst": {"status": "unknown"}, "audit": {"status": "unknown"}, "scheduler": {"status": "unknown"}}, "state_consistency": true}, "error": null, "timestamp": "2025-07-10 10:24:38.001952"}], "agent_fault_tolerance_tests": [{"test_name": "Agent <PERSON>ault Tolerance - Circuit Breaker", "success": false, "duration": 6.198883056640625e-06, "details": {}, "error": "can't subtract offset-naive and offset-aware datetimes", "timestamp": "2025-07-10 10:24:38.002012"}, {"test_name": "Agent <PERSON><PERSON> Tolerance - Graceful Degradation", "success": true, "duration": 0.0001220703125, "details": {"pipeline_completed": true, "pipeline_success": false, "missing_agent": "technical", "system_crashed": false}, "error": null, "timestamp": "2025-07-10 10:24:38.002137"}], "agent_performance_metrics": {"discovery": {"agent_name": "discovery", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "validator": {"agent_name": "validator", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "chain_info": {"agent_name": "chain_info", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "market_data": {"agent_name": "market_data", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "sentiment": {"agent_name": "sentiment", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "analyst": {"agent_name": "analyst", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "audit": {"agent_name": "audit", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "scheduler": {"agent_name": "scheduler", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}, "technical": {"agent_name": "technical", "response_times": [0.001, 0.001, 0.001, 0.001, 0.001], "success_rate": 1.0, "error_count": 0, "message_count": 5, "state_changes": 1}}, "workflow_tests": [{"test_name": "Complete Workflow - Discovery to Analysis", "success": false, "duration": 0.0001480579376220703, "details": {"workflow_completed": true, "tokens_discovered": 0, "agents_executed": 0, "workflow_success": false, "agent_results": {}}, "error": null, "timestamp": "2025-07-10 10:24:38.002350"}], "api_integration_tests": [{"test_name": "API Integration - Honeypot.is", "success": false, "duration": 0.0, "details": {}, "error": "Honeypot check method not found", "timestamp": "2025-07-10 10:24:38.002383"}, {"test_name": "API Integration - GoPlus Labs", "success": false, "duration": 9.5367431640625e-07, "details": {}, "error": "Contract security check method not found", "timestamp": "2025-07-10 10:24:38.002385"}, {"test_name": "API Integration - DexScreener", "success": false, "duration": 0.0, "details": {}, "error": "Search tokens method not found", "timestamp": "2025-07-10 10:24:38.002387"}], "accuracy_tests": [{"test_name": "Accuracy Test - Legitimate Tokens", "success": false, "duration": 0.0003218650817871094, "details": {"tokens_tested": 0, "correct_assessments": 0, "accuracy": 0, "threshold": 0.8}, "error": null, "timestamp": "2025-07-10 10:24:38.002740"}], "real_data_tests": [{"test_name": "Volatility Test - Multiple Crypto Types", "success": false, "duration": 0.00028014183044433594, "details": {"tokens_tested": 3, "successful_analyses": 0, "success_rate": 0.0, "average_duration": 7.605552673339844e-05, "volatility_results": [{"token": "BTC", "volatility": "low", "analysis_success": false, "analysis_duration": 8.0108642578125e-05, "tokens_found": 0}, {"token": "ETH", "volatility": "medium", "analysis_success": false, "analysis_duration": 7.677078247070312e-05, "tokens_found": 0}, {"token": "DOGE", "volatility": "high", "analysis_success": false, "analysis_duration": 7.128715515136719e-05, "tokens_found": 0}]}, "error": null, "timestamp": "2025-07-10 10:24:38.003065"}], "load_tests": [{"test_name": "Production Load - Concurrent Requests", "success": false, "duration": 0.0006647109985351562, "details": {"concurrent_requests": 5, "successful_requests": 0, "success_rate": 0.0, "total_duration": 0.0006647109985351562, "avg_request_time": 0.00013294219970703126}, "error": null, "timestamp": "2025-07-10 10:24:38.003773"}], "stress_tests": [{"test_name": "Stress Test - Rapid Sequential Requests", "success": false, "duration": 1.0191638469696045, "details": {"total_requests": 10, "successful_requests": 0, "success_rate": 0.0, "average_duration": 0.00046179294586181643, "total_test_duration": 1.0191638469696045}, "error": null, "timestamp": "2025-07-10 10:24:39.022978"}], "error_handling_tests": [{"test_name": "Error Handling - API Failure Recovery", "success": true, "duration": 0.0002613067626953125, "details": {"invalid_source_handled": true}, "error": null, "timestamp": "2025-07-10 10:24:39.023344"}, {"test_name": "Error <PERSON>ling - Circuit Breaker Recovery", "success": false, "duration": 6.198883056640625e-06, "details": {}, "error": "can't subtract offset-naive and offset-aware datetimes", "timestamp": "2025-07-10 10:24:39.023355"}], "monitoring_tests": [{"test_name": "Monitoring - Metrics Collection", "success": false, "duration": 2.1457672119140625e-06, "details": {}, "error": "'MetricsCollector' object has no attribute 'record_metric'", "timestamp": "2025-07-10 10:24:39.023410"}, {"test_name": "Monitoring - System Health Checks", "success": true, "duration": 3.0994415283203125e-06, "details": {"health_checks": {"database": true, "cache": true, "agents": true}, "overall_health": true, "components_checked": 3}, "error": null, "timestamp": "2025-07-10 10:24:39.023415"}], "overall_success_rate": 40.0, "critical_failures": [], "recommendations": ["Strengthen error handling and recovery mechanisms", "Review and improve API integration reliability", "Enhance system capacity for production load handling", "Conduct thorough system review - high failure rate detected"]}