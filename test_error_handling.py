#!/usr/bin/env python3
"""
Comprehensive Error Handling Test - Production Quality Validation
Tests circuit breakers, retries, and fault tolerance patterns
"""

import asyncio
import sys
import time
from datetime import datetime
from unittest.mock import AsyncMock, patch
sys.path.append('src')

async def test_error_handling():
    """Test comprehensive error handling system"""
    print("🛡️  COMPREHENSIVE ERROR HANDLING TEST")
    print("=" * 60)
    
    try:
        from src.utils.error_handling import (
            FaultTolerantExecutor, CircuitBreaker, CircuitBreakerConfig,
            RetryConfig, CircuitBreakerState, CircuitBreakerError,
            fault_tolerant, API_CIRCUIT_CONFIG, EXTERNAL_SERVICE_RETRY_CONFIG
        )
        
        print("✅ Error handling components imported successfully")
        
        # Test scenarios for error handling
        test_scenarios = [
            {
                "name": "Circuit Breaker Basic Operation",
                "description": "Test circuit breaker state transitions",
                "test_method": "circuit_breaker_basic"
            },
            {
                "name": "Circuit Breaker Failure Threshold",
                "description": "Test circuit breaker trips after failures",
                "test_method": "circuit_breaker_failure"
            },
            {
                "name": "Circuit Breaker Recovery",
                "description": "Test circuit breaker recovery after timeout",
                "test_method": "circuit_breaker_recovery"
            },
            {
                "name": "Retry Logic with Exponential Backoff",
                "description": "Test retry mechanism with backoff",
                "test_method": "retry_logic"
            },
            {
                "name": "Fault Tolerant Decorator",
                "description": "Test fault tolerant decorator integration",
                "test_method": "fault_tolerant_decorator"
            },
            {
                "name": "Real API Integration",
                "description": "Test error handling with real API calls",
                "test_method": "real_api_integration"
            }
        ]
        
        print(f"\n🧪 Testing {len(test_scenarios)} error handling scenarios...")
        print("-" * 60)
        
        results = []
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n{i}. Testing {scenario['name']}")
            print(f"   Description: {scenario['description']}")
            
            start_time = datetime.now()
            
            try:
                if scenario['test_method'] == 'circuit_breaker_basic':
                    # Test basic circuit breaker operation
                    print("   🔄 Testing circuit breaker basic operation...")
                    
                    config = CircuitBreakerConfig(failure_threshold=3, recovery_timeout=2)
                    cb = CircuitBreaker("test_service", config)
                    
                    # Should start in CLOSED state
                    assert cb.state == CircuitBreakerState.CLOSED
                    assert cb.can_execute() == True
                    
                    # Record success
                    cb.record_success()
                    assert cb.state == CircuitBreakerState.CLOSED
                    
                    print("   ✅ Circuit breaker basic operation working")
                    status = "PASS"
                
                elif scenario['test_method'] == 'circuit_breaker_failure':
                    # Test circuit breaker failure threshold
                    print("   ⚡ Testing circuit breaker failure threshold...")
                    
                    config = CircuitBreakerConfig(failure_threshold=2, recovery_timeout=1)
                    cb = CircuitBreaker("test_service", config)
                    
                    # Record failures to trip circuit breaker
                    cb.record_failure()
                    assert cb.state == CircuitBreakerState.CLOSED
                    
                    cb.record_failure()
                    assert cb.state == CircuitBreakerState.OPEN
                    assert cb.can_execute() == False
                    
                    print("   ✅ Circuit breaker trips correctly after failures")
                    status = "PASS"
                
                elif scenario['test_method'] == 'circuit_breaker_recovery':
                    # Test circuit breaker recovery
                    print("   🔄 Testing circuit breaker recovery...")
                    
                    config = CircuitBreakerConfig(failure_threshold=1, recovery_timeout=1, success_threshold=1)
                    cb = CircuitBreaker("test_service", config)
                    
                    # Trip circuit breaker
                    cb.record_failure()
                    assert cb.state == CircuitBreakerState.OPEN
                    
                    # Wait for recovery timeout
                    await asyncio.sleep(1.1)
                    
                    # Should transition to HALF_OPEN
                    assert cb.can_execute() == True
                    assert cb.state == CircuitBreakerState.HALF_OPEN
                    
                    # Record success to reset
                    cb.record_success()
                    assert cb.state == CircuitBreakerState.CLOSED
                    
                    print("   ✅ Circuit breaker recovery working")
                    status = "PASS"
                
                elif scenario['test_method'] == 'retry_logic':
                    # Test retry logic
                    print("   🔁 Testing retry logic with exponential backoff...")
                    
                    executor = FaultTolerantExecutor()
                    retry_config = RetryConfig(max_attempts=3, min_wait=0.1, max_wait=1.0)
                    
                    # Mock function that fails twice then succeeds
                    call_count = 0
                    async def mock_function():
                        nonlocal call_count
                        call_count += 1
                        if call_count < 3:
                            raise ConnectionError(f"Attempt {call_count} failed")
                        return f"Success on attempt {call_count}"
                    
                    result = await executor.execute_with_fault_tolerance(
                        mock_function, "test_retry", retry_config
                    )
                    
                    assert result == "Success on attempt 3"
                    assert call_count == 3
                    
                    print(f"   ✅ Retry logic working: {call_count} attempts made")
                    status = "PASS"
                
                elif scenario['test_method'] == 'fault_tolerant_decorator':
                    # Test fault tolerant decorator
                    print("   🎯 Testing fault tolerant decorator...")
                    
                    call_count = 0
                    
                    @fault_tolerant("decorator_test", EXTERNAL_SERVICE_RETRY_CONFIG, API_CIRCUIT_CONFIG)
                    async def decorated_function():
                        nonlocal call_count
                        call_count += 1
                        if call_count == 1:
                            raise ConnectionError("First call fails")
                        return f"Success on call {call_count}"
                    
                    result = await decorated_function()
                    assert "Success" in result
                    assert call_count >= 2
                    
                    print(f"   ✅ Fault tolerant decorator working: {call_count} calls made")
                    status = "PASS"
                
                elif scenario['test_method'] == 'real_api_integration':
                    # Test with real API integration
                    print("   🌐 Testing error handling with real API integration...")
                    
                    from src.agents.scam_detector import AdvancedScamDetector
                    from src.core.cache import CacheManager
                    
                    cache_manager = CacheManager()
                    await cache_manager.initialize()
                    
                    scam_detector = AdvancedScamDetector(cache_manager)
                    await scam_detector.initialize()
                    
                    # Test with invalid token address to trigger errors
                    try:
                        result = await scam_detector._detect_honeypot("0xinvalid", 1)
                        print(f"   📊 API call completed (may have failed gracefully)")
                    except Exception as e:
                        print(f"   📊 API call failed as expected: {type(e).__name__}")
                    
                    # Check executor stats
                    executor = scam_detector._get_fault_tolerant_executor() if hasattr(scam_detector, '_get_fault_tolerant_executor') else None
                    if executor:
                        stats = executor.get_stats()
                        print(f"   📈 Executor stats: {stats}")
                    
                    await scam_detector.shutdown()
                    
                    print("   ✅ Real API integration error handling working")
                    status = "PASS"
                
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                print(f"      Execution time: {execution_time:.2f}s")
                print(f"   ✅ {status}: {scenario['name']} completed successfully")
                
                results.append({
                    "scenario": scenario['name'],
                    "status": status,
                    "execution_time": execution_time,
                    "description": scenario['description']
                })
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results.append({
                    "scenario": scenario['name'],
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # Test global executor statistics
        print(f"\n📊 Testing Global Executor Statistics...")
        try:
            from src.utils.error_handling import get_fault_tolerant_executor
            global_executor = get_fault_tolerant_executor()
            stats = global_executor.get_stats()
            
            print(f"   📈 Global executor stats:")
            print(f"      Total executions: {stats['total_executions']}")
            print(f"      Success rate: {stats['success_rate']:.2%}")
            print(f"      Circuit breaker states: {stats['circuit_breaker_states']}")
            
        except Exception as e:
            print(f"   ⚠️  Could not get global executor stats: {e}")
        
        # Summary Report
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE ERROR HANDLING SUMMARY")
        print("-" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('status') == 'PASS')
        failed_tests = sum(1 for r in results if r.get('status') == 'FAIL')
        error_tests = sum(1 for r in results if r.get('status') == 'ERROR')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        
        # Performance Metrics
        successful_results = [r for r in results if 'execution_time' in r]
        if successful_results:
            avg_time = sum(r['execution_time'] for r in successful_results) / len(successful_results)
            max_time = max(r['execution_time'] for r in successful_results)
            min_time = min(r['execution_time'] for r in successful_results)
            
            print(f"\nPerformance Metrics:")
            print(f"Average Execution Time: {avg_time:.2f}s")
            print(f"Max Execution Time: {max_time:.2f}s")
            print(f"Min Execution Time: {min_time:.2f}s")
        
        # Final Assessment
        print("\n" + "=" * 60)
        print("🎯 FINAL ASSESSMENT:")
        print("-" * 60)
        
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        
        if success_rate >= 0.9 and error_tests == 0:
            print("✅ COMPREHENSIVE ERROR HANDLING: FULLY FUNCTIONAL")
            print("   - Circuit breakers working correctly")
            print("   - Retry logic with exponential backoff operational")
            print("   - Fault tolerance patterns implemented")
            print("   - Real API integration error handling working")
        elif success_rate >= 0.7:
            print("⚠️  COMPREHENSIVE ERROR HANDLING: MOSTLY FUNCTIONAL")
            print("   - Core error handling features working")
            print("   - Some edge cases may need refinement")
            print("   - Overall system resilience improved")
        else:
            print("❌ COMPREHENSIVE ERROR HANDLING: NEEDS IMPROVEMENT")
            print("   - Significant issues with error handling")
            print("   - Circuit breakers or retries not working properly")
            print("   - Further development required")
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_error_handling())
