#!/usr/bin/env python3
"""
Real API Test for Multi-Source Token Discovery
"""

import asyncio
import sys
sys.path.append('src')

from src.agents.discovery import DiscoveryAgent
from src.core.database import DatabaseManager
from src.core.cache import CacheManager
from src.integrations.metrics import MetricsCollector
from src.agents.coordinator import AgentCoordinator

async def test_real_discovery():
    print('🚀 Testing Real Multi-Source Token Discovery')
    print('=' * 60)
    
    # Initialize components
    db_manager = DatabaseManager()
    cache_manager = CacheManager()
    metrics_collector = MetricsCollector()
    coordinator = AgentCoordinator(db_manager, cache_manager, metrics_collector)

    # Initialize discovery agent
    discovery = DiscoveryAgent(db_manager, cache_manager, metrics_collector, coordinator)
    
    try:
        await discovery.initialize()
        
        # Test each source individually first
        sources_to_test = ['dexscreener', 'coingecko', 'dextools', 'birdeye']
        
        for source in sources_to_test:
            print(f'\n📡 Testing {source.upper()} API...')
            try:
                result = await discovery.discover_tokens(sources=[source], min_age_hours=24, limit=5)
                tokens = result.get('tokens', [])
                print(f'   ✅ {source}: Found {len(tokens)} tokens')
                
                if tokens:
                    sample_token = tokens[0]
                    print(f'   📊 Sample: {sample_token.get("symbol", "N/A")} - {sample_token.get("name", "N/A")}')
                    print(f'   💰 Price: ${sample_token.get("price_usd", 0):.6f}')
                    print(f'   📈 Volume 24h: ${sample_token.get("volume_24h_usd", 0):,.2f}')
                    print(f'   🔗 Chain: {sample_token.get("chain", "N/A")}')
                else:
                    print(f'   ⚠️  No tokens found from {source}')
                    
            except Exception as e:
                print(f'   ❌ {source} failed: {str(e)}')
        
        # Test multi-source discovery
        print(f'\n🔄 Testing Multi-Source Discovery...')
        try:
            result = await discovery.discover_tokens(
                sources=['dexscreener', 'coingecko'], 
                min_age_hours=24, 
                limit=10
            )
            
            tokens = result.get('tokens', [])
            source_results = result.get('source_results', {})
            
            print(f'   ✅ Multi-source: Found {len(tokens)} unique tokens')
            print(f'   📊 Sources used: {list(source_results.keys())}')
            
            for source, data in source_results.items():
                source_tokens = data.get('tokens', [])
                print(f'   - {source}: {len(source_tokens)} tokens')
                
        except Exception as e:
            print(f'   ❌ Multi-source discovery failed: {str(e)}')
            
    finally:
        await discovery.shutdown()
        
    print('\n✅ Real API Discovery Test Complete')

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_real_discovery())
