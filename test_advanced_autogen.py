#!/usr/bin/env python3
"""
Tests for advanced AutoGen patterns.
"""

import asyncio
import sys
import time
from pathlib import Path
from unittest.mock import MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Mock problematic imports
sys.modules['duckdb'] = MagicMock()

from src.agents.advanced_autogen import (
    AgentState,
    MessageType,
    EventType,
    AgentMessage,
    AgentEvent,
    AgentSnapshot,
    EventStore,
    MessageBus,
    AdvancedAgent,
    AgentCoordinator,
    TokenAnalysisAgent,
    event_store,
    message_bus,
    agent_coordinator,
    get_autogen_status
)


def test_agent_message():
    """Test agent message functionality."""
    print("🧪 Testing AgentMessage...")
    
    message = AgentMessage(
        sender_id="agent1",
        receiver_id="agent2",
        message_type=MessageType.TASK_REQUEST,
        content={"task": "analyze_token", "token_address": "0x123"},
        ttl_seconds=300
    )
    
    # Test serialization
    message_dict = message.to_dict()
    
    assert message_dict["sender_id"] == "agent1"
    assert message_dict["receiver_id"] == "agent2"
    assert message_dict["message_type"] == "task_request"
    assert message_dict["content"]["task"] == "analyze_token"
    assert message_dict["ttl_seconds"] == 300
    
    # Test expiration
    assert not message.is_expired()
    
    # Test expired message
    expired_message = AgentMessage(ttl_seconds=0)
    time.sleep(0.1)
    assert expired_message.is_expired()
    
    print("✅ AgentMessage tests passed")
    return True


def test_agent_event():
    """Test agent event functionality."""
    print("🧪 Testing AgentEvent...")
    
    event = AgentEvent(
        event_type=EventType.AGENT_CREATED,
        agent_id="test_agent",
        data={"initial_state": "idle"},
        version=1
    )
    
    # Test serialization
    event_dict = event.to_dict()
    
    assert event_dict["event_type"] == "agent_created"
    assert event_dict["agent_id"] == "test_agent"
    assert event_dict["data"]["initial_state"] == "idle"
    assert event_dict["version"] == 1
    
    print("✅ AgentEvent tests passed")
    return True


def test_agent_snapshot():
    """Test agent snapshot functionality."""
    print("🧪 Testing AgentSnapshot...")
    
    snapshot = AgentSnapshot(
        agent_id="test_agent",
        state=AgentState.ACTIVE,
        properties={"capability": "analysis"},
        last_event_id="event_123",
        version=5
    )
    
    # Test serialization
    snapshot_dict = snapshot.to_dict()
    
    assert snapshot_dict["agent_id"] == "test_agent"
    assert snapshot_dict["state"] == "active"
    assert snapshot_dict["properties"]["capability"] == "analysis"
    assert snapshot_dict["version"] == 5
    
    print("✅ AgentSnapshot tests passed")
    return True


def test_event_store():
    """Test event store functionality."""
    print("🧪 Testing EventStore...")
    
    store = EventStore()
    
    # Test event handler registration
    handled_events = []
    
    def test_handler(event):
        handled_events.append(event)
    
    store.register_event_handler(EventType.AGENT_CREATED, test_handler)
    
    # Test event appending
    event = AgentEvent(
        event_type=EventType.AGENT_CREATED,
        agent_id="test_agent",
        data={"test": "data"}
    )
    
    store.append_event(event)
    
    # Check that handler was called
    assert len(handled_events) == 1
    assert handled_events[0].agent_id == "test_agent"
    
    # Test event retrieval
    events = store.get_events("test_agent")
    assert len(events) == 1
    assert events[0].agent_id == "test_agent"
    
    # Test snapshot functionality
    snapshot = AgentSnapshot(
        agent_id="test_agent",
        state=AgentState.ACTIVE,
        properties={},
        last_event_id="event_1",
        version=1
    )
    
    store.save_snapshot(snapshot)
    retrieved_snapshot = store.get_snapshot("test_agent")
    
    assert retrieved_snapshot is not None
    assert retrieved_snapshot.agent_id == "test_agent"
    assert retrieved_snapshot.state == AgentState.ACTIVE
    
    print("✅ EventStore tests passed")
    return True


async def test_message_bus():
    """Test message bus functionality."""
    print("🧪 Testing MessageBus...")
    
    bus = MessageBus()
    
    # Test message sending and receiving
    message = AgentMessage(
        sender_id="sender",
        receiver_id="receiver",
        message_type=MessageType.TASK_REQUEST,
        content={"test": "message"}
    )
    
    # Send message
    success = await bus.send_message(message)
    assert success == True
    
    # Receive messages
    messages = await bus.receive_messages("receiver")
    assert len(messages) == 1
    assert messages[0].sender_id == "sender"
    assert messages[0].content["test"] == "message"
    
    # Test subscription
    bus.subscribe("agent1", "topic1")
    
    # Test queue status
    status = bus.get_queue_status("receiver")
    assert status["agent_id"] == "receiver"
    assert status["queue_size"] >= 0
    
    print("✅ MessageBus tests passed")
    return True


async def test_token_analysis_agent():
    """Test token analysis agent."""
    print("🧪 Testing TokenAnalysisAgent...")
    
    store = EventStore()
    bus = MessageBus()
    
    agent = TokenAnalysisAgent("token_agent_1", store, bus)
    
    # Test initialization
    assert agent.agent_id == "token_agent_1"
    assert agent.state == AgentState.IDLE
    assert "token_analysis" in agent.properties["capabilities"]
    
    # Test task processing
    task = {
        "id": "task_1",
        "type": "token_analysis",
        "token_address": "0x123456"
    }
    
    result = await agent.process_task(task)
    
    assert "token_address" in result
    assert result["token_address"] == "0x123456"
    assert "analysis_result" in result
    
    # Test risk assessment task
    risk_task = {
        "id": "task_2",
        "type": "risk_assessment",
        "token_address": "0x789abc"
    }
    
    risk_result = await agent.process_task(risk_task)
    
    assert "risk_level" in risk_result
    assert "confidence" in risk_result
    
    # Test message handling
    task_message = AgentMessage(
        sender_id="coordinator",
        receiver_id=agent.agent_id,
        message_type=MessageType.TASK_REQUEST,
        content=task
    )
    
    response = await agent.handle_message(task_message)
    assert response is not None
    assert response.message_type == MessageType.TASK_RESPONSE
    
    # Test status
    status = agent.get_status()
    assert status["agent_id"] == "token_agent_1"
    assert "state" in status
    assert "properties" in status
    
    print("✅ TokenAnalysisAgent tests passed")
    return True


async def test_agent_coordinator():
    """Test agent coordinator functionality."""
    print("🧪 Testing AgentCoordinator...")
    
    store = EventStore()
    bus = MessageBus()
    coordinator = AgentCoordinator(store, bus)
    
    # Create test agents
    agent1 = TokenAnalysisAgent("agent_1", store, bus)
    agent2 = TokenAnalysisAgent("agent_2", store, bus)
    
    # Register agents
    coordinator.register_agent(agent1)
    coordinator.register_agent(agent2)
    
    # Test task assignment
    task = {
        "id": "coord_task_1",
        "type": "token_analysis",
        "token_address": "0xcoordinated"
    }
    
    task_id = await coordinator.assign_task(task, strategy="round_robin")
    assert task_id is not None
    
    # Check that task was assigned
    status = coordinator.get_coordination_status()
    assert status["total_agents"] == 2
    assert status["active_tasks"] >= 0
    
    # Test broadcast
    await coordinator.broadcast_message(
        MessageType.STATUS_UPDATE,
        {"broadcast": "test"}
    )
    
    # Test agent unregistration
    coordinator.unregister_agent("agent_1")
    
    status = coordinator.get_coordination_status()
    assert status["total_agents"] == 1
    
    print("✅ AgentCoordinator tests passed")
    return True


async def test_agent_state_management():
    """Test agent state management and event sourcing."""
    print("🧪 Testing agent state management...")
    
    store = EventStore()
    bus = MessageBus()
    
    agent = TokenAnalysisAgent("state_agent", store, bus)
    
    # Test state changes
    initial_state = agent.state
    assert initial_state == AgentState.IDLE
    
    agent.change_state(AgentState.ACTIVE, "Starting work")
    assert agent.state == AgentState.ACTIVE
    
    agent.change_state(AgentState.BUSY, "Processing task")
    assert agent.state == AgentState.BUSY
    
    # Test property setting
    agent.set_property("current_task", "analysis_123")
    assert agent.properties["current_task"] == "analysis_123"
    
    # Test snapshot creation
    snapshot = agent.create_snapshot()
    assert snapshot.agent_id == "state_agent"
    assert snapshot.state == AgentState.BUSY
    assert snapshot.properties["current_task"] == "analysis_123"
    
    # Test restoration from snapshot
    agent.change_state(AgentState.ERROR, "Test error")
    agent.restore_from_snapshot(snapshot)
    
    assert agent.state == AgentState.BUSY
    assert agent.properties["current_task"] == "analysis_123"
    
    # Test event history
    events = store.get_events("state_agent")
    assert len(events) > 0
    
    # Should have creation, state changes, and property changes
    event_types = [event.event_type for event in events]
    assert EventType.AGENT_CREATED in event_types
    assert EventType.AGENT_STATE_CHANGED in event_types
    
    print("✅ Agent state management tests passed")
    return True


async def test_message_expiration():
    """Test message expiration functionality."""
    print("🧪 Testing message expiration...")
    
    bus = MessageBus()
    
    # Create message with short TTL
    message = AgentMessage(
        sender_id="sender",
        receiver_id="receiver",
        message_type=MessageType.TASK_REQUEST,
        content={"test": "expiring"},
        ttl_seconds=1
    )
    
    # Send message
    await bus.send_message(message)
    
    # Wait for expiration
    await asyncio.sleep(1.1)
    
    # Try to receive - should get no messages due to expiration
    messages = await bus.receive_messages("receiver")
    
    # Message should be filtered out due to expiration
    assert len(messages) == 0
    
    print("✅ Message expiration tests passed")
    return True


async def test_coordination_strategies():
    """Test different coordination strategies."""
    print("🧪 Testing coordination strategies...")
    
    store = EventStore()
    bus = MessageBus()
    coordinator = AgentCoordinator(store, bus)
    
    # Create agents with different capabilities
    agent1 = TokenAnalysisAgent("strategy_agent_1", store, bus)
    agent1.set_property("capabilities", ["token_analysis"])
    
    agent2 = TokenAnalysisAgent("strategy_agent_2", store, bus)
    agent2.set_property("capabilities", ["risk_assessment"])
    
    coordinator.register_agent(agent1)
    coordinator.register_agent(agent2)
    
    # Test round-robin strategy
    task1 = {"id": "rr_task_1", "type": "general"}
    task_id1 = await coordinator.assign_task(task1, "round_robin")
    assert task_id1 is not None
    
    # Test least-loaded strategy
    task2 = {"id": "ll_task_2", "type": "general"}
    task_id2 = await coordinator.assign_task(task2, "least_loaded")
    assert task_id2 is not None
    
    # Test capability-based strategy
    task3 = {"id": "cb_task_3", "type": "token_analysis"}
    task_id3 = await coordinator.assign_task(task3, "capability_based")
    assert task_id3 is not None
    
    print("✅ Coordination strategies tests passed")
    return True


def test_global_instances():
    """Test global AutoGen instances."""
    print("🧪 Testing global instances...")
    
    # Test global instances exist
    assert event_store is not None
    assert message_bus is not None
    assert agent_coordinator is not None
    
    # Test status function
    status = get_autogen_status()
    
    assert "event_store" in status
    assert "message_bus" in status
    assert "coordination" in status
    assert "timestamp" in status
    
    # Test event store status
    event_status = status["event_store"]
    assert "total_events" in event_status
    assert "total_snapshots" in event_status
    
    # Test message bus status
    bus_status = status["message_bus"]
    assert "total_queues" in bus_status
    assert "total_subscribers" in bus_status
    
    print("✅ Global instances tests passed")
    return True


async def main():
    """Run all advanced AutoGen tests."""
    print("🚀 Starting Advanced AutoGen Pattern Tests")
    print("=" * 60)
    
    test_results = {}
    
    try:
        # Run all tests
        tests = [
            ("agent_message", test_agent_message),
            ("agent_event", test_agent_event),
            ("agent_snapshot", test_agent_snapshot),
            ("event_store", test_event_store),
            ("message_bus", test_message_bus),
            ("token_analysis_agent", test_token_analysis_agent),
            ("agent_coordinator", test_agent_coordinator),
            ("agent_state_management", test_agent_state_management),
            ("message_expiration", test_message_expiration),
            ("coordination_strategies", test_coordination_strategies),
            ("global_instances", test_global_instances),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Running test: {test_name}")
            
            try:
                start_time = time.time()
                
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                duration = time.time() - start_time
                
                test_results[test_name] = {
                    "status": "passed" if result else "failed",
                    "duration": duration
                }
                
            except Exception as e:
                test_results[test_name] = {
                    "status": "error",
                    "error": str(e)
                }
                print(f"❌ Test failed: {test_name} - {e}")
        
        # Calculate results
        passed_tests = sum(1 for result in test_results.values() if result["status"] == "passed")
        total_tests = len(test_results)
        success_rate = passed_tests / total_tests
        
        print("\n" + "=" * 60)
        print("🎉 Advanced AutoGen Pattern Tests Completed!")
        print(f"📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
        
        if success_rate >= 0.9:
            print("🏆 AutoGen pattern tests meet high standards - System ready!")
            return 0
        else:
            print("⚠️ Some AutoGen tests failed - Review before deployment")
            return 1
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
