#!/usr/bin/env python3
"""
Comprehensive Integration Testing - Production Quality
End-to-end integration tests with actual API calls and data validation
"""

import asyncio
import sys
import time
import os
from datetime import datetime
from typing import Dict, Any, List
sys.path.append('src')

async def test_integration_comprehensive():
    """Run comprehensive integration tests with real APIs."""
    print("🔗 COMPREHENSIVE INTEGRATION TESTING")
    print("=" * 60)
    
    # Test scenarios for integration testing
    test_scenarios = [
        {
            "name": "Real API Integration - Honeypot Detection",
            "description": "Test honeypot detection with real APIs (Honeypot.is + GoPlus)",
            "test_method": "real_honeypot_detection"
        },
        {
            "name": "Real API Integration - Token Discovery",
            "description": "Test token discovery with real APIs (DexScreener + Birdeye)",
            "test_method": "real_token_discovery"
        },
        {
            "name": "Error Handling Integration",
            "description": "Test error handling with circuit breakers and retries",
            "test_method": "error_handling_integration"
        },
        {
            "name": "Rate Limiting Integration",
            "description": "Test rate limiting across multiple API calls",
            "test_method": "rate_limiting_integration"
        },
        {
            "name": "Cache System Integration",
            "description": "Test caching behavior with real data",
            "test_method": "cache_integration"
        },
        {
            "name": "End-to-End Token Analysis",
            "description": "Complete token analysis pipeline with real data",
            "test_method": "end_to_end_analysis"
        }
    ]
    
    print(f"\n🧪 Running {len(test_scenarios)} integration test scenarios...")
    print("-" * 60)
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. Testing {scenario['name']}")
        print(f"   Description: {scenario['description']}")
        
        start_time = datetime.now()
        
        try:
            if scenario['test_method'] == 'real_honeypot_detection':
                # Test real honeypot detection APIs
                print("   🍯 Testing real honeypot detection APIs...")
                
                from src.agents.scam_detector import AdvancedScamDetector
                from src.core.cache import CacheManager
                
                cache_manager = CacheManager()
                await cache_manager.initialize()
                
                scam_detector = AdvancedScamDetector(cache_manager)
                await scam_detector.initialize()
                
                # Test with known tokens
                test_tokens = [
                    ("******************************************", 1, "USDC"),  # Legitimate
                    ("******************************************", 1, "WETH"),  # Legitimate
                ]
                
                api_results = []
                for token_addr, chain_id, symbol in test_tokens:
                    try:
                        result = await scam_detector._detect_honeypot(token_addr, chain_id)
                        api_results.append({
                            "token": symbol,
                            "address": token_addr,
                            "result": result,
                            "success": True
                        })
                        print(f"      ✅ {symbol}: API calls completed")
                    except Exception as e:
                        api_results.append({
                            "token": symbol,
                            "address": token_addr,
                            "error": str(e),
                            "success": False
                        })
                        print(f"      ⚠️  {symbol}: {type(e).__name__}")
                
                await scam_detector.shutdown()
                
                # Validate results
                successful_calls = sum(1 for r in api_results if r.get('success'))
                print(f"   📊 API Success Rate: {successful_calls}/{len(api_results)} ({successful_calls/len(api_results)*100:.1f}%)")
                
                status = "PASS" if successful_calls >= len(api_results) * 0.5 else "PARTIAL"
                
            elif scenario['test_method'] == 'real_token_discovery':
                # Test real token discovery APIs
                print("   🔍 Testing real token discovery APIs...")
                
                from src.agents.discovery import DiscoveryAgent
                from src.core.database import DatabaseManager
                from src.core.cache import CacheManager
                from src.integrations.metrics import MetricsCollector
                from src.agents.coordinator import AgentCoordinator
                
                # Initialize components
                db_manager = DatabaseManager()
                cache_manager = CacheManager()
                await cache_manager.initialize()
                metrics_collector = MetricsCollector()
                coordinator = AgentCoordinator(db_manager, cache_manager, metrics_collector)
                
                discovery_agent = DiscoveryAgent(db_manager, cache_manager, metrics_collector, coordinator)
                await discovery_agent.initialize()
                
                # Test discovery from multiple sources
                discovery_results = []
                sources = ['dexscreener', 'birdeye']
                
                for source in sources:
                    try:
                        if source == 'dexscreener':
                            result = await discovery_agent._discover_from_dexscreener(24, 5)
                        elif source == 'birdeye':
                            result = await discovery_agent._discover_from_birdeye(24, 5)
                        
                        discovery_results.append({
                            "source": source,
                            "tokens_found": len(result.get('tokens', [])),
                            "success": True
                        })
                        print(f"      ✅ {source}: Found {len(result.get('tokens', []))} tokens")
                    except Exception as e:
                        discovery_results.append({
                            "source": source,
                            "error": str(e),
                            "success": False
                        })
                        print(f"      ⚠️  {source}: {type(e).__name__}")
                
                await discovery_agent.shutdown()
                
                # Validate results
                successful_sources = sum(1 for r in discovery_results if r.get('success'))
                total_tokens = sum(r.get('tokens_found', 0) for r in discovery_results if r.get('success'))
                
                print(f"   📊 Discovery Success: {successful_sources}/{len(sources)} sources")
                print(f"   📊 Total Tokens Found: {total_tokens}")
                
                status = "PASS" if successful_sources >= 1 and total_tokens >= 0 else "PARTIAL"
                
            elif scenario['test_method'] == 'error_handling_integration':
                # Test error handling integration
                print("   🛡️  Testing error handling integration...")
                
                from src.utils.error_handling import get_fault_tolerant_executor
                
                executor = get_fault_tolerant_executor()
                
                # Test circuit breaker behavior
                failure_count = 0
                
                async def sometimes_fail():
                    nonlocal failure_count
                    failure_count += 1
                    if failure_count <= 2:
                        raise ConnectionError(f"Simulated failure {failure_count}")
                    return f"Success after {failure_count} attempts"
                
                try:
                    result = await executor.execute_with_fault_tolerance(
                        sometimes_fail, "integration_test"
                    )
                    print(f"      ✅ Error handling successful: {result}")
                    
                    # Check executor stats
                    stats = executor.get_stats()
                    print(f"      📊 Executor stats: {stats['total_executions']} executions, {stats['success_rate']:.1%} success rate")
                    
                    status = "PASS"
                except Exception as e:
                    print(f"      ⚠️  Error handling failed: {e}")
                    status = "PARTIAL"
                
            elif scenario['test_method'] == 'rate_limiting_integration':
                # Test rate limiting integration
                print("   ⚡ Testing rate limiting integration...")
                
                from src.utils.rate_limit import _global_rate_limiter
                
                # Test rate limiting across multiple APIs
                api_calls = []
                apis_to_test = ['honeypot_is', 'goplus', 'dexscreener']
                
                for api_name in apis_to_test:
                    call_start = time.time()
                    
                    # Make 3 calls to each API
                    for i in range(3):
                        await _global_rate_limiter.acquire(api_name)
                    
                    call_time = time.time() - call_start
                    api_calls.append({
                        "api": api_name,
                        "time": call_time,
                        "calls": 3
                    })
                    
                    print(f"      📊 {api_name}: 3 calls in {call_time:.2f}s")
                
                # Validate rate limiting is working
                rate_limited_apis = sum(1 for call in api_calls if call['time'] > 0.5)
                print(f"   📊 Rate Limited APIs: {rate_limited_apis}/{len(apis_to_test)}")
                
                status = "PASS" if rate_limited_apis >= 1 else "PARTIAL"
                
            elif scenario['test_method'] == 'cache_integration':
                # Test cache system integration
                print("   💾 Testing cache system integration...")
                
                from src.core.cache import CacheManager
                
                cache_manager = CacheManager()
                await cache_manager.initialize()
                
                # Test cache operations
                cache_operations = []
                
                # Test set/get
                await cache_manager.set("integration_test_key", {"test": "data"}, ttl=60)
                cached_data = await cache_manager.get("integration_test_key")
                
                cache_operations.append({
                    "operation": "set_get",
                    "success": cached_data is not None and cached_data.get("test") == "data"
                })
                
                # Test delete
                await cache_manager.delete("integration_test_key")
                deleted_data = await cache_manager.get("integration_test_key")
                
                cache_operations.append({
                    "operation": "delete",
                    "success": deleted_data is None
                })
                
                await cache_manager.shutdown()
                
                successful_ops = sum(1 for op in cache_operations if op.get('success'))
                print(f"   📊 Cache Operations: {successful_ops}/{len(cache_operations)} successful")
                
                status = "PASS" if successful_ops == len(cache_operations) else "PARTIAL"
                
            elif scenario['test_method'] == 'end_to_end_analysis':
                # Test end-to-end token analysis
                print("   🔄 Testing end-to-end token analysis...")
                
                from src.agents.scam_detector import AdvancedScamDetector
                from src.core.cache import CacheManager
                
                cache_manager = CacheManager()
                await cache_manager.initialize()
                
                scam_detector = AdvancedScamDetector(cache_manager)
                await scam_detector.initialize()
                
                # Test full analysis pipeline
                test_token = "******************************************"  # USDC
                
                try:
                    analysis_result = await scam_detector.analyze_token(test_token, 1)
                    
                    # Validate analysis result structure
                    has_risk_level = hasattr(analysis_result, 'risk_level')
                    has_confidence = hasattr(analysis_result, 'confidence')
                    has_red_flags = hasattr(analysis_result, 'red_flags')
                    
                    print(f"      ✅ Analysis completed for USDC")
                    print(f"      📊 Risk Level: {analysis_result.risk_level if has_risk_level else 'N/A'}")
                    print(f"      📊 Confidence: {analysis_result.confidence if has_confidence else 'N/A'}%")
                    print(f"      📊 Red Flags: {len(analysis_result.red_flags) if has_red_flags else 'N/A'}")
                    
                    status = "PASS" if has_risk_level and has_confidence else "PARTIAL"
                    
                except Exception as e:
                    print(f"      ⚠️  Analysis failed: {type(e).__name__}")
                    status = "PARTIAL"
                
                await scam_detector.shutdown()
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            print(f"      Execution time: {execution_time:.2f}s")
            print(f"   ✅ {status}: {scenario['name']} completed")
            
            results.append({
                "scenario": scenario['name'],
                "status": status,
                "execution_time": execution_time,
                "description": scenario['description']
            })
            
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            results.append({
                "scenario": scenario['name'],
                "status": "ERROR",
                "error": str(e)
            })
    
    # Summary Report
    print("\n" + "=" * 60)
    print("📊 INTEGRATION TESTING SUMMARY")
    print("-" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r.get('status') == 'PASS')
    partial_tests = sum(1 for r in results if r.get('status') == 'PARTIAL')
    failed_tests = sum(1 for r in results if r.get('status') == 'FAIL')
    error_tests = sum(1 for r in results if r.get('status') == 'ERROR')
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
    print(f"Partial: {partial_tests} ({partial_tests/total_tests*100:.1f}%)")
    print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
    print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
    
    # Performance Metrics
    successful_results = [r for r in results if 'execution_time' in r]
    if successful_results:
        avg_time = sum(r['execution_time'] for r in successful_results) / len(successful_results)
        max_time = max(r['execution_time'] for r in successful_results)
        min_time = min(r['execution_time'] for r in successful_results)
        
        print(f"\nPerformance Metrics:")
        print(f"Average Execution Time: {avg_time:.2f}s")
        print(f"Max Execution Time: {max_time:.2f}s")
        print(f"Min Execution Time: {min_time:.2f}s")
    
    # Final Assessment
    print("\n" + "=" * 60)
    print("🎯 FINAL ASSESSMENT:")
    print("-" * 60)
    
    success_rate = (passed_tests + partial_tests * 0.5) / total_tests if total_tests > 0 else 0
    
    if success_rate >= 0.8 and error_tests == 0:
        print("✅ INTEGRATION TESTING: EXCELLENT")
        print("   - Real API integrations working")
        print("   - Error handling and rate limiting functional")
        print("   - End-to-end analysis pipeline operational")
        print("   - Production-ready system validated")
    elif success_rate >= 0.6:
        print("⚠️  INTEGRATION TESTING: GOOD")
        print("   - Most integrations working correctly")
        print("   - Some API limitations or intermittent issues")
        print("   - Overall system functionality confirmed")
    else:
        print("❌ INTEGRATION TESTING: NEEDS IMPROVEMENT")
        print("   - Significant integration issues")
        print("   - API connectivity or configuration problems")
        print("   - Further development required")

if __name__ == "__main__":
    asyncio.run(test_integration_comprehensive())
