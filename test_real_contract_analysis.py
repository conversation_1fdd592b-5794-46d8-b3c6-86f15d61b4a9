#!/usr/bin/env python3
"""
Real Contract Security Analysis Test - Production Quality Validation
Tests the actual implementation with real APIs and security data
"""

import asyncio
import sys
import os
from datetime import datetime
sys.path.append('src')

async def test_real_contract_analysis():
    """Test real contract security analysis with actual APIs"""
    print("🔒 REAL CONTRACT SECURITY ANALYSIS TEST")
    print("=" * 60)
    
    try:
        from src.agents.scam_detector import AdvancedScamDetector
        from src.core.cache import CacheManager
        
        # Initialize components
        cache_manager = CacheManager()
        await cache_manager.initialize()
        
        scam_detector = AdvancedScamDetector(cache_manager)
        await scam_detector.initialize()
        
        print("✅ Components initialized successfully")
        
        # Test tokens with different security profiles
        test_tokens = [
            {
                "name": "USDC (High Security)",
                "address": "******************************************",
                "chain_id": 1,
                "expected_risk": "low"
            },
            {
                "name": "WETH (High Security)", 
                "address": "******************************************",
                "chain_id": 1,
                "expected_risk": "low"
            },
            {
                "name": "PEPE (Medium Security)",
                "address": "******************************************",
                "chain_id": 1,
                "expected_risk": "medium"
            }
        ]
        
        print(f"\n🧪 Testing {len(test_tokens)} tokens for contract security...")
        print("-" * 60)
        
        results = []
        
        for i, token in enumerate(test_tokens, 1):
            print(f"\n{i}. Analyzing {token['name']}")
            print(f"   Address: {token['address']}")
            print(f"   Chain: {token['chain_id']}")
            
            start_time = datetime.now()
            
            try:
                # Test contract pattern analysis
                print("   🔍 Analyzing contract patterns...")
                contract_analysis = await scam_detector._analyze_contract_patterns(
                    token['address'], token['chain_id']
                )
                
                if contract_analysis.get('error'):
                    print(f"      ❌ Analysis failed: {contract_analysis['error']}")
                    continue
                
                print("   📊 CONTRACT ANALYSIS RESULTS:")
                print(f"      Verified Source: {contract_analysis.get('verified_source', 'Unknown')}")
                print(f"      Proxy Contract: {contract_analysis.get('proxy_contract', 'Unknown')}")
                print(f"      Dangerous Functions: {contract_analysis.get('has_dangerous_functions', 'Unknown')}")
                print(f"      Hidden Mint: {contract_analysis.get('has_hidden_mint', 'Unknown')}")
                print(f"      Ownership Renounced: {contract_analysis.get('ownership_renounced', 'Unknown')}")
                print(f"      Creator Address: {contract_analysis.get('creator_address', 'Unknown')}")
                print(f"      Creator Balance: {contract_analysis.get('creator_balance', 0):,.0f}")
                print(f"      Creator Percent: {contract_analysis.get('creator_percent', 0):.2f}%")
                print(f"      Holder Count: {contract_analysis.get('holder_count', 0):,}")
                print(f"      Buy Tax: {contract_analysis.get('buy_tax', 0)}%")
                print(f"      Sell Tax: {contract_analysis.get('sell_tax', 0)}%")
                
                # Test risk evaluation
                print("   ⚖️  Evaluating contract risks...")
                risk_score, risk_flags = scam_detector._evaluate_contract_risk(contract_analysis)
                
                print(f"   📊 RISK EVALUATION:")
                print(f"      Risk Score: {risk_score:.1f}/100")
                print(f"      Risk Flags: {len(risk_flags)}")
                for flag in risk_flags:
                    print(f"        - {flag}")
                
                end_time = datetime.now()
                analysis_time = (end_time - start_time).total_seconds()
                
                # Determine risk level
                if risk_score >= 70:
                    risk_level = "high"
                elif risk_score >= 40:
                    risk_level = "medium"
                else:
                    risk_level = "low"
                
                print(f"      Risk Level: {risk_level.upper()}")
                print(f"      Analysis Time: {analysis_time:.2f}s")
                
                # Validate against expectations
                expected_risk = token['expected_risk']
                
                if risk_level == expected_risk:
                    print(f"   ✅ RISK ASSESSMENT MATCHES EXPECTATION: {risk_level.upper()}")
                    status = "PASS"
                elif (risk_level == "low" and expected_risk == "medium") or (risk_level == "medium" and expected_risk == "low"):
                    print(f"   ⚠️  RISK ASSESSMENT CLOSE: Expected {expected_risk.upper()}, Got {risk_level.upper()}")
                    status = "PARTIAL"
                else:
                    print(f"   ❌ RISK ASSESSMENT MISMATCH: Expected {expected_risk.upper()}, Got {risk_level.upper()}")
                    status = "FAIL"
                
                results.append({
                    "token": token['name'],
                    "address": token['address'],
                    "analysis_time": analysis_time,
                    "risk_score": risk_score,
                    "risk_level": risk_level,
                    "risk_flags": risk_flags,
                    "verified_source": contract_analysis.get('verified_source', False),
                    "proxy_contract": contract_analysis.get('proxy_contract', False),
                    "dangerous_functions": contract_analysis.get('has_dangerous_functions', False),
                    "hidden_mint": contract_analysis.get('has_hidden_mint', False),
                    "ownership_renounced": contract_analysis.get('ownership_renounced', False),
                    "holder_count": contract_analysis.get('holder_count', 0),
                    "creator_percent": contract_analysis.get('creator_percent', 0),
                    "buy_tax": contract_analysis.get('buy_tax', 0),
                    "sell_tax": contract_analysis.get('sell_tax', 0),
                    "status": status
                })
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results.append({
                    "token": token['name'],
                    "address": token['address'],
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # Summary Report
        print("\n" + "=" * 60)
        print("📊 REAL CONTRACT ANALYSIS SUMMARY")
        print("-" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('status') == 'PASS')
        partial_tests = sum(1 for r in results if r.get('status') == 'PARTIAL')
        failed_tests = sum(1 for r in results if r.get('status') == 'FAIL')
        error_tests = sum(1 for r in results if r.get('status') == 'ERROR')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Partial: {partial_tests} ({partial_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        
        # Security Metrics
        successful_results = [r for r in results if 'risk_score' in r]
        if successful_results:
            avg_risk = sum(r['risk_score'] for r in successful_results) / len(successful_results)
            max_risk = max(r['risk_score'] for r in successful_results)
            min_risk = min(r['risk_score'] for r in successful_results)
            
            print(f"\nSecurity Metrics:")
            print(f"Average Risk Score: {avg_risk:.1f}/100")
            print(f"Max Risk Score: {max_risk:.1f}/100")
            print(f"Min Risk Score: {min_risk:.1f}/100")
            
            # Security feature analysis
            verified_count = sum(1 for r in successful_results if r.get('verified_source', False))
            proxy_count = sum(1 for r in successful_results if r.get('proxy_contract', False))
            dangerous_count = sum(1 for r in successful_results if r.get('dangerous_functions', False))
            
            print(f"\nSecurity Features:")
            print(f"Verified Source Code: {verified_count}/{len(successful_results)} ({verified_count/len(successful_results)*100:.1f}%)")
            print(f"Proxy Contracts: {proxy_count}/{len(successful_results)} ({proxy_count/len(successful_results)*100:.1f}%)")
            print(f"Dangerous Functions: {dangerous_count}/{len(successful_results)} ({dangerous_count/len(successful_results)*100:.1f}%)")
        
        # Performance Metrics
        if successful_results:
            avg_time = sum(r['analysis_time'] for r in successful_results) / len(successful_results)
            max_time = max(r['analysis_time'] for r in successful_results)
            min_time = min(r['analysis_time'] for r in successful_results)
            
            print(f"\nPerformance Metrics:")
            print(f"Average Analysis Time: {avg_time:.2f}s")
            print(f"Max Analysis Time: {max_time:.2f}s")
            print(f"Min Analysis Time: {min_time:.2f}s")
            
            # Check if we meet the <30s requirement
            if max_time < 30:
                print("✅ Performance Requirement Met: <30s analysis time")
            else:
                print("❌ Performance Requirement Failed: >30s analysis time")
        
        # Final Assessment
        print("\n" + "=" * 60)
        print("🎯 FINAL ASSESSMENT:")
        print("-" * 60)
        
        success_rate = (passed_tests + partial_tests) / total_tests if total_tests > 0 else 0
        
        if success_rate >= 0.9 and error_tests == 0:
            print("✅ REAL CONTRACT ANALYSIS: FULLY FUNCTIONAL")
            print("   - High accuracy in risk assessment")
            print("   - Real security data integration working")
            print("   - Performance meets requirements")
            print("   - Mock implementations successfully replaced")
        elif success_rate >= 0.7:
            print("⚠️  REAL CONTRACT ANALYSIS: MOSTLY FUNCTIONAL")
            print("   - Good accuracy in risk assessment")
            print("   - Some edge cases may need refinement")
            print("   - Overall system is operational")
        else:
            print("❌ REAL CONTRACT ANALYSIS: NEEDS IMPROVEMENT")
            print("   - Significant accuracy issues detected")
            print("   - Risk assessment logic needs refinement")
            print("   - Further debugging required")
        
        await scam_detector.shutdown()
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_real_contract_analysis())
