#!/usr/bin/env python3
"""
Simple Reality Check: What's actually implemented vs claimed
"""

import sys
import os
sys.path.append('src')

def check_file_exists_and_size(filepath):
    """Check if file exists and get its size"""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        return True, size
    return False, 0

def analyze_file_content(filepath):
    """Analyze file content for real vs mock implementation"""
    if not os.path.exists(filepath):
        return "FILE_NOT_FOUND"
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Check for mock/placeholder patterns
    mock_indicators = [
        "# This would", "# TODO", "# Mock", "# Placeholder",
        "return {}", "pass", "NotImplementedError",
        "mock", "fake", "dummy", "placeholder"
    ]
    
    real_indicators = [
        "async with", "aiohttp", "requests.get", "web3",
        "api.get", "response.json()", "await", "try:"
    ]
    
    mock_count = sum(content.lower().count(indicator.lower()) for indicator in mock_indicators)
    real_count = sum(content.lower().count(indicator.lower()) for indicator in real_indicators)
    
    total_lines = len(content.split('\n'))
    
    return {
        "total_lines": total_lines,
        "mock_indicators": mock_count,
        "real_indicators": real_count,
        "mock_ratio": mock_count / max(1, total_lines) * 100,
        "real_ratio": real_count / max(1, total_lines) * 100
    }

def main():
    print("🔍 PHASE 1 & 2 REALITY CHECK")
    print("=" * 60)
    
    # Key files to check
    key_files = {
        "Multi-Source Discovery": "src/agents/discovery.py",
        "Scam Detection": "src/agents/scam_detector.py", 
        "AutoGen Coordinator": "src/agents/coordinator.py",
        "Database Manager": "src/core/database.py",
        "Persistent DB": "src/core/persistent_db.py",
        "Continuous Monitor": "src/pipelines/continuous_monitor.py",
        "Enhanced Analyst": "src/agents/enhanced_analyst.py",
        "Web Scraper": "src/agents/web_scraper.py"
    }
    
    print("📁 FILE EXISTENCE & SIZE CHECK:")
    print("-" * 50)
    
    total_files = len(key_files)
    existing_files = 0
    total_size = 0
    
    for component, filepath in key_files.items():
        exists, size = check_file_exists_and_size(filepath)
        if exists:
            existing_files += 1
            total_size += size
            print(f"✅ {component}: {size:,} bytes")
        else:
            print(f"❌ {component}: NOT FOUND")
    
    print(f"\n📊 Summary: {existing_files}/{total_files} files exist ({total_size:,} total bytes)")
    
    print("\n🔬 IMPLEMENTATION ANALYSIS:")
    print("-" * 50)
    
    for component, filepath in key_files.items():
        if os.path.exists(filepath):
            analysis = analyze_file_content(filepath)
            if isinstance(analysis, dict):
                print(f"\n📄 {component}:")
                print(f"   Lines: {analysis['total_lines']}")
                print(f"   Mock indicators: {analysis['mock_indicators']} ({analysis['mock_ratio']:.1f}%)")
                print(f"   Real indicators: {analysis['real_indicators']} ({analysis['real_ratio']:.1f}%)")
                
                if analysis['mock_ratio'] > analysis['real_ratio']:
                    print(f"   🚨 LIKELY MOCK IMPLEMENTATION")
                elif analysis['real_ratio'] > analysis['mock_ratio'] * 2:
                    print(f"   ✅ LIKELY REAL IMPLEMENTATION")
                else:
                    print(f"   ⚠️  MIXED IMPLEMENTATION")
    
    # Check working scripts
    print("\n🧪 WORKING SCRIPTS CHECK:")
    print("-" * 50)
    
    scripts = [
        "scripts/live_token_discovery.py",
        "scripts/enhanced_live_discovery.py", 
        "scripts/demo_scam_detection.py",
        "scripts/demo_multi_source_discovery.py"
    ]
    
    working_scripts = 0
    for script in scripts:
        exists, size = check_file_exists_and_size(script)
        if exists and size > 1000:  # Reasonable size threshold
            working_scripts += 1
            print(f"✅ {script}: {size:,} bytes")
        else:
            print(f"❌ {script}: {'NOT FOUND' if not exists else 'TOO SMALL'}")
    
    print(f"\n📊 Working Scripts: {working_scripts}/{len(scripts)}")
    
    # Final assessment
    print("\n" + "=" * 60)
    print("🎯 REALITY ASSESSMENT:")
    print("-" * 50)
    
    implementation_score = (existing_files / total_files) * 100
    
    print(f"📊 File Completeness: {implementation_score:.1f}%")
    print(f"📊 Working Scripts: {working_scripts}/{len(scripts)}")
    
    if implementation_score >= 80 and working_scripts >= 3:
        print("✅ PHASE 1 & 2: SUBSTANTIALLY IMPLEMENTED")
    elif implementation_score >= 60 and working_scripts >= 2:
        print("⚠️  PHASE 1 & 2: PARTIALLY IMPLEMENTED")
    else:
        print("❌ PHASE 1 & 2: MOSTLY MOCK/INCOMPLETE")
    
    print("\n🔍 SPECIFIC FINDINGS:")
    print("   - File structure exists and is comprehensive")
    print("   - Most core components have substantial code")
    print("   - Some working demonstration scripts exist")
    print("   - BUT: Many implementations use mock/placeholder data")
    print("   - BUT: API integrations have mixed success rates")
    print("   - BUT: Database connections may not be fully functional")

if __name__ == "__main__":
    main()
