#!/usr/bin/env python3
"""
Birdeye API Integration Test - Production Quality Validation
Tests the Birdeye API integration with real Solana token data
"""

import asyncio
import sys
import os
from datetime import datetime
sys.path.append('src')

async def test_birdeye_integration():
    """Test Birdeye API integration with real Solana data"""
    print("🐦 BIRDEYE API INTEGRATION TEST")
    print("=" * 60)
    
    try:
        from src.agents.discovery import DiscoveryAgent
        from src.core.database import DatabaseManager
        from src.core.cache import CacheManager
        from src.integrations.metrics import MetricsCollector
        from src.agents.coordinator import AgentCoordinator
        
        # Initialize components
        db_manager = DatabaseManager()
        cache_manager = CacheManager()
        await cache_manager.initialize()
        metrics_collector = MetricsCollector()
        coordinator = AgentCoordinator(db_manager, cache_manager, metrics_collector)
        
        discovery = DiscoveryAgent(db_manager, cache_manager, metrics_collector, coordinator)
        await discovery.initialize()
        
        print("✅ Components initialized successfully")
        
        # Test different Birdeye discovery scenarios
        test_scenarios = [
            {
                "name": "High Volume Solana Tokens",
                "min_age_hours": 24,
                "limit": 10,
                "expected_min_tokens": 5
            },
            {
                "name": "Recent Solana Tokens",
                "min_age_hours": 1,
                "limit": 5,
                "expected_min_tokens": 2
            },
            {
                "name": "Large Batch Discovery",
                "min_age_hours": 168,  # 1 week
                "limit": 20,
                "expected_min_tokens": 10
            }
        ]
        
        print(f"\n🧪 Testing {len(test_scenarios)} Birdeye scenarios...")
        print("-" * 60)
        
        results = []
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n{i}. Testing {scenario['name']}")
            print(f"   Min Age: {scenario['min_age_hours']} hours")
            print(f"   Limit: {scenario['limit']} tokens")
            
            start_time = datetime.now()
            
            try:
                # Test Birdeye discovery directly
                print("   🐦 Testing Birdeye discovery...")
                birdeye_result = await discovery._discover_from_birdeye(
                    scenario['min_age_hours'], scenario['limit']
                )
                
                tokens = birdeye_result.get('tokens', [])
                total_checked = birdeye_result.get('total_checked', 0)
                
                print(f"   📊 BIRDEYE RESULTS:")
                print(f"      Tokens Found: {len(tokens)}")
                print(f"      Total Tokens Checked: {total_checked}")
                print(f"      Source: {birdeye_result.get('source', 'unknown')}")
                
                if tokens:
                    # Analyze token quality
                    sample_token = tokens[0]
                    print(f"      Sample Token: {sample_token.get('symbol', 'N/A')} - {sample_token.get('name', 'N/A')}")
                    print(f"      Price: ${sample_token.get('price_usd', 0):.6f}")
                    print(f"      Volume 24h: ${sample_token.get('volume_24h_usd', 0):,.2f}")
                    print(f"      Market Cap: ${sample_token.get('market_cap_usd', 0):,.2f}")
                    print(f"      Liquidity: ${sample_token.get('liquidity_usd', 0):,.2f}")
                    print(f"      Chain: {sample_token.get('chain', 'N/A')}")
                    
                    # Quality metrics for Solana tokens
                    high_volume_tokens = sum(1 for t in tokens if t.get('volume_24h_usd', 0) > 50000)
                    high_liquidity_tokens = sum(1 for t in tokens if t.get('liquidity_usd', 0) > 100000)
                    solana_tokens = sum(1 for t in tokens if t.get('chain') == 'solana')
                    valid_addresses = sum(1 for t in tokens if t.get('address') and len(t.get('address', '')) > 30)
                    
                    print(f"      Quality Metrics:")
                    print(f"        High Volume (>$50K): {high_volume_tokens}/{len(tokens)} ({high_volume_tokens/len(tokens)*100:.1f}%)")
                    print(f"        High Liquidity (>$100K): {high_liquidity_tokens}/{len(tokens)} ({high_liquidity_tokens/len(tokens)*100:.1f}%)")
                    print(f"        Solana Tokens: {solana_tokens}/{len(tokens)} ({solana_tokens/len(tokens)*100:.1f}%)")
                    print(f"        Valid Addresses: {valid_addresses}/{len(tokens)} ({valid_addresses/len(tokens)*100:.1f}%)")
                
                end_time = datetime.now()
                discovery_time = (end_time - start_time).total_seconds()
                
                print(f"      Discovery Time: {discovery_time:.2f}s")
                
                # Validate against expectations
                expected_min = scenario['expected_min_tokens']
                
                if len(tokens) >= expected_min:
                    print(f"   ✅ SUCCESS: Found {len(tokens)} tokens (expected ≥{expected_min})")
                    status = "PASS"
                elif len(tokens) > 0:
                    print(f"   ⚠️  PARTIAL: Found {len(tokens)} tokens (expected ≥{expected_min})")
                    status = "PARTIAL"
                else:
                    print(f"   ❌ FAIL: Found {len(tokens)} tokens (expected ≥{expected_min})")
                    status = "FAIL"
                
                results.append({
                    "scenario": scenario['name'],
                    "tokens_found": len(tokens),
                    "total_checked": total_checked,
                    "discovery_time": discovery_time,
                    "expected_min": expected_min,
                    "status": status,
                    "high_volume_count": high_volume_tokens if tokens else 0,
                    "high_liquidity_count": high_liquidity_tokens if tokens else 0,
                    "solana_count": solana_tokens if tokens else 0,
                    "valid_address_count": valid_addresses if tokens else 0
                })
                
            except Exception as e:
                print(f"   ❌ Test failed: {e}")
                results.append({
                    "scenario": scenario['name'],
                    "status": "ERROR",
                    "error": str(e)
                })
        
        # Test integration with full discovery system
        print(f"\n🔄 Testing Full Discovery Integration...")
        try:
            full_result = await discovery.discover_tokens(
                sources=['birdeye'], 
                min_age_hours=24, 
                limit=10
            )
            
            integration_tokens = full_result.get('tokens', [])
            source_results = full_result.get('source_results', {})
            
            print(f"   ✅ Integration test: Found {len(integration_tokens)} tokens")
            print(f"   📊 Source results: {list(source_results.keys())}")
            
            if 'birdeye' in source_results:
                birdeye_data = source_results['birdeye']
                print(f"   🐦 Birdeye in integration: {len(birdeye_data.get('tokens', []))} tokens")
            
        except Exception as e:
            print(f"   ❌ Integration test failed: {e}")
        
        # Summary Report
        print("\n" + "=" * 60)
        print("📊 BIRDEYE INTEGRATION SUMMARY")
        print("-" * 60)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.get('status') == 'PASS')
        partial_tests = sum(1 for r in results if r.get('status') == 'PARTIAL')
        failed_tests = sum(1 for r in results if r.get('status') == 'FAIL')
        error_tests = sum(1 for r in results if r.get('status') == 'ERROR')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Partial: {partial_tests} ({partial_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        
        # Discovery Metrics
        successful_results = [r for r in results if 'tokens_found' in r]
        if successful_results:
            total_tokens = sum(r['tokens_found'] for r in successful_results)
            total_checked = sum(r['total_checked'] for r in successful_results)
            avg_discovery_time = sum(r['discovery_time'] for r in successful_results) / len(successful_results)
            
            print(f"\nDiscovery Metrics:")
            print(f"Total Solana Tokens Found: {total_tokens}")
            print(f"Total Tokens Checked: {total_checked}")
            print(f"Average Discovery Time: {avg_discovery_time:.2f}s")
            print(f"Discovery Efficiency: {total_tokens/max(1, total_checked)*100:.2f}% (found/checked)")
            
            # Solana-specific quality metrics
            total_high_volume = sum(r.get('high_volume_count', 0) for r in successful_results)
            total_high_liquidity = sum(r.get('high_liquidity_count', 0) for r in successful_results)
            total_solana = sum(r.get('solana_count', 0) for r in successful_results)
            total_valid_addresses = sum(r.get('valid_address_count', 0) for r in successful_results)
            
            print(f"\nSolana Quality Metrics:")
            print(f"High Volume Tokens: {total_high_volume}/{total_tokens} ({total_high_volume/max(1, total_tokens)*100:.1f}%)")
            print(f"High Liquidity Tokens: {total_high_liquidity}/{total_tokens} ({total_high_liquidity/max(1, total_tokens)*100:.1f}%)")
            print(f"Solana Chain Tokens: {total_solana}/{total_tokens} ({total_solana/max(1, total_tokens)*100:.1f}%)")
            print(f"Valid Addresses: {total_valid_addresses}/{total_tokens} ({total_valid_addresses/max(1, total_tokens)*100:.1f}%)")
        
        # Final Assessment
        print("\n" + "=" * 60)
        print("🎯 FINAL ASSESSMENT:")
        print("-" * 60)
        
        success_rate = (passed_tests + partial_tests) / total_tests if total_tests > 0 else 0
        
        if success_rate >= 0.8 and error_tests == 0 and total_tokens > 0:
            print("✅ BIRDEYE INTEGRATION: FULLY SUCCESSFUL")
            print("   - API key authentication working")
            print("   - Solana token discovery operational")
            print("   - Quality filtering effective")
            print("   - Integration with discovery system working")
        elif success_rate >= 0.6 and total_tokens > 0:
            print("⚠️  BIRDEYE INTEGRATION: MOSTLY SUCCESSFUL")
            print("   - Most scenarios working")
            print("   - Some edge cases may need attention")
            print("   - Overall Solana coverage improved")
        else:
            print("❌ BIRDEYE INTEGRATION: NEEDS MORE WORK")
            print("   - Significant issues with API integration")
            print("   - Authentication or endpoint problems")
            print("   - Further debugging required")
        
        await discovery.shutdown()
        
    except Exception as e:
        print(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_birdeye_integration())
