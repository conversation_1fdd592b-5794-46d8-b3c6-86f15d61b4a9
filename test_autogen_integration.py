#!/usr/bin/env python3
"""
Test AutoGen Integration Reality Check
"""

import sys
import os
sys.path.append('src')

def check_autogen_dependencies():
    """Check if AutoGen dependencies are properly installed"""
    print("🔍 CHECKING AUTOGEN DEPENDENCIES:")
    print("-" * 50)
    
    dependencies = [
        "autogen",
        "pyautogen", 
        "openai",
        "anthropic"
    ]
    
    installed = []
    missing = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            installed.append(dep)
            print(f"✅ {dep}: INSTALLED")
        except ImportError:
            missing.append(dep)
            print(f"❌ {dep}: MISSING")
    
    return installed, missing

def check_autogen_implementation():
    """Check the actual AutoGen implementation"""
    print("\n🔍 CHECKING AUTOGEN IMPLEMENTATION:")
    print("-" * 50)
    
    autogen_file = "src/agents/autogen_coordinator.py"
    
    if not os.path.exists(autogen_file):
        print("❌ AutoGen coordinator file not found")
        return False
    
    with open(autogen_file, 'r') as f:
        content = f.read()
    
    # Check for actual AutoGen imports and usage
    autogen_indicators = [
        "from autogen import",
        "import autogen",
        "AssistantAgent",
        "UserProxyAgent", 
        "GroupChat",
        "GroupChatManager",
        "a_initiate_chat"
    ]
    
    found_indicators = []
    missing_indicators = []
    
    for indicator in autogen_indicators:
        if indicator in content:
            found_indicators.append(indicator)
            print(f"✅ Found: {indicator}")
        else:
            missing_indicators.append(indicator)
            print(f"❌ Missing: {indicator}")
    
    print(f"\n📊 AutoGen Integration: {len(found_indicators)}/{len(autogen_indicators)} indicators found")
    
    return len(found_indicators) >= len(autogen_indicators) * 0.7

def test_autogen_import():
    """Test if we can actually import and use AutoGen components"""
    print("\n🧪 TESTING AUTOGEN IMPORT:")
    print("-" * 50)
    
    try:
        from src.agents.autogen_coordinator import AutoGenCoordinator
        print("✅ AutoGenCoordinator import successful")
        
        # Try to create instance (without initializing)
        coordinator = AutoGenCoordinator()
        print("✅ AutoGenCoordinator instantiation successful")
        
        # Check if it has expected methods
        expected_methods = [
            'initialize',
            'analyze_token',
            'shutdown'
        ]
        
        for method in expected_methods:
            if hasattr(coordinator, method):
                print(f"✅ Method {method}: EXISTS")
            else:
                print(f"❌ Method {method}: MISSING")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Instantiation failed: {e}")
        return False

def check_llm_configuration():
    """Check if LLM configuration is properly set up"""
    print("\n🔍 CHECKING LLM CONFIGURATION:")
    print("-" * 50)
    
    # Check environment variables
    llm_vars = [
        "OPENROUTER_API_KEY",
        "ANTHROPIC_API_KEY", 
        "OPENAI_API_KEY"
    ]
    
    configured_llms = []
    
    for var in llm_vars:
        if os.getenv(var):
            configured_llms.append(var)
            print(f"✅ {var}: CONFIGURED")
        else:
            print(f"❌ {var}: NOT SET")
    
    if configured_llms:
        print(f"\n📊 LLM Configuration: {len(configured_llms)}/{len(llm_vars)} providers configured")
        return True
    else:
        print("\n❌ No LLM providers configured")
        return False

def main():
    print("🤖 AUTOGEN INTEGRATION REALITY CHECK")
    print("=" * 60)
    
    # Check dependencies
    installed, missing = check_autogen_dependencies()
    
    # Check implementation
    implementation_ok = check_autogen_implementation()
    
    # Test import
    import_ok = test_autogen_import()
    
    # Check LLM config
    llm_ok = check_llm_configuration()
    
    # Final assessment
    print("\n" + "=" * 60)
    print("🎯 AUTOGEN INTEGRATION ASSESSMENT:")
    print("-" * 50)
    
    total_checks = 4
    passed_checks = sum([
        len(installed) > 0,
        implementation_ok,
        import_ok,
        llm_ok
    ])
    
    print(f"📊 Overall Score: {passed_checks}/{total_checks} checks passed")
    
    if passed_checks >= 3:
        print("✅ AUTOGEN INTEGRATION: FUNCTIONAL")
        print("   - Core dependencies are available")
        print("   - Implementation structure exists")
        print("   - Components can be imported")
        print("   - LLM configuration is set up")
    elif passed_checks >= 2:
        print("⚠️  AUTOGEN INTEGRATION: PARTIAL")
        print("   - Some components are working")
        print("   - May require additional setup")
        print("   - Missing dependencies or configuration")
    else:
        print("❌ AUTOGEN INTEGRATION: NON-FUNCTIONAL")
        print("   - Critical dependencies missing")
        print("   - Implementation incomplete")
        print("   - Cannot be used in current state")
    
    print("\n🔍 SPECIFIC FINDINGS:")
    if missing:
        print(f"   - Missing dependencies: {', '.join(missing)}")
    if not implementation_ok:
        print("   - AutoGen implementation incomplete")
    if not import_ok:
        print("   - Cannot import AutoGen coordinator")
    if not llm_ok:
        print("   - LLM providers not configured")
    
    print("\n💡 RECOMMENDATIONS:")
    if missing:
        print("   - Install missing dependencies: pip install " + " ".join(missing))
    if not llm_ok:
        print("   - Configure at least one LLM provider API key")
    if not implementation_ok:
        print("   - Complete AutoGen GroupChat implementation")

if __name__ == "__main__":
    main()
